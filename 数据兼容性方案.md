# 数据兼容性方案

## 1. 现状分析

### 1.1 现有数据表结构
**lottery_config表**（生产环境中有2条数据）：
- 字段映射到新的prize_pool表
- 需要保持向后兼容性
- 现有RemoteLotteryService接口正在使用

**activity_lottery_item表**：
- 字段映射到新的draw_history表
- 包含历史抽奖记录
- 需要数据迁移策略

### 1.2 兼容性挑战
1. **业务连续性**：现有抽奖功能不能中断
2. **数据一致性**：新老数据结构需要同步
3. **接口兼容性**：RemoteLotteryService接口需要继续工作
4. **渐进式升级**：新功能逐步上线

## 2. 兼容性策略

### 2.1 双写策略
在过渡期间，同时写入新老两套数据表：

```java
@Service
public class LotteryDataCompatibilityService {

    @Resource
    private LotteryConfigBuilder lotteryConfigBuilder; // 现有

    @Resource
    private PrizePoolBuilder prizePoolBuilder; // 新增

    /**
     * 兼容性写入：同时更新新老表结构
     */
    public void saveLotteryConfigWithCompatibility(LotteryConfig oldConfig) {
        // 1. 保存到现有表
        lotteryConfigBuilder.insert(oldConfig);

        // 2. 转换并保存到新表
        PrizePool newPool = convertToPrizePool(oldConfig);
        prizePoolBuilder.createOrUpdate(newPool);
    }

    /**
     * 数据结构转换：LotteryConfig -> PrizePool
     */
    private PrizePool convertToPrizePool(LotteryConfig oldConfig) {
        PrizePool prizePool = new PrizePool();
        prizePool.setId(oldConfig.getId());
        prizePool.setCode(oldConfig.getCode());
        prizePool.setName(oldConfig.getType()); // 临时映射

        // 转换兑换规则
        if (oldConfig.getCurrency() != null && oldConfig.getAmount() != null) {
            String exchangeRules = String.format(
                "[{\"assetType\": \"%s\", \"cost\": %d}]",
                oldConfig.getCurrency(), oldConfig.getAmount()
            );
            prizePool.setExchangeRules(exchangeRules);
        }

        // 设置默认概率策略
        prizePool.setProbabilityStrategy("OVERALL");

        // 转换限制次数
        prizePool.setDailyLimit(oldConfig.getLimitTimes());
        if ("weekly".equals(oldConfig.getLimitUnit())) {
            prizePool.setWeeklyLimit(oldConfig.getLimitTimes());
            prizePool.setDailyLimit(-1); // 无限制
        }
        prizePool.setMonthlyLimit(-1); // 默认无限制

        // 转换时间
        prizePool.setStartTime(oldConfig.getStartTime());
        prizePool.setEndTime(oldConfig.getEndTime());
        prizePool.setStatus(oldConfig.getStatus());
        prizePool.setSaasId(oldConfig.getSaasId());

        // 设置时间戳
        long currentTime = System.currentTimeMillis();
        prizePool.setCreateTime(currentTime);
        prizePool.setUpdateTime(currentTime);

        return prizePool;
    }
}
```

### 2.2 读取策略
优先从新表读取，如果没有则从老表读取：

```java
@Service
public class LotteryConfigCompatibilityService {

    /**
     * 兼容性读取：优先新表，回退老表
     */
    public PrizePool findPrizePoolByCode(String code) {
        // 1. 先尝试从新表读取
        PrizePool prizePool = prizePoolBuilder.findByCode(code);
        if (prizePool != null) {
            return prizePool;
        }

        // 2. 从老表读取并转换
        LotteryConfig oldConfig = lotteryConfigBuilder.findByCode(code);
        if (oldConfig != null) {
            return convertToPrizePool(oldConfig);
        }

        return null;
    }
}
```

## 3. 数据迁移方案

### 3.1 迁移脚本设计
```java
@Component
public class DataMigrationService {

    /**
     * 迁移lottery_config到prize_pool
     */
    public void migrateLotteryConfigToPrizePool() {
        log.info("开始迁移lottery_config数据到prize_pool表");

        // 1. 查询所有现有配置
        List<LotteryConfig> oldConfigs = lotteryConfigBuilder.findAll();

        for (LotteryConfig oldConfig : oldConfigs) {
            try {
                // 2. 检查新表是否已存在
                PrizePool existing = prizePoolBuilder.findByCode(oldConfig.getCode());
                if (existing != null) {
                    log.info("奖池配置已存在，跳过: {}", oldConfig.getCode());
                    continue;
                }

                // 3. 转换并保存
                PrizePool newPool = convertToPrizePool(oldConfig);
                prizePoolBuilder.createOrUpdate(newPool);

                // 4. 创建默认奖品配置
                createDefaultPrizeConfig(oldConfig, newPool);

                log.info("成功迁移奖池配置: {}", oldConfig.getCode());

            } catch (Exception e) {
                log.error("迁移奖池配置失败: {}", oldConfig.getCode(), e);
            }
        }

        log.info("lottery_config数据迁移完成");
    }

    /**
     * 为迁移的奖池创建默认奖品配置
     */
    private void createDefaultPrizeConfig(LotteryConfig oldConfig, PrizePool prizePool) {
        // 解析现有的awards配置
        if (StringUtils.isNotBlank(oldConfig.getAwards())) {
            try {
                // 假设awards是JSON格式的奖品配置
                List<Map<String, Object>> awards = JSON.parseArray(oldConfig.getAwards(), Map.class);

                for (Map<String, Object> award : awards) {
                    PrizeConfig prizeConfig = new PrizeConfig();
                    prizeConfig.setId(IdUtil.objectId());
                    prizeConfig.setPrizePoolCode(prizePool.getCode());
                    prizeConfig.setPrizeName((String) award.get("name"));
                    prizeConfig.setPrizeType("CURRENCY"); // 默认类型
                    prizeConfig.setPrizeItemId((String) award.get("currency"));
                    prizeConfig.setQuantityPerWin(Integer.valueOf(award.get("amount").toString()));
                    prizeConfig.setWinningProbability(new BigDecimal(award.get("probability").toString()));
                    prizeConfig.setStockQuantity(-1); // 无限库存
                    prizeConfig.setIsActive(true);
                    prizeConfig.setSaasId(oldConfig.getSaasId());

                    long currentTime = System.currentTimeMillis();
                    prizeConfig.setCreateTime(currentTime);
                    prizeConfig.setUpdateTime(currentTime);

                    prizeConfigBuilder.createOrUpdate(prizeConfig);
                }
            } catch (Exception e) {
                log.error("解析奖品配置失败: {}", oldConfig.getCode(), e);
            }
        }
    }
}
```

## 4. 服务兼容性方案

### 4.1 现有服务保持不变
RemoteLotteryService继续使用现有逻辑，确保生产环境稳定：

```java
@DubboService
public class RemoteLotteryServiceImpl implements RemoteLotteryService {

    // 保持现有实现不变
    @Override
    public LotteryResponse lottery(String customerId, String code, String saasId) throws Exception {
        return lotteryCommonService.lottery(customerId, code, saasId);
    }

    // 其他方法保持不变...
}
```

### 4.2 新服务独立实现
RewardPlatformService使用新的数据结构和业务逻辑：

```java
@DubboService
public class RewardPlatformServiceImpl implements RewardPlatformService {

    @Resource
    private PrizePoolBuilder prizePoolBuilder;

    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;

    @Override
    public DrawBatchResponse drawBatch(DrawBatchRequest request) {
        // 使用新的数据结构和业务逻辑
        PrizePool prizePool = prizePoolBuilder.findByCode(request.getPrizePoolCode());
        // ... 新的抽奖逻辑
    }
}
```

## 5. 配置管理方案

### 5.1 功能开关
使用配置开关控制新老功能的启用：

```java
@Component
@ConfigurationProperties(prefix = "reward.platform")
public class RewardPlatformConfig {

    /**
     * 是否启用新的奖励中台功能
     */
    private boolean enableNewPlatform = false;

    /**
     * 是否启用数据双写
     */
    private boolean enableDualWrite = true;

    /**
     * 是否优先读取新表数据
     */
    private boolean preferNewData = false;

    /**
     * 灰度发布百分比
     */
    private int grayReleasePercentage = 0;
}
```

### 5.2 灰度发布策略
```java
@Service
public class FeatureToggleService {

    @Resource
    private RewardPlatformConfig rewardPlatformConfig;

    /**
     * 判断用户是否使用新功能
     */
    public boolean shouldUseNewPlatform(String userId, String saasId) {
        // 基于SaaS ID进行灰度
        if ("test_saas".equals(saasId)) {
            return true; // 测试环境优先使用新功能
        }

        // 基于用户ID哈希进行灰度
        int hash = Math.abs(userId.hashCode()) % 100;
        return hash < rewardPlatformConfig.getGrayReleasePercentage();
    }
}
```

## 6. 监控和告警

### 6.1 数据一致性监控
```java
@Component
public class DataConsistencyMonitor {

    @Scheduled(fixedRate = 300000) // 5分钟检查一次
    public void checkDataConsistency() {
        try {
            // 检查新老数据的一致性
            List<String> inconsistentCodes = findInconsistentData();
            if (!inconsistentCodes.isEmpty()) {
                // 发送告警
                alertService.sendAlert("数据一致性异常", inconsistentCodes);
                log.warn("发现数据不一致的奖池: {}", inconsistentCodes);
            }
        } catch (Exception e) {
            log.error("数据一致性检查失败", e);
        }
    }

    private List<String> findInconsistentData() {
        List<String> inconsistentCodes = new ArrayList<>();

        // 检查lottery_config和prize_pool的一致性
        List<LotteryConfig> oldConfigs = lotteryConfigBuilder.findAll();
        for (LotteryConfig oldConfig : oldConfigs) {
            PrizePool newPool = prizePoolBuilder.findByCode(oldConfig.getCode());
            if (newPool == null) {
                inconsistentCodes.add(oldConfig.getCode());
            } else if (!isDataConsistent(oldConfig, newPool)) {
                inconsistentCodes.add(oldConfig.getCode());
            }
        }

        return inconsistentCodes;
    }

    private boolean isDataConsistent(LotteryConfig oldConfig, PrizePool newPool) {
        // 检查关键字段是否一致
        return Objects.equals(oldConfig.getCode(), newPool.getCode()) &&
               Objects.equals(oldConfig.getStatus(), newPool.getStatus()) &&
               Objects.equals(oldConfig.getStartTime(), newPool.getStartTime()) &&
               Objects.equals(oldConfig.getEndTime(), newPool.getEndTime());
    }
}
```

### 6.2 性能监控
```java
@Component
public class PerformanceMonitor {

    private final MeterRegistry meterRegistry;

    public PerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    /**
     * 监控接口响应时间
     */
    public void recordApiResponseTime(String apiName, long responseTime) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("api.response.time")
                .tag("api", apiName)
                .register(meterRegistry));
    }

    /**
     * 监控数据库查询性能
     */
    public void recordDbQueryTime(String tableName, String operation, long queryTime) {
        Timer.builder("db.query.time")
                .tag("table", tableName)
                .tag("operation", operation)
                .register(meterRegistry)
                .record(queryTime, TimeUnit.MILLISECONDS);
    }
}
```

## 7. 回滚方案

### 7.1 快速回滚
如果新功能出现问题，可以快速回滚到老功能：

```properties
# application.properties
# 通过配置快速关闭新功能
reward.platform.enable-new-platform=false
reward.platform.prefer-new-data=false
reward.platform.gray-release-percentage=0
```

### 7.2 数据回滚
如果需要数据回滚，提供反向迁移脚本：

```java
@Component
public class DataRollbackService {

    /**
     * 紧急回滚：将新表数据同步回老表
     * 仅在紧急情况下使用
     */
    public void rollbackPrizePoolToLotteryConfig() {
        log.warn("开始执行数据回滚操作");

        List<PrizePool> prizePools = prizePoolBuilder.findAll();
        for (PrizePool prizePool : prizePools) {
            try {
                // 检查老表是否存在对应记录
                LotteryConfig existing = lotteryConfigBuilder.findByCode(prizePool.getCode());
                if (existing != null) {
                    // 更新现有记录
                    updateLotteryConfigFromPrizePool(existing, prizePool);
                } else {
                    // 创建新记录
                    LotteryConfig newConfig = convertToLotteryConfig(prizePool);
                    lotteryConfigBuilder.insert(newConfig);
                }

                log.info("成功回滚奖池配置: {}", prizePool.getCode());

            } catch (Exception e) {
                log.error("回滚奖池配置失败: {}", prizePool.getCode(), e);
            }
        }

        log.warn("数据回滚操作完成");
    }

    private LotteryConfig convertToLotteryConfig(PrizePool prizePool) {
        LotteryConfig config = new LotteryConfig();
        config.setId(prizePool.getId());
        config.setCode(prizePool.getCode());
        config.setType(prizePool.getName());
        config.setStatus(prizePool.getStatus());
        config.setStartTime(prizePool.getStartTime());
        config.setEndTime(prizePool.getEndTime());
        config.setSaasId(prizePool.getSaasId());

        // 转换限制次数
        if (prizePool.getDailyLimit() != null && prizePool.getDailyLimit() > 0) {
            config.setLimitTimes(prizePool.getDailyLimit());
            config.setLimitUnit("daily");
        } else if (prizePool.getWeeklyLimit() != null && prizePool.getWeeklyLimit() > 0) {
            config.setLimitTimes(prizePool.getWeeklyLimit());
            config.setLimitUnit("weekly");
        }

        return config;
    }
}
```

## 8. 实施时间表

### 8.1 第1周：准备工作
- [x] 完成新数据表结构设计
- [x] 完成新服务接口设计
- [ ] 完成兼容性代码开发
- [ ] 编写数据迁移脚本
- [ ] 准备监控告警机制

### 8.2 第2周：测试验证
- [ ] 在测试环境部署新代码
- [ ] 执行数据迁移测试
- [ ] 验证数据一致性
- [ ] 测试新老功能兼容性
- [ ] 性能测试和优化

### 8.3 第3周：生产部署
- [ ] 部署兼容性代码到生产环境
- [ ] 执行生产数据迁移
- [ ] 开启数据双写模式
- [ ] 监控系统稳定性
- [ ] 验证现有功能正常

### 8.4 第4周：功能切换
- [ ] 小范围灰度启用新功能
- [ ] 监控新功能性能和稳定性
- [ ] 逐步扩大新功能覆盖范围
- [ ] 收集用户反馈
- [ ] 优化和调整

## 9. 风险控制措施

### 9.1 技术风险
- **数据丢失风险**：通过双写和备份机制防范
- **性能下降风险**：通过性能监控和优化应对
- **功能异常风险**：通过灰度发布和快速回滚应对

### 9.2 业务风险
- **用户体验影响**：通过充分测试和渐进式发布降低
- **数据不一致风险**：通过一致性监控和告警机制防范
- **服务中断风险**：通过保持现有服务不变来避免

### 9.3 应急预案
1. **发现严重问题**：立即关闭新功能，回退到老功能
2. **数据不一致**：启动数据修复流程，必要时执行数据回滚
3. **性能问题**：调整灰度比例，优化查询和缓存策略
4. **功能异常**：快速定位问题，发布热修复或回滚

## 10. 成功标准

### 10.1 技术指标
- 数据迁移成功率 > 99.9%
- 新老数据一致性 > 99.9%
- 接口响应时间增长 < 10%
- 系统可用性 > 99.95%

### 10.2 业务指标
- 现有抽奖功能零中断
- 用户投诉数量无显著增长
- 新功能正常可用
- 灰度用户反馈良好

通过这个详细的数据兼容性方案，我们可以确保在不影响现有业务的前提下，安全、平滑地完成奖励中台的升级改造。整个过程采用渐进式策略，最大程度降低风险，确保业务连续性。
```
```