package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.framework.ots.builder.BaseBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 礼包内容规则表数据访问层
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
public class GiftPackConfigBuilder extends BaseBuilder<GiftPackConfig> {

    /**
     * 根据礼包ID查询礼包配置规则
     * 
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    public List<GiftPackConfig> findByPackId(String packId, String saasId) {
        return searchByIndex(GiftPackConfig.SEARCH_GIFT_PACK_CONFIG)
                .addEqualCondition("pack_id", packId)
                .addEqualCondition("saas_id", saasId)
                .addEqualCondition("is_active", true)
                .orderBy("rule_order", true) // 按规则顺序排序
                .list();
    }

    /**
     * 根据礼包ID和规则类型查询配置
     * 
     * @param packId 礼包ID
     * @param ruleType 规则类型
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    public List<GiftPackConfig> findByPackIdAndRuleType(String packId, String ruleType, String saasId) {
        return searchByIndex(GiftPackConfig.SEARCH_GIFT_PACK_CONFIG)
                .addEqualCondition("pack_id", packId)
                .addEqualCondition("rule_type", ruleType)
                .addEqualCondition("saas_id", saasId)
                .addEqualCondition("is_active", true)
                .orderBy("rule_order", true)
                .list();
    }

    /**
     * 根据SaaS ID查询所有活跃的礼包配置
     * 
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    public List<GiftPackConfig> findActiveBySaasId(String saasId) {
        return searchByIndex(GiftPackConfig.SEARCH_GIFT_PACK_CONFIG)
                .addEqualCondition("saas_id", saasId)
                .addEqualCondition("is_active", true)
                .list();
    }
}
