package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;
import com.kikitrade.framework.ots.builder.BaseBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 随机奖励池表数据访问层
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
public class RandomRewardPoolBuilder extends BaseBuilder<RandomRewardPool> {

    /**
     * 根据随机池ID查询奖励池配置
     * 
     * @param poolId 随机池ID
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    public List<RandomRewardPool> findByPoolId(String poolId, String saasId) {
        return searchByIndex(RandomRewardPool.SEARCH_RANDOM_REWARD_POOL)
                .addEqualCondition("pool_id", poolId)
                .addEqualCondition("saas_id", saasId)
                .addEqualCondition("is_active", true)
                .list();
    }

    /**
     * 根据随机池ID和物品类型查询奖励池配置
     * 
     * @param poolId 随机池ID
     * @param itemType 物品类型
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    public List<RandomRewardPool> findByPoolIdAndItemType(String poolId, String itemType, String saasId) {
        return searchByIndex(RandomRewardPool.SEARCH_RANDOM_REWARD_POOL)
                .addEqualCondition("pool_id", poolId)
                .addEqualCondition("item_type", itemType)
                .addEqualCondition("saas_id", saasId)
                .addEqualCondition("is_active", true)
                .list();
    }

    /**
     * 根据SaaS ID查询所有活跃的奖励池配置
     * 
     * @param saasId SaaS ID
     * @return 奖励池配置列表
     */
    public List<RandomRewardPool> findActiveBySaasId(String saasId) {
        return searchByIndex(RandomRewardPool.SEARCH_RANDOM_REWARD_POOL)
                .addEqualCondition("saas_id", saasId)
                .addEqualCondition("is_active", true)
                .list();
    }
}
