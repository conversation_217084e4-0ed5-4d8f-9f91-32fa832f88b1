package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.UserLotteryProfile;

/**
 * 用户抽奖档案表数据访问层接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface UserLotteryProfileBuilder {

    /**
     * 根据用户ID查询用户档案
     */
    UserLotteryProfile findByUserId(String userId, String saasId);

    /**
     * 插入用户档案
     */
    boolean insert(UserLotteryProfile profile);

    /**
     * 更新用户档案
     */
    boolean update(UserLotteryProfile profile);

    /**
     * 根据ID查询用户档案
     */
    UserLotteryProfile findById(String profileId);
}