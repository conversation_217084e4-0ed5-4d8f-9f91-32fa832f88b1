package com.kikitrade.activity.dal.tablestore.model;

import com.aliyun.tablestore.model.Column;
import com.aliyun.tablestore.model.PartitionKey;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户进度数据模型
 * 记录用户在各种进度类型上的当前进度和历史记录
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class UserProgress implements Serializable {

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 用户ID（排序键1）
     */
    @PartitionKey(name = "user_id", value = 1)
    private String userId;

    /**
     * 进度类型（排序键2）
     */
    @PartitionKey(name = "progress_type", value = 2)
    private String progressType;

    /**
     * 当前进度值
     */
    @Column(name = "current_progress", type = Column.Type.INTEGER)
    private Integer currentProgress;

    /**
     * 历史最高进度值
     */
    @Column(name = "max_progress", type = Column.Type.INTEGER)
    private Integer maxProgress;

    /**
     * 今日进度值
     */
    @Column(name = "daily_progress", type = Column.Type.INTEGER)
    private Integer dailyProgress;

    /**
     * 本周进度值
     */
    @Column(name = "weekly_progress", type = Column.Type.INTEGER)
    private Integer weeklyProgress;

    /**
     * 本月进度值
     */
    @Column(name = "monthly_progress", type = Column.Type.INTEGER)
    private Integer monthlyProgress;

    /**
     * 上次重置时间
     */
    @Column(name = "last_reset_time", type = Column.Type.INTEGER)
    private Long lastResetTime;

    /**
     * 上次更新时间
     */
    @Column(name = "last_update_time", type = Column.Type.INTEGER)
    private Long lastUpdateTime;

    /**
     * 进度详情（JSON格式）
     * 存储详细的进度信息和历史记录
     */
    @Column(name = "progress_details")
    private String progressDetails;

    /**
     * 里程碑记录（JSON格式）
     * 记录用户达到的各个里程碑
     */
    @Column(name = "milestones")
    private String milestones;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_properties")
    private String extraProperties;
}
