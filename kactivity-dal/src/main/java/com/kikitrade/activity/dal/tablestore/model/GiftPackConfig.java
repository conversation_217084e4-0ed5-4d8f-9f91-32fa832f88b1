package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 礼包内容规则表
 * 对应技术规格书中的 gift_pack_config 表
 * 用于定义礼包内的固定奖励和随机奖励规则
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "gift_pack_config")
public class GiftPackConfig implements Serializable {

    public static final String SEARCH_GIFT_PACK_CONFIG = "search_gift_pack_config";

    /**
     * 主键ID
     */
    @PartitionKey(name = "id")
    private String id;

    /**
     * 礼包ID (关联 prize_config.prize_item_id)
     */
    @Column(name = "pack_id")
    @SearchIndex(name = SEARCH_GIFT_PACK_CONFIG, column = "pack_id")
    private String packId;

    /**
     * 规则类型 (FIXED_ITEM 或 RANDOM_POOL_PICK)
     */
    @Column(name = "rule_type")
    private String ruleType;

    /**
     * 物品ID (仅当 rule_type='FIXED_ITEM')
     */
    @Column(name = "item_id")
    private String itemId;

    /**
     * 最小数量 (仅当 rule_type='FIXED_ITEM')
     */
    @Column(name = "quantity_min", type = Column.Type.INTEGER)
    private Integer quantityMin;

    /**
     * 最大数量 (仅当 rule_type='FIXED_ITEM')
     */
    @Column(name = "quantity_max", type = Column.Type.INTEGER)
    private Integer quantityMax;

    /**
     * 随机池ID (仅当 rule_type='RANDOM_POOL_PICK')
     */
    @Column(name = "random_pool_id")
    private String randomPoolId;

    /**
     * 从随机池中抽取的数量 (仅当 rule_type='RANDOM_POOL_PICK')
     */
    @Column(name = "pick_count", type = Column.Type.INTEGER)
    private Integer pickCount;

    /**
     * 规则优先级/顺序
     */
    @Column(name = "rule_order", type = Column.Type.INTEGER)
    private Integer ruleOrder;

    /**
     * 是否启用
     */
    @Column(name = "is_active", type = Column.Type.BOOLEAN)
    private Boolean isActive;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * SaaS ID
     */
    @Column(name = "saas_id")
    @SearchIndex(name = SEARCH_GIFT_PACK_CONFIG, column = "saas_id")
    private String saasId;
}