package com.kikitrade.activity.dal.tablestore.model;

import com.aliyun.tablestore.model.Column;
import com.aliyun.tablestore.model.PartitionKey;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户礼包权益数据模型
 * 记录用户获得的礼包权益和领取状态
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class UserPackageEntitlement implements Serializable {

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 用户ID（排序键1）
     */
    @PartitionKey(name = "user_id", value = 1)
    private String userId;

    /**
     * 礼包ID（排序键2）
     */
    @PartitionKey(name = "package_id", value = 2)
    private String packageId;

    /**
     * 权益ID（唯一标识）
     */
    @Column(name = "entitlement_id")
    private String entitlementId;

    /**
     * 礼包名称（冗余字段，便于查询）
     */
    @Column(name = "package_name")
    private String packageName;

    /**
     * 礼包类型（冗余字段）
     */
    @Column(name = "package_type")
    private String packageType;

    /**
     * 权益状态
     * GRANTED: 已授予（未领取）
     * CLAIMED: 已领取
     * EXPIRED: 已过期
     * REVOKED: 已撤销
     */
    @Column(name = "status")
    private String status;

    /**
     * 授予原因
     * WELCOME: 新手奖励
     * LEVEL_UP: 等级提升
     * VIP_UPGRADE: VIP升级
     * ACTIVITY: 活动奖励
     * ACHIEVEMENT: 成就奖励
     * MANUAL: 手动发放
     */
    @Column(name = "grant_reason")
    private String grantReason;

    /**
     * 授予时间
     */
    @Column(name = "grant_time", type = Column.Type.INTEGER)
    private Long grantTime;

    /**
     * 领取时间
     */
    @Column(name = "claim_time", type = Column.Type.INTEGER)
    private Long claimTime;

    /**
     * 过期时间
     */
    @Column(name = "expire_time", type = Column.Type.INTEGER)
    private Long expireTime;

    /**
     * 奖品配置（JSON格式）
     * 记录用户实际获得的奖品内容
     */
    @Column(name = "prize_config")
    private String prizeConfig;

    /**
     * 领取IP地址
     */
    @Column(name = "claim_ip")
    private String claimIp;

    /**
     * 领取设备信息
     */
    @Column(name = "claim_device")
    private String claimDevice;

    /**
     * 领取渠道
     * APP: 移动应用
     * WEB: 网页
     * API: API调用
     */
    @Column(name = "claim_channel")
    private String claimChannel;

    /**
     * 已领取次数
     */
    @Column(name = "claim_count", type = Column.Type.INTEGER)
    private Integer claimCount;

    /**
     * 最大可领取次数
     */
    @Column(name = "max_claim_count", type = Column.Type.INTEGER)
    private Integer maxClaimCount;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_properties")
    private String extraProperties;
}
