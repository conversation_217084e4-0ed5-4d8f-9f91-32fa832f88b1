package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.UserClaimEntitlement;
import com.kikitrade.framework.ots.builder.BaseBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户领奖凭证表数据访问层
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
public class UserClaimEntitlementBuilder extends BaseBuilder<UserClaimEntitlement> {

    /**
     * 根据用户ID查询未领取的凭证
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 未领取的凭证列表
     */
    public List<UserClaimEntitlement> findUnclaimedByUserId(String userId, String saasId) {
        return searchByIndex(UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT)
                .addEqualCondition("user_id", userId)
                .addEqualCondition("saas_id", saasId)
                .addEqualCondition("status", "UNCLAIMED")
                .orderBy("create_time", false) // 按创建时间倒序
                .list();
    }

    /**
     * 根据凭证ID查询凭证
     * 
     * @param claimId 凭证ID
     * @param saasId SaaS ID
     * @return 凭证信息
     */
    public UserClaimEntitlement findByClaimId(String claimId, String saasId) {
        return searchByIndex(UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT)
                .addEqualCondition("claim_id", claimId)
                .addEqualCondition("saas_id", saasId)
                .one();
    }

    /**
     * 根据用户ID和凭证ID查询凭证
     * 
     * @param userId 用户ID
     * @param claimId 凭证ID
     * @param saasId SaaS ID
     * @return 凭证信息
     */
    public UserClaimEntitlement findByUserIdAndClaimId(String userId, String claimId, String saasId) {
        return searchByIndex(UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT)
                .addEqualCondition("user_id", userId)
                .addEqualCondition("claim_id", claimId)
                .addEqualCondition("saas_id", saasId)
                .one();
    }

    /**
     * 根据来源交易ID查询凭证（用于幂等性检查）
     * 
     * @param sourceTransactionId 来源交易ID
     * @param saasId SaaS ID
     * @return 凭证信息
     */
    public UserClaimEntitlement findBySourceTransactionId(String sourceTransactionId, String saasId) {
        return searchByIndex(UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT)
                .addEqualCondition("source_transaction_id", sourceTransactionId)
                .addEqualCondition("saas_id", saasId)
                .one();
    }

    /**
     * 根据用户ID和奖励类型查询凭证
     * 
     * @param userId 用户ID
     * @param rewardType 奖励类型
     * @param saasId SaaS ID
     * @return 凭证列表
     */
    public List<UserClaimEntitlement> findByUserIdAndRewardType(String userId, String rewardType, String saasId) {
        return searchByIndex(UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT)
                .addEqualCondition("user_id", userId)
                .addEqualCondition("reward_type", rewardType)
                .addEqualCondition("saas_id", saasId)
                .orderBy("create_time", false)
                .list();
    }

    /**
     * 根据状态查询凭证
     * 
     * @param status 状态
     * @param saasId SaaS ID
     * @return 凭证列表
     */
    public List<UserClaimEntitlement> findByStatus(String status, String saasId) {
        return searchByIndex(UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT)
                .addEqualCondition("status", status)
                .addEqualCondition("saas_id", saasId)
                .list();
    }

    /**
     * 查询过期的凭证
     * 
     * @param currentTime 当前时间
     * @param saasId SaaS ID
     * @return 过期的凭证列表
     */
    public List<UserClaimEntitlement> findExpiredEntitlements(Long currentTime, String saasId) {
        return searchByIndex(UserClaimEntitlement.SEARCH_USER_CLAIM_ENTITLEMENT)
                .addEqualCondition("status", "UNCLAIMED")
                .addEqualCondition("saas_id", saasId)
                .addRangeCondition("expire_time", null, currentTime) // expire_time < currentTime
                .list();
    }
}
