package com.kikitrade.activity.dal.tablestore.model;

import com.aliyun.tablestore.model.Column;
import com.aliyun.tablestore.model.PartitionKey;
import lombok.Data;

import java.io.Serializable;

/**
 * 进度宝箱配置数据模型
 * 存储进度宝箱的解锁条件、奖品内容等配置
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProgressChest implements Serializable {

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 宝箱ID（排序键）
     */
    @PartitionKey(name = "chest_id", value = 1)
    private String chestId;

    /**
     * 宝箱名称
     */
    @Column(name = "chest_name")
    private String chestName;

    /**
     * 宝箱类型
     * DAILY: 每日进度宝箱
     * WEEKLY: 每周进度宝箱
     * MONTHLY: 每月进度宝箱
     * LEVEL: 等级进度宝箱
     * ACHIEVEMENT: 成就进度宝箱
     * ACTIVITY: 活动进度宝箱
     */
    @Column(name = "chest_type")
    private String chestType;

    /**
     * 宝箱状态
     * ACTIVE: 激活
     * INACTIVE: 未激活
     * EXPIRED: 已过期
     */
    @Column(name = "status")
    private String status;

    /**
     * 宝箱描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 宝箱图标URL
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 进度类型
     * LOGIN_DAYS: 登录天数
     * DRAW_COUNT: 抽奖次数
     * WIN_COUNT: 中奖次数
     * POINTS_EARNED: 获得积分
     * LEVEL_REACHED: 达到等级
     * TASKS_COMPLETED: 完成任务数
     */
    @Column(name = "progress_type")
    private String progressType;

    /**
     * 解锁所需进度值
     */
    @Column(name = "required_progress", type = Column.Type.INTEGER)
    private Integer requiredProgress;

    /**
     * 奖品配置（JSON格式）
     * 存储宝箱包含的奖品列表和数量
     */
    @Column(name = "prize_config")
    private String prizeConfig;

    /**
     * 解锁条件（JSON格式）
     * 存储额外的解锁条件
     */
    @Column(name = "unlock_conditions")
    private String unlockConditions;

    /**
     * 有效期开始时间（时间戳）
     */
    @Column(name = "valid_start_time", type = Column.Type.INTEGER)
    private Long validStartTime;

    /**
     * 有效期结束时间（时间戳）
     */
    @Column(name = "valid_end_time", type = Column.Type.INTEGER)
    private Long validEndTime;

    /**
     * 宝箱价值（用于排序和展示）
     */
    @Column(name = "chest_value", type = Column.Type.INTEGER)
    private Integer chestValue;

    /**
     * 每用户最大领取次数（-1表示无限制）
     */
    @Column(name = "max_claim_per_user", type = Column.Type.INTEGER)
    private Integer maxClaimPerUser;

    /**
     * 重置周期（天）
     * 0: 不重置
     * 1: 每日重置
     * 7: 每周重置
     * 30: 每月重置
     */
    @Column(name = "reset_cycle_days", type = Column.Type.INTEGER)
    private Integer resetCycleDays;

    /**
     * 宝箱优先级（数字越小优先级越高）
     */
    @Column(name = "priority", type = Column.Type.INTEGER)
    private Integer priority;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * 创建者
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 扩展属性（JSON格式）
     * 存储其他自定义属性
     */
    @Column(name = "extra_properties")
    private String extraProperties;
}
