package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 奖池配置表
 * 对应技术规格书中的 prize_pool 表
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "prize_pool")
public class PrizePool implements Serializable {

    public static final String SEARCH_PRIZE_POOL = "search_prize_pool";

    /**
     * SaaS ID（分区键）- 多租户优先，数据分布均匀
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 奖池唯一编码（排序键）
     */
    @PartitionKey(name = "code", value = 1)
    private String code;

    /**
     * 系统内部ID（保留用于兼容性）
     */
    @Column(name = "id")
    private String id;

    /**
     * 奖池名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 兑换规则 JSON格式
     * [{"assetType": "POINTS", "cost": 100}]
     */
    @Column(name = "exchange_rules")
    private String exchangeRules;

    /**
     * 概率策略 (OVERALL 或 SINGLE)
     */
    @Column(name = "probability_strategy")
    private String probabilityStrategy;

    /**
     * 兜底奖品ID (SINGLE策略专用)
     */
    @Column(name = "fallback_prize_id", type = Column.Type.INTEGER)
    private Long fallbackPrizeId;

    /**
     * 每日抽奖上限 (-1表示无限制)
     */
    @Column(name = "daily_limit", type = Column.Type.INTEGER)
    private Integer dailyLimit;

    /**
     * 每周抽奖上限 (-1表示无限制)
     */
    @Column(name = "weekly_limit", type = Column.Type.INTEGER)
    private Integer weeklyLimit;

    /**
     * 每月抽奖上限 (-1表示无限制)
     */
    @Column(name = "monthly_limit", type = Column.Type.INTEGER)
    private Integer monthlyLimit;

    /**
     * 进度宝箱的刷新周期（天）
     */
    @Column(name = "chest_cycle_days", type = Column.Type.INTEGER)
    private Integer chestCycleDays;

    /**
     * 状态 (ACTIVE, INACTIVE)
     */
    @Column(name = "status")
    @SearchIndex(name = SEARCH_PRIZE_POOL, column = "status")
    private String status;

    /**
     * 活动开始时间
     */
    @Column(name = "start_time", type = Column.Type.INTEGER)
    private Long startTime;

    /**
     * 活动结束时间
     */
    @Column(name = "end_time", type = Column.Type.INTEGER)
    private Long endTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;


}