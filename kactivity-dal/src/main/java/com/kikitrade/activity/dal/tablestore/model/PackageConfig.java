package com.kikitrade.activity.dal.tablestore.model;

import com.aliyun.tablestore.model.Column;
import com.aliyun.tablestore.model.PartitionKey;
import lombok.Data;

import java.io.Serializable;

/**
 * 礼包配置数据模型
 * 存储礼包的基本信息、发放条件、奖品内容等配置
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class PackageConfig implements Serializable {

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 礼包ID（排序键）
     */
    @PartitionKey(name = "package_id", value = 1)
    private String packageId;

    /**
     * 礼包名称
     */
    @Column(name = "package_name")
    private String packageName;

    /**
     * 礼包类型
     * WELCOME: 新手礼包
     * DAILY: 每日礼包
     * WEEKLY: 每周礼包
     * MONTHLY: 每月礼包
     * ACTIVITY: 活动礼包
     * VIP: VIP礼包
     * ACHIEVEMENT: 成就礼包
     */
    @Column(name = "package_type")
    private String packageType;

    /**
     * 礼包状态
     * ACTIVE: 激活
     * INACTIVE: 未激活
     * EXPIRED: 已过期
     */
    @Column(name = "status")
    private String status;

    /**
     * 礼包描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 礼包图标URL
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 奖品配置（JSON格式）
     * 存储礼包包含的奖品列表和数量
     */
    @Column(name = "prize_config")
    private String prizeConfig;

    /**
     * 发放条件（JSON格式）
     * 存储礼包的发放条件，如用户等级、VIP等级、完成任务等
     */
    @Column(name = "grant_conditions")
    private String grantConditions;

    /**
     * 领取条件（JSON格式）
     * 存储礼包的领取条件和限制
     */
    @Column(name = "claim_conditions")
    private String claimConditions;

    /**
     * 有效期开始时间（时间戳）
     */
    @Column(name = "valid_start_time", type = Column.Type.INTEGER)
    private Long validStartTime;

    /**
     * 有效期结束时间（时间戳）
     */
    @Column(name = "valid_end_time", type = Column.Type.INTEGER)
    private Long validEndTime;

    /**
     * 礼包价值（用于排序和展示）
     */
    @Column(name = "package_value", type = Column.Type.INTEGER)
    private Integer packageValue;

    /**
     * 最大发放数量（-1表示无限制）
     */
    @Column(name = "max_grant_count", type = Column.Type.INTEGER)
    private Integer maxGrantCount;

    /**
     * 已发放数量
     */
    @Column(name = "granted_count", type = Column.Type.INTEGER)
    private Integer grantedCount;

    /**
     * 每用户最大领取次数（-1表示无限制）
     */
    @Column(name = "max_claim_per_user", type = Column.Type.INTEGER)
    private Integer maxClaimPerUser;

    /**
     * 礼包优先级（数字越小优先级越高）
     */
    @Column(name = "priority", type = Column.Type.INTEGER)
    private Integer priority;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

    /**
     * 创建者
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 扩展属性（JSON格式）
     * 存储其他自定义属性
     */
    @Column(name = "extra_properties")
    private String extraProperties;
}
