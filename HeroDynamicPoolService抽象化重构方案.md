# HeroDynamicPoolService抽象化重构方案

## 🎯 重构目标
将当前与英雄概念强耦合的HeroDynamicPoolService重构为通用的、可扩展的动态奖池构建框架，支持多维度过滤条件和插件化扩展。

## 📊 当前设计问题分析

### 1. 现有设计的局限性

#### 1.1 硬编码英雄逻辑
```java
// 当前实现中的问题
public List<PrizeConfig> buildDynamicPool(String userId, String prizePoolCode, String saasId) {
    // 硬编码获取英雄ID
    String selectedHeroId = userProfile.getSelectedHeroId();

    // 硬编码英雄专属奖品查询
    List<PrizeConfig> heroPrizes = prizeConfigBuilder.findByPrizePoolCodeAndHeroId(prizePoolCode, selectedHeroId);

    // 硬编码合并逻辑
    dynamicPool.addAll(commonPrizes);
    dynamicPool.addAll(heroPrizes);
}
```

#### 1.2 单一维度过滤
- 只支持英雄维度的过滤
- 无法扩展到其他维度（VIP等级、地区、用户等级等）
- 过滤逻辑固化，难以复用

#### 1.3 紧耦合设计
- 服务名称与英雄概念绑定
- 方法名与英雄概念绑定
- 数据模型与英雄概念绑定

### 2. 扩展需求分析

未来可能需要支持的过滤维度：
- **英雄维度**：当前已支持
- **VIP等级维度**：VIP用户专属奖品
- **用户等级维度**：不同等级用户的专属奖品
- **地区维度**：特定地区的专属奖品
- **时间维度**：特定时间段的限时奖品
- **活动维度**：参与特定活动的用户专属奖品

## 🚀 抽象化重构方案

### 1. 核心抽象设计

#### 1.1 过滤策略接口
```java
/**
 * 奖池过滤策略接口
 */
public interface PrizePoolFilterStrategy {

    /**
     * 策略名称
     */
    String getStrategyName();

    /**
     * 获取用户的过滤条件值
     */
    String getUserFilterValue(String userId, String saasId);

    /**
     * 根据过滤条件查询奖品配置
     */
    List<PrizeConfig> filterPrizes(String prizePoolCode, String filterValue);

    /**
     * 策略优先级（数字越小优先级越高）
     */
    int getPriority();
}
```

#### 1.2 用户偏好管理接口
```java
/**
 * 用户偏好管理接口
 */
public interface UserPreferenceService {

    /**
     * 设置用户偏好
     */
    boolean setUserPreference(String userId, String saasId, String preferenceType, String preferenceValue);

    /**
     * 获取用户偏好
     */
    String getUserPreference(String userId, String saasId, String preferenceType);

    /**
     * 获取用户所有偏好
     */
    Map<String, String> getAllUserPreferences(String userId, String saasId);
}
```

#### 1.3 动态奖池构建器
```java
/**
 * 动态奖池构建器
 */
public interface DynamicPrizePoolBuilder {

    /**
     * 构建动态奖池
     */
    List<PrizeConfig> buildDynamicPool(String userId, String prizePoolCode, String saasId);

    /**
     * 注册过滤策略
     */
    void registerFilterStrategy(PrizePoolFilterStrategy strategy);

    /**
     * 获取所有已注册的策略
     */
    List<PrizePoolFilterStrategy> getAllStrategies();
}
```

### 2. 具体实现示例

#### 2.1 英雄过滤策略实现
```java
@Component
public class HeroFilterStrategy implements PrizePoolFilterStrategy {

    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;

    @Resource
    private UserPreferenceService userPreferenceService;

    @Override
    public String getStrategyName() {
        return "HERO";
    }

    @Override
    public String getUserFilterValue(String userId, String saasId) {
        return userPreferenceService.getUserPreference(userId, saasId, "SELECTED_HERO");
    }

    @Override
    public List<PrizeConfig> filterPrizes(String prizePoolCode, String filterValue) {
        if (filterValue == null) {
            return Collections.emptyList();
        }
        return prizeConfigBuilder.findByPrizePoolCodeAndHeroId(prizePoolCode, filterValue);
    }

    @Override
    public int getPriority() {
        return 100; // 英雄策略优先级
    }
}
```

#### 2.2 VIP等级过滤策略实现
```java
@Component
public class VipLevelFilterStrategy implements PrizePoolFilterStrategy {

    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;

    @Resource
    private UserVipService userVipService; // 假设的VIP服务

    @Override
    public String getStrategyName() {
        return "VIP_LEVEL";
    }

    @Override
    public String getUserFilterValue(String userId, String saasId) {
        // 从VIP服务获取用户VIP等级
        return userVipService.getUserVipLevel(userId, saasId);
    }

    @Override
    public List<PrizeConfig> filterPrizes(String prizePoolCode, String filterValue) {
        if (filterValue == null) {
            return Collections.emptyList();
        }
        // 查询VIP等级专属奖品
        return prizeConfigBuilder.findByPrizePoolCodeAndVipLevel(prizePoolCode, filterValue);
    }

    @Override
    public int getPriority() {
        return 200; // VIP策略优先级
    }
}
```

### 3. 向后兼容的适配器

#### 3.1 HeroDynamicPoolService适配器
```java
@Service
@Deprecated // 标记为废弃，但保持兼容
public class HeroDynamicPoolService {

    @Resource
    private DynamicPrizePoolBuilder dynamicPrizePoolBuilder;

    @Resource
    private UserPreferenceService userPreferenceService;

    /**
     * 构建英雄动态奖池（兼容方法）
     */
    public List<PrizeConfig> buildDynamicPool(String userId, String prizePoolCode, String saasId) {
        // 委托给新的动态奖池构建器
        return dynamicPrizePoolBuilder.buildDynamicPool(userId, prizePoolCode, saasId);
    }

    /**
     * 用户选择英雄（兼容方法）
     */
    public boolean selectHero(String userId, String heroId, String saasId) {
        // 委托给用户偏好服务
        return userPreferenceService.setUserPreference(userId, saasId, "SELECTED_HERO", heroId);
    }

    /**
     * 获取用户选择的英雄（兼容方法）
     */
    public String getUserSelectedHero(String userId, String saasId) {
        // 委托给用户偏好服务
        return userPreferenceService.getUserPreference(userId, saasId, "SELECTED_HERO");
    }
}
```

## 📈 重构收益分析

### 1. 扩展性提升
- **插件化架构**：新增过滤维度只需实现PrizePoolFilterStrategy接口
- **配置化管理**：过滤策略可以通过配置动态启用/禁用
- **优先级控制**：支持多策略的优先级排序

### 2. 代码复用性
- **通用过滤框架**：可复用于其他类似场景
- **策略模式**：每个过滤维度独立实现，便于维护
- **接口抽象**：降低模块间耦合度

### 3. 业务灵活性
- **多维度组合**：支持英雄+VIP+地区等多维度组合过滤
- **动态配置**：运行时动态调整过滤策略
- **A/B测试支持**：可以为不同用户群体配置不同策略

## 🎉 总结

这个抽象化重构方案将：

1. **解耦英雄概念**：从硬编码的英雄逻辑抽象为通用的过滤策略
2. **提升扩展性**：支持多维度过滤条件的插件化扩展
3. **保持兼容性**：通过适配器模式确保现有代码无需修改
4. **增强灵活性**：支持运行时动态配置和多策略组合

建议分阶段实施：
- **阶段1**：实现核心抽象接口和英雄策略
- **阶段2**：实现用户偏好服务和动态构建器
- **阶段3**：添加新的过滤策略（VIP、等级等）
- **阶段4**：逐步迁移现有业务代码