# 奖励中台后端技术规格书 (最终版)

## 1. 系统概述

本文档为一份完整的后端技术规格说明书，旨在为开发一个功能全面、高内聚、低耦合的**奖励中台**提供清晰、详尽的实现指南。该系统作为平台能力，支持由多个外部业务渠道驱动，并集成了英雄动态奖池、多维度风控、多种激励机制和灵活的概率策略。

### 核心功能：

- **概率性抽奖**：
  - **英雄动态奖池**：支持根据用户存储的英雄选择，动态替换奖池中的专属奖品。
  - **双重概率策略**：支持“整体概率 (OVERALL)”和“单品概率 (SINGLE)”两种模式。
  - **两阶段抽奖**：提供独立的“资产兑换抽奖券”和“消耗抽奖券批量抽奖”接口，由前端编排调用。
- **组合奖励 (礼包/宝箱)**：支持配置“礼包”型奖品，内含多个固定奖励和随机奖励。可通过抽奖获得，也可由外部服务直接按ID触发发放。
- **统一领奖能力**：提供一个统一、抽象的领奖接口，用于核销由不同业务系统（如抽奖进度、任务系统）为用户生成的“领奖凭证”。
- **定向奖励发放**：提供安全的内部API，允许其他微服务直接指定奖励内容并调用本中台进行发放。
- **排行榜奖励发放**：支持由上游系统（如排行榜服务）定时调用，根据排名配置，通过消息队列异步为上榜用户发放奖励。
- **多维度抽奖风控**：可按日、周、月三个维度精确控制用户的抽奖次数上限。
- **后台管理支持**：提供独立的API接口，供管理后台配置活动和监控库存。

## 2. 系统架构与流程图

### 2.1 业务流程图

#### **A. 抽奖与进度奖励流程**

```
[游戏客户端]
    |
    | 1. 调用批量抽奖接口
    v
[奖励中台 API (/lottery/draw-batch)]
    |
    | 2. (内部) 执行抽奖 & 更新用户进度
    |
    | 3. (判断) 用户进度是否达到解锁宝箱条件？
    |    |
    |    +-----> 是 ----> [奖励中台 核心逻辑]
    |                       |
    |                       | 4. (内部调用) 创建一条“领奖凭证”
    |                       v
    |                    [user_claim_entitlement 表]
    v
[游戏客户端] <--- (轮询/推送) --- [奖励中台]
    |
    | 5. 发现有新的可领取奖励，显示“领取”按钮
    v
[奖励中台 API (/rewards/claim)]
    |
    | 6. 接收领奖请求 (含 claimId)
    v
[奖励中台 核心逻辑]
    |
    | 7. 核销凭证，发放宝箱奖励，更新凭证状态
    v
[数据库]
```

#### **B. 外部服务授予奖励流程**

```
[上游服务 (如任务系统)]
    |
    | 1. 业务完成，决定授予用户奖励
    v
[奖励中台 内部API (grantClaimEntitlement)]
    |
    | 2. 创建一条“领奖凭证”
    v
[user_claim_entitlement 表]
    |
    | 3. (后续流程同上)
    v
[游戏客户端] -> [奖励中台 API (/rewards/claim)] -> ...
```

## 3. 数据模型 (Data Model)

### 3.1 `prize_pool` - 奖池配置表



| **字段**               | **类型**     | **含义**                                          |
| ---------------------- | ------------ | ------------------------------------------------- |
| `id`                   | BIGINT       | 主键ID                                            |
| `code`                 | VARCHAR(50)  | 奖池唯一编码                                      |
| `name`                 | VARCHAR(100) | 奖池名称                                          |
| `exchange_rules`       | JSON         | 兑换规则 `[{"assetType": "POINTS", "cost": 100}]` |
| `probability_strategy` | VARCHAR(20)  | 概率策略 (`OVERALL` 或 `SINGLE`)                  |
| `fallback_prize_id`    | BIGINT       | 兜底奖品ID (`SINGLE`策略专用)                     |
| `daily_limit`          | INT          | 每日抽奖上限 (-1表示无限制)                       |
| `weekly_limit`         | INT          | 每周抽奖上限 (-1表示无限制)                       |
| `monthly_limit`        | INT          | 每月抽奖上限 (-1表示无限制)                       |
| `chest_cycle_days`     | INT          | 进度宝箱的刷新周期（天）                          |
| `status`               | VARCHAR(20)  | 状态 (`ACTIVE`, `INACTIVE`)                       |
| `start_time`           | DATETIME     | 活动开始时间                                      |
| `end_time`             | DATETIME     | 活动结束时间                                      |
| `create_time`          | DATETIME     | 创建时间                                          |
| `update_time`          | DATETIME     | 更新时间                                          |

### 3.2 `prize_config` - 奖品配置表

| **字段**              | **类型**       | **含义**                                      |
| --------------------- | -------------- | --------------------------------------------- |
| `id`                  | BIGINT         | 主键ID                                        |
| `prize_pool_code`     | VARCHAR(50)    | 所属奖池编码                                  |
| `hero_id`             | VARCHAR(100)   | 关联的英雄ID (`NULL`表示通用)                 |
| `prize_name`          | VARCHAR(100)   | 奖品名称                                      |
| `prize_type`          | VARCHAR(50)    | 奖品类型 (如 `ITEM`, `CURRENCY`, `GIFT_PACK`) |
| `prize_item_id`       | VARCHAR(100)   | 奖品/礼包ID                                   |
| `prize_icon`          | VARCHAR(255)   | 奖品图标URL                                   |
| `quantity_per_win`    | INT            | 每次中奖发放数量                              |
| `winning_probability` | DECIMAL(10, 8) | 中奖概率                                      |
| `stock_quantity`      | INT            | 库存数量 (-1 表示无限)                        |
| `is_active`           | TINYINT(1)     | 是否生效                                      |
| `create_time`         | DATETIME       | 创建时间                                      |
| `update_time`         | DATETIME       | 更新时间                                      |

### 3.3 `user_lottery_profile` - 用户抽奖档案表

| **字段**             | **类型**     | **含义**             |
| -------------------- | ------------ | -------------------- |
| `id`                 | BIGINT       | 主键ID               |
| `user_id`            | BIGINT       | 用户ID (唯一)        |
| `selected_hero_id`   | VARCHAR(100) | 用户当前选择的英雄ID |
| `total_draw_count`   | BIGINT       | 历史总抽奖次数       |
| `daily_draw_count`   | INT          | 今日已抽奖次数       |
| `weekly_draw_count`  | INT          | 本周已抽奖次数       |
| `monthly_draw_count` | INT          | 本月已抽奖次数       |
| `last_draw_time`     | DATETIME     | 上次抽奖时间         |
| `create_time`        | DATETIME     | 创建时间             |
| `update_time`        | DATETIME     | 更新时间             |

### 3.4 `progress_chest_config` - 进度宝箱配置表

| **字段**            | **类型**     | **含义**                                                |
| ------------------- | ------------ | ------------------------------------------------------- |
| `id`                | BIGINT       | 主键ID                                                  |
| `prize_pool_code`   | VARCHAR(50)  | 关联的奖池编码                                          |
| `hero_id`           | VARCHAR(100) | 关联的英雄ID (`NULL`表示通用宝箱)                       |
| `chest_name`        | VARCHAR(100) | 宝箱名称                                                |
| `unlock_progress`   | INT          | 解锁所需的进度值                                        |
| `rewards`           | JSON         | 宝箱内的奖励 (**注意: 此字段可废弃，统一使用礼包配置**) |
| `pack_id_on_unlock` | VARCHAR(100) | 解锁时对应的礼包ID                                      |
| `display_order`     | INT          | 显示顺序                                                |
| `is_active`         | TINYINT(1)   | 是否启用                                                |
| `create_time`       | DATETIME     | 创建时间                                                |
| `update_time`       | DATETIME     | 更新时间                                                |

### 3.5 `user_claim_entitlement` - 用户领奖凭证表 (核心)

| **字段**                | **类型**     | **含义**                                    |
| ----------------------- | ------------ | ------------------------------------------- |
| `id`                    | BIGINT       | 主键ID                                      |
| `claim_id`              | VARCHAR(255) | 领奖凭证唯一ID (UUID)                       |
| `user_id`               | BIGINT       | 用户ID                                      |
| `reward_type`           | VARCHAR(50)  | 奖励类型 (`PROGRESS_CHEST`, `GRANTED_PACK`) |
| `reward_source_id`      | VARCHAR(100) | 奖励来源ID (如 `chestId` 或 `packId`)       |
| `status`                | VARCHAR(20)  | 状态 (`UNCLAIMED`, `CLAIMED`)               |
| `source_channel`        | VARCHAR(50)  | 凭证来源渠道                                |
| `source_transaction_id` | VARCHAR(255) | 来源渠道的唯一交易ID (幂等键)               |
| `create_time`           | DATETIME     | 创建时间                                    |
| `update_time`           | DATETIME     | 更新时间                                    |

### 3.6 `draw_history` - 抽奖历史记录表

(保持不变)

### 3.7 `gift_pack_config` - 礼包内容规则表

(保持不变)

### 3.8 `random_reward_pool` - 随机奖励池定义表

(保持不变)

### 3.9 `direct_reward_issuance_log` - 定向奖励发放流水表

(保持不变)

### 3.10 `leaderboard_reward_config` - 排行榜奖励配置表

(保持不变)

## 4. 核心业务逻辑

### 4.1 批量抽奖与进度处理

1. **抽奖主流程**: (保持不变) ...
2. **更新用户进度**:
   - 在批量抽奖成功后，根据抽奖次数更新用户的进度值。
3. **检查并生成凭证**:
   - 检查更新后的进度值是否跨越了某个或某些 `progress_chest_config` 中配置的 `unlock_progress` 阈值。
   - 对于每一个新解锁的宝箱，调用内部服务，在 `user_claim_entitlement` 表中**创建一条新的凭证记录**。
     - `claim_id` = 生成新的UUID
     - `reward_type` = 'PROGRESS_CHEST'
     - `reward_source_id` = 对应的 `chestId`
     - `status` = 'UNCLAIMED'

## 5. API 接口设计

### 5.1 用户端 API

#### `POST /api/lottery/exchange-tickets`

(保持不变)

#### `POST /api/lottery/draw-batch`

(保持不变)

#### `POST /api/lottery/select-hero`

(保持不变)

#### `GET /api/lottery/state`

- Success Response (200 OK) - 

  已调整

  :

  ```
  {
    "success": true,
    "data": {
      // ... prizePool, userProfile 保持不变
      "claimableRewards": [ // 新增字段，替代原有的 chestProgress
        {
          "claimId": "uuid-for-chest-1",
          "rewardType": "PROGRESS_CHEST",
          "rewardName": "进度宝箱1",
          "rewardIcon": "url_to_chest_icon"
        },
        {
          "claimId": "uuid-for-granted-pack-2",
          "rewardType": "GRANTED_PACK",
          "rewardName": "任务奖励礼包",
          "rewardIcon": "url_to_pack_icon"
        }
      ]
    }
  }
  ```

- **核心逻辑**: 查询 `user_claim_entitlement` 表中该用户所有 `status` 为 `UNCLAIMED` 的记录，并格式化返回。

#### `POST /api/rewards/claim` (新)

用户领取一个已解锁的奖励（无论是宝箱还是礼包）。

- **Request Body**:

  ```
  {
    "claimId": "a-unique-uuid-representing-the-entitlement"
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "rewards": [
        { "itemId": "gold", "itemName": "金币", "quantity": 2000 }
      ]
    }
  }
  ```

- **核心逻辑**:

  1. 认证用户Token。
  2. 根据 `claimId` 和 `userId` 查询 `user_claim_entitlement` 表，验证凭证有效性。
  3. 根据凭证中的 `reward_type` 和 `reward_source_id`，查询相应的配置（如 `progress_chest_config` 或 `gift_pack_config`），确定要发放的 `pack_id`。
  4. 执行“开启礼包”逻辑，发放奖励。
  5. 将凭证状态更新为 `CLAIMED`。

#### `GET /api/leaderboards/rewards`

(保持不变)

### 5.2 管理后台 API

(保持不变)

### 5.3 内部服务 API (Dubbo)

#### `grantClaimEntitlement(GrantEntitlementRequest request)` (新)

由上游服务（如任务系统）为用户创建领奖凭证。

- **Request DTO**:

  ```
  public class GrantEntitlementRequest implements Serializable {
      private String userId;
      private String rewardType;
      private String rewardSourceId;
      private String sourceChannel;
      private String sourceTransactionId; // 用于幂等性
  }
  ```

- **核心逻辑**: 幂等性检查后，在 `user_claim_entitlement` 表中插入一条 `status` 为 `UNCLAIMED` 的记录。

#### `issuePack(IssuePackRequest request)`

(保持不变)

#### `issueDirect(IssueDirectRequest request)`

(保持不变)

#### `issueLeaderboard(IssueLeaderboardRequest request)`

(保持不变)

**废弃的表和API**:

- **表**: `user_chest_progress`, `user_pack_entitlement`
- **API**: `/api/lottery/chest/claim`, `/api/lottery/packs/claim`

这个重构后的设计，让您的奖励中台在架构上更加清晰、抽象和健壮，为未来的业务扩展奠定了坚实的基础。