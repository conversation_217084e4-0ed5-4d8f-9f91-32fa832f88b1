package com.kikitrade.activity.service.task;

import com.kikitrade.activity.api.model.ActivityTaskVO;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.TaskSubmitDTO;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.mq.ActivityEventMessage;

import java.util.List;

public interface ActivityTaskService {

    /**
     * 新增任务
     * @param activityTaskConfig
     * @return
     */
    ActivityTaskConfig insert(ActivityTaskConfig activityTaskConfig);

    /**
     * 修改任务
     * @param activityTaskConfig
     * @return
     */
    ActivityTaskConfig update(ActivityTaskConfig activityTaskConfig);


    /**
     * 查询任务配置
     * @param id
     * @return
     */
    ActivityTaskConfig findTaskById(String id);

    /***********上面的过期*****************/

    /**
     * 做任务
     * @param activityEventMessage
     * @param taskConfig
     * @param codeConfig
     * @throws Exception
     */
    List<Award> doTask(ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig) throws Exception;

    /**
     * 获取某个任务完成的结果
     * @param customerId
     * @param cycle
     * @param taskId
     * @return
     */
    TaskCompletedResult getTaskResult(String customerId, String cycle, String taskId);

    TaskCompletedResult getTaskDetailResult(String customerId, String cycle ,String taskId, String targetId);

    TaskCompletedResult getTaskResultBySocial(String platform, String socialCustomerId, String cycle, String taskId);

    /**
     * 任务列表
     * @param request
     * @return
     */
    List<TaskListResponse> taskList(TaskListRequest request);

    /**
     * 任务详情
     * @param taskId
     * @param customerId
     * @return
     */
    TaskDetailResponse findByTaskId(String taskId, String customerId, ActivityConstant.VipLevelEnum vipLevelEnum, String channel);

    /**
     * 根据任务code查询任务
     * @param taskCode
     * @param customerId
     * @return
     */
    TaskCodeDetailResponse findByTaskCode(String saasId, String taskCode, String customerId);


    /**
     * 根据任务code查询应得奖励
     * @param taskCode
     * @param saasId
     * @param vipLevelEnum
     * @return
     */
    List<Award>  findByTaskCode(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum);

    /**
     * 根据任务code查询应得奖励列表
     * @param taskCode
     * @param saasId
     * @param vipLevelEnum
     * @return
     */
    List<Award> listTaskRewards(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum);

    TaskProgressResponse findProgressByTaskId(String saasId, String taskId, String customerId, String type);

    /**
     * 获取早鸟弹窗
     * @param saasId
     * @param customerId
     * @return
     */
    TaskPopResponse getTaskEarlyBirdPop(String saasId, String customerId);

    /**
     * 根据 code列表 和 任务id 批量创建兑换码，校验完全通过后统一创建
     * @param codes /
     * @param saasId /
     * @param taskId /
     * @param businessType /
     * @return List<String> 返回失败时校验不通过的code
     */
    Result<List<String>> batchCreateRedeemByTask(List<String> codes, String saasId, String taskId, String businessType);

    /**
     *
     * @param accessToken
     * @param taskId
     * @param saasId
     * @param ext
     * @return
     * @throws ActivityException
     */
    VerifyResponse verify(Token accessToken, String taskId, String saasId, String ext) throws ActivityException;

    /**
     * 根据 code列表 和 任务id 批量创建兑换码，校验完全通过后统一创建
     * @param codes /
     * @param saasId /
     * @param taskId /
     * @param businessType /
     * @return List<String> 返回失败时校验不通过的code
     */
    Result<List<String>> batchCreateRedeem(List<String> codes, String saasId, String taskId, String businessType, String awardValue);

    VerifyResponse serverVerify(String saasId, String uid, String scene, String ext) throws ActivityException;

    boolean submitTask(TaskSubmitDTO taskSubmitDTO);
}
