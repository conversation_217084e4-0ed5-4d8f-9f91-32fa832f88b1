package com.kikitrade.activity.service.importing.source.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.OSSObject;
import com.csvreader.CsvReader;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.importing.source.CsvSourceService;
import com.kikitrade.activity.service.importing.source.domain.Constant;
import com.kikitrade.activity.service.importing.source.domain.ImportSourceDTO;
import com.kikitrade.kcustomer.api.model.credential.OssObjectDTO;
import com.kikitrade.kcustomer.api.model.credential.SignOssUrlRequest;
import com.kikitrade.kcustomer.api.service.RemoteCredentialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractCsvSourceService implements CsvSourceService {

    @Resource
    private UploadOssUtil uploadOssUtil;
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @Resource
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @Resource
    private ActivityEntityService activityEntityService;
    @DubboReference
    private RemoteCredentialService remoteCredentialService;
    @Resource
    private KactivityProperties kactivityProperties;
    /**
     * 获取oss对象
     *
     * @param fileUrl
     * @return
     */
    public OSSObject getOss(String fileUrl) {
        try {
            Map<String, String> urlMap = signOssUrl(kactivityProperties.getSaasId(), Collections.singletonList(fileUrl));
            fileUrl = urlMap.getOrDefault(fileUrl, fileUrl);
            return uploadOssUtil.getObject(new URL(fileUrl));
        } catch (Exception ex) {
            log.error("获取oss文件异常，fileUrl:{}", fileUrl, ex);
            return null;
        }
    }

    /**
     * 导入文件到roster表
     *
     * @param fileUrl oss地址
     * @param dto
     * @return
     */
    @Override
    public Result<String> importSource(String fileUrl, ImportSourceDTO dto) {
        if (StringUtils.isBlank(dto.getBatchId()) || StringUtils.isBlank(fileUrl)) {
            return new Result<>(false, Constant.FILE_BLANK_ERROR);
        }

        OSSObject ossObject = getOss(fileUrl);
        if (ossObject == null) {
            return new Result<>(false, Constant.FILE_NOT_EXIST_ERROR);
        }
        ActivityBatch batch = activityBatchNewService.findByBatchId(dto.getBatchId());
        if (batch == null) {
            return new Result<>(false, Constant.BATCH_INVALID);
        }
        CsvReader csvReader = null;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent()))) {
            csvReader = new CsvReader(reader, Constant.Delimiter);
            csvReader.setSkipEmptyRecords(true);
            csvReader.readHeaders();            // 读表头
            //校验表头
            if(!checkCsvHead(csvReader.getHeaders())){
                return new Result<>(false, Constant.FILE_FORMAT_ERROR);
            }
            ActivityEntity activityEntity = activityEntityService.findById(batch.getActivityId());
            List<ActivityBatchRewardRoster> list = new ArrayList<>();
            Map<String, Integer> map = new HashedMap();
            int num = 0;

            while (csvReader.readRecord()) {    // 读数据
                if(isAllBlank(csvReader.getValues())){
                    continue;
                }
                num++;
                list.add(parseOssObject(activityEntity, batch, csvReader.getValues(), map));
            }
            if (list.size() > 0) {
                for(int i = 0; i < list.size(); i+=100){
                    activityBatchRewardRosterStoreBuilder.batchInsert(list.subList(i, Math.min(i+100, list.size())));
                }
            }
            ActivityBatchStatus activityBatchStatus = new ActivityBatchStatus();
            activityBatchStatus.setBatchId(batch.getBatchId());
            activityBatchStatus.setStatus(ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name());
            activityBatchStatus.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            activityBatchStatus.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            activityBatchStatus.setNum(num);
            activityBatchStatusStoreBuilder.insert(activityBatchStatus);
            log.debug("ActivityRosterImportService, file read finish. key:{}", fileUrl);
        } catch (Exception e) {
            log.error("ActivityRosterImportService, file read exception, path:{}", fileUrl, e);
            return new Result<>(false, Constant.FILE_FORMAT_ERROR);
        } finally {
            if (csvReader != null) {
                try {
                    csvReader.close();
                } catch (Exception e) {
                    log.error("ActivityRosterImportService, csvReader close exception.", e);
                }
            }
        }
        return new Result<>(true, "upload success");
    }

    /**
     * 校验格式
     *
     * @param head
     * @return
     */
    public Boolean checkCsvHead(String[] head) {
        try {
            for (int i = 0; i < header().length; i++) {
                if (!header()[i].replace("\\s+", "").equalsIgnoreCase(head[i].trim().replace("\\s+", ""))) {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            log.error("csv head check error, head:{}", head, ex);
            return false;
        }
    }

    public ActivityConstant.SeqPrefix getSeq(String activityType){
        return ActivityConstant.SeqPrefix.getSeq(activityType);
    }

    private boolean isAllBlank(Object[] objs){
        for(Object obj : objs){
            if(StringUtils.isNotBlank(String.valueOf(obj))){
                return false;
            }
        }
        return true;
    }

    /**
     * 支持的活动类型
     * @return
     */
    public abstract boolean support(String activityType);

    /**
     * 表头
     * @return
     */
    public abstract String[] header();

    /**
     * 实体转换
     * @param activityBatch
     * @param ossObject
     * @param map
     * @return
     */
    public abstract ActivityBatchRewardRoster parseOssObject(ActivityEntity activityEntity, ActivityBatch activityBatch, String[] ossObject, Map<String, Integer> map);

    private Map<String, String> signOssUrl(String saasId, List<String> urls) {
        try {
            if (urls.size() > 0) {
                List<OssObjectDTO> results = remoteCredentialService.signOssUrl(SignOssUrlRequest.builder()
                        .saasId(saasId)
                        .expirationInSecond((int) Duration.ofDays(1).getSeconds())
                        .urls(urls)
                        .build());
                return results.stream().collect(Collectors.toMap(o -> o.getOriginUrl(), o -> o.getSignedUrl()));
            }
        } catch (Exception e) {
            log.error("signOssUrl failed, urls:{}", JSON.toJSONString(urls), e);
        }
        return new HashMap<>();
    }
}
