package com.kikitrade.activity.service.business;

import com.kikitrade.activity.api.model.ActivityDTO;
import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.model.ActivityEventMassage;
import com.kikitrade.activity.service.common.model.JsonResult;


public interface ActivityService {

    JsonResult save(Activity activity) throws Exception;

    JsonResult update(Activity activity) throws Exception;

    JsonResult findById(Integer id);

    JsonResult findDetailById(Integer id);

    JsonResult delete(Integer id, boolean flag) throws Exception;

    JsonResult findAll(String saasId, Integer offset, Integer limit, boolean flag, Integer type);

    JsonResult findForStatsUpdate();

    JsonResult updateStatus(Integer Id, Integer status) throws Exception;

    Activity findActiveByTypeAndLocale(String saasId, Integer type,String locale);

    ActivityResponse activityManualReward(ActivityEventMassage activityEventMassage);

    ActivityResponse activityDataProcess(ActivityEventMassage activityEventMassage);

    ActivityDTO findActivity(Integer type, String local);
}
