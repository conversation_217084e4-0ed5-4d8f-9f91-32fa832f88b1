package com.kikitrade.activity.service.autoreward.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Instance;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.odps.dao.AutoRewardOdpsDao;
import com.kikitrade.activity.service.autoreward.AutoRewardService;
import com.kikitrade.activity.service.autoreward.domain.Condition;
import com.kikitrade.activity.service.config.KactivitySqlConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-11-30 17:38
 */
@Service
@Slf4j
public class AutoRewardServiceImpl implements AutoRewardService {

    @Resource
    private AutoRewardOdpsDao autoRewardOdpsDao;

    @Override
    public Instance executeTradeRecord(ActivityEntity activityEntity, String batchId) {
        try{
            KactivitySqlConfig.SqlTemplate sqlTemplate = KactivitySqlConfig.getValue(activityEntity.getTemplateCode());
            log.info("executeTradeRecord:{},{}", sqlTemplate, activityEntity.getConditions());

            Map<String, Object> conditionParam = JSON.parseArray(activityEntity.getConditions(), Condition.class)
                    .stream().collect(Collectors.toMap(Condition::getName, Condition::getValue));
            conditionParam.put("batchId", batchId);
            log.info("executeTradeRecord:{}", conditionParam);
            return autoRewardOdpsDao.execute(sqlTemplate.getTemplateCode(), sqlTemplate.getSql(), conditionParam);
        }catch (Exception ex){
            log.error("executeTradeRecord error", ex);
            return null;
        }
    }
}
