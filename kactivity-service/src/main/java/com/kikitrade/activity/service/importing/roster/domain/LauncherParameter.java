package com.kikitrade.activity.service.importing.roster.domain;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Data
public class LauncherParameter implements Serializable {

    private ActivityBatch batch;

    private Long shard = 1L;

    private Long totalShard = 1L;

    private PrimaryKey nextToken;

    /**
     * 单条发奖
     */
    private ActivityCustomReward activityCustomReward;

    private ActivityTaskConstant.ProvideType provideType;
}
