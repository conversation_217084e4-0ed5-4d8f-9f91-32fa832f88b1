package com.kikitrade.activity.service.importing.source.impl;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.AssertUtil;
import com.kikitrade.market.client.CurrencyClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class CustomizeCsvSourceServiceImpl extends AbstractCsvSourceService {

    public static final String[] CSV_HEAD = new String[]{"Customer ID", "Award Amount", "Award", "Award Type"};
    @Resource
    private CurrencyClient currencyClient;

    /**
     * 解析csv文件
     *
     * @param activityBatch
     * @param ossObject
     * @return
     */
    @Override
    public ActivityBatchRewardRoster parseOssObject(ActivityEntity activityEntity, ActivityBatch activityBatch, String[] ossObject, Map<String, Integer> map) {
        ActivityBatchRewardRoster roster = new ActivityBatchRewardRoster();
        //{"Customer ID", "Award Amount", "Award", "Award Type"};

        AssertUtil.isNotBlank(ossObject[0], "customerId cannot be empty");
        AssertUtil.isNotBlank(ossObject[1], "award amount cannot be empty");
        AssertUtil.isNumber(ossObject[1], "award amount is not number");
        AssertUtil.isNotBlank(ossObject[2], "award cannot be empty");
        AssertUtil.isNotBlank(ossObject[3], "award type cannot be empty");
        if(NumberUtils.isDigits(ossObject[0].trim())){
            roster.setCustomerId(ossObject[0].trim());
        }else{
            roster.setCustomerId(ossObject[0].trim().substring(1));
        }

        AssertUtil.isNumber(roster.getCustomerId(), "customerId is not number");
        roster.setAmount(ossObject[1].trim());
        roster.setCurrency(ossObject[2].trim());
        roster.setRewardType(ossObject[3].trim());

        if (map.containsKey(roster.getCustomerId())) {
            roster.setSeq(getSeq(activityEntity.getType()).getPrefix() + map.get(roster.getCustomerId()) + 1);
            map.put(roster.getCustomerId(), map.get(roster.getCustomerId()) + 1);
        } else {
            roster.setSeq(getSeq(activityEntity.getType()).getInit());
            map.put(roster.getCustomerId(), 1);
        }
        if("Token".equalsIgnoreCase(roster.getRewardType())){
            if(currencyClient.get(roster.getCurrency(), true) == null){
                throw new IllegalArgumentException(roster.getCurrency() + "not found");
            }
        }
        roster.setBatchId(activityBatch.getBatchId());
        roster.setStatus(ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name());
        roster.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        roster.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        return roster;
    }

    @Override
    public boolean support(String activityType) {
        return Objects.equals(ActivityConstant.ActivityTypeEnum.CUSTOMIZE.name(), activityType);
    }

    @Override
    public String[] header() {
        return CSV_HEAD;
    }
}