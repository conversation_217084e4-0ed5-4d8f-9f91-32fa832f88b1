package com.kikitrade.activity.service.mq;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.model.constant.ActivityMessageConstant;
import com.kikitrade.activity.service.business.ActivityCommonService;
import com.kikitrade.activity.service.engine.process.ActivityAutoProcessExecutor;
import com.kikitrade.activity.service.common.LogAlarmConstant;
import com.kikitrade.activity.service.common.LogAlarmUtil;
import com.kikitrade.framework.mybatis.config.SaasContext;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Setter
@Getter
@Slf4j
@Component
public class ActivityCheckMassageListener implements OnsMessageListener {

    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @Resource
    private ActivityAutoProcessExecutor activityAutoProcessExecutor;

    @Resource
    private ActivityCommonService activityCommonService;

    @Resource
    private LogAlarmUtil logAlarmUtil;

    @Resource
    private TopicConfig topicConfig;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {

        ActivityMessage activityMessage;
        boolean flag = false;

        try {
            String body = new String(message.getBody(), "utf-8");
            activityMessage = JSON.parseObject(body, ActivityMessage.class);
            log.info("ActivityCheckMassageListener consume msg {}", JSON.toJSONString(activityMessage));
            //消息格式错误，忽略处理
            if (StringUtils.isBlank(activityMessage.getBusinessId())
                    || activityMessage.getEvent() == null
                    || StringUtils.isBlank(activityMessage.getCustomerId())) {
                log.warn("activitycheckmassagelistener:  wrong message parameters,business_id [{}], event [{}] so skip!", activityMessage.getBusinessId(), activityMessage.getEvent());

                recordSave(activityMessage, ActivityConstant.RecordStatus.FAILED.getCode(), ActivityConstant.MsgStatus.PROCESS_FAILED.getParaMsg("Wrong message format!"));

                return Action.CommitMessage;
            }

            //用户合法性检查
            CustomerDTO customer = null;
            try {
                SaasContext.setDubboSaasId(activityMessage.getSaasId());

                customer = remoteCustomerService.getById(activityMessage.getSaasId(), activityMessage.getCustomerId(), true);
                flag = customer == null ? false : true;
            } catch (Exception e) {
                log.error("activitycheckmassagelistener customer check fail.", e);
                flag = false;
            } finally {
                if (!flag) {
                    log.info("activitycheckmassagelistener customer check fail.");
                    recordSave(activityMessage, ActivityConstant.RecordStatus.FAILED.getCode(), ActivityConstant.MsgStatus.PROCESS_FAILED.getParaMsg("invalid customer!"));
                    return Action.CommitMessage;
                }
            }

            //活动风控检查，忽略处理
            /*try {
                RemoteResult remoteResult = remoteCustomerRiskService.checkCustomerActivityRisk(activityMessage.getCustomerId(), null);
                log.info("activitycheckmassagelistener: customer risk check result [{}] ", JSONObject.toJSONString(remoteResult));
                if (remoteResult != null && remoteResult.getSuccess() && remoteResult.getCode().equals(CodeConstant.CODE_SUCCESS)) {
                    log.info("activitycheckmassagelistener: customer risk check pass ,customer_id [{}] business_id [{}] event [{}] ", activityMessage.getCustomerId(), activityMessage.getBusinessId(), activityMessage.getEvent());
                } else {
                    log.warn("activitycheckmassagelistener: customer risk check fail !");
                    recordSave(activityMessage, ActivityConstant.RecordStatus.FAILED.getCode(), ActivityConstant.MsgStatus.PROCESS_FAILED.getParaMsg("customer risk check fail!"));
                    return Action.CommitMessage;
                }
            } catch (Exception e) {
                log.error("activitycheckmassagelistener: user risk check fail,error msg is {} ", e);
                return Action.ReconsumeLater;
            }*/

            // 填充触发本次活动的一些业务信息
            activityMessage.putBusinessAttr(ActivityMessageConstant.BusinessAttrs.USER_NAME.getName(), customer.getUserName());

            flag = activityAutoProcessExecutor.execute(activityMessage);

        } catch (Exception e) {
            logAlarmUtil.alarm(LogAlarmConstant.CheckPoint.ACTIVITY_MSG_CONSUME_ERROR, "activitycheckmassagelistener: consume msg error fail", e);
            flag = false;
        }

        return flag ? Action.CommitMessage : Action.ReconsumeLater;
    }

    private void recordSave(ActivityMessage activityMassage, Integer status, String remark) {
        try {
            activityCommonService.saveRecords(activityMassage, null, status, remark);
        } catch (Exception e) {
            log.error("activitycheckmassagelistener: saveRecords fail.", e);
        }
    }

    @Override
    public String topic() {
        return topicConfig.getTopicActivity();
    }
}
