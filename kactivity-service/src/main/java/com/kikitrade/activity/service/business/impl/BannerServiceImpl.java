package com.kikitrade.activity.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.kikitrade.activity.api.model.BannerVO;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.BannerBuilder;
import com.kikitrade.activity.dal.tablestore.model.Banner;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.facade.banner.BannerSource;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.service.business.BannerService;
import com.kikitrade.activity.service.goods.GoodsService;
import com.kikitrade.activity.service.task.TaskConfigService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.bean.BeanUtil.*;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/1 15:36
 */
@Service
public class BannerServiceImpl implements BannerService {

    @Resource
    private BannerBuilder bannerBuilder;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private GoodsService goodsService;

    @Override
    public List<BannerVO> queryByLocal(String saasId, String local, String channel) {
        List<Banner> banners = bannerBuilder.getByLocal(saasId, local, channel);
        List<BannerVO> bannerVOS = new ArrayList<>();
        for(Banner banner : banners){
            BannerVO bannerVO = copyProperties(banner, BannerVO.class);
            bannerVO.setAttr(buildMap(banner.getSource(), banner.getSourceId()));
            bannerVOS.add(bannerVO);
        }
        return bannerVOS;
    }

    private Map<String, Object> buildMap(String source, String sourceId){
        Map<String, Object> map = new HashMap<>();
        BannerSource bannerSource = BannerSource.valueOf(source);
        switch (bannerSource){
            case TASK -> {
                TaskConfigDTO configDTO = taskConfigService.findByTaskId(sourceId);
                Award award = configDTO.getReward().get("0").get(0);
                map.put("amount", award.getAmount());
                map.put("currency", award.getCurrency());
                map.put("startTime", configDTO.getStartTime());
                map.put("endTime", configDTO.getEndTime());
            }
            case GOODS -> {
                Goods goods = goodsService.findById(sourceId);
                map.put("amount", goods.getPrice());
                map.put("startTime", goods.getStartTime());
                map.put("endTime", goods.getEndTime());
            }
        }
        return map;
    }
}
