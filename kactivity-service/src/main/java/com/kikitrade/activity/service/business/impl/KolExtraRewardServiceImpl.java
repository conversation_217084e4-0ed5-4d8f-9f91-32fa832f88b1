package com.kikitrade.activity.service.business.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.OSSObject;
import com.csvreader.CsvReader;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.service.business.KolExtraRewardService;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.framework.common.saas.SaasId;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.common.constants.CustomerReferralConstants;
import com.kikitrade.market.client.CurrencyClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KolExtraRewardServiceImpl implements KolExtraRewardService {

    private static char Delimiter = ',';

    // TODO redis
    @Resource
    private RedisService redisService;

    @Resource
    private UploadOssUtil uploadOssUtil;

    @Resource
    private CurrencyClient currencyClient;

    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @Value("${app.oss.activity.kolExtraReward.key}")
    private String kolExtraRewardOssKey;

    @Override
    public boolean reload() {
        redisService.delete(RedisKeyConst.ACTIVITY_KOL_EXTRA_REWARD_KEY.getPrefix());
        return load();
    }

    /**
     * 从OSS加载CSV文件，并上传到redis缓存
     *
     * @return
     */
    private boolean load() {
        try {
            // 从OSS读取文件
            OSSObject ossObject = uploadOssUtil.getObject(uploadOssUtil.absoluteKey(kolExtraRewardOssKey));
            if (ossObject == null) {
                log.error("KolExtraRewardService load fail. ossObject not exist. key:{}", kolExtraRewardOssKey);
                return false;
            }

            // 解析CSV
            Map<String, String> dataMap = new HashMap<>();
            CsvReader csvReader = null;
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent()))) {
                csvReader = new CsvReader(reader, Delimiter);
                csvReader.readHeaders();            // 读表头
                while (csvReader.readRecord()) {    // 读数据
                    putData2Map(csvReader.getValues(), dataMap);
                }
                log.debug("KolExtraRewardService, file read finish. key:{}, dataMap:{}", kolExtraRewardOssKey, JSON.toJSONString(dataMap));
            } catch (Exception e) {
                log.error("KolExtraRewardService, file read exception.", e);
                return false;
            } finally {
                if (csvReader != null) {
                    try {
                        csvReader.close();
                    } catch (Exception e) {
                        log.error("KolExtraRewardService, csvReader close exception.", e);
                    }
                }
            }

            // 加载到redis缓存
            redisService.saveMap(RedisKeyConst.ACTIVITY_KOL_EXTRA_REWARD_KEY.getPrefix(), dataMap);

            return true;
        } catch (Exception e) {
            log.error("KolExtraRewardService load exception.", e);
            return false;
        }
    }

    /**
     * 根据csv数据，构造对应的redis缓存对象，并填充到map中
     *
     * @param columns
     * @param dataMap
     * @throws Exception
     */
    private void putData2Map(String[] columns, Map<String, String> dataMap) throws Exception {
        if (columns == null || columns.length < 3 || StringUtils.isAnyBlank(columns[0], columns[1], columns[2])) {
            throw new ActivityException(ActivityExceptionType.SYSTEM_PARAMETER_INVALID, String.format("csv file data invalid. data:%s", Arrays.toString(columns)));
        }
        String customerName = columns[0].trim();
        String currency = columns[1].trim();
        BigDecimal reward = new BigDecimal(columns[2].trim()); // 这里转换一下，如果格式有问题会抛出异常被上层捕获到
        if (currencyClient.get(currency) == null) {
            throw new ActivityException(ActivityExceptionType.SYSTEM_PARAMETER_INVALID, String.format("csv file data invalid. currency:%s ExProduct not found", currency));
        }
        if (reward.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ActivityException(ActivityExceptionType.SYSTEM_PARAMETER_INVALID, String.format("csv file data invalid. reward:%s <= 0", reward.toPlainString()));
        }
        CustomerDTO customer = remoteCustomerService.getByUserName(SaasId.KIKI.id(), customerName);
        if (customer != null) {
            dataMap.put(customer.getId() + "_" + currency, reward.toPlainString());
        } else {
            log.error("KolExtraRewardServiceImpl, putData2Map fail. customer[{}] saasId[{}] not find.", customerName, SaasId.KIKI.id());
        }
    }

    @Override
    public Map<String, BigDecimal> getByKolCustomerId(String customerId) {
        if (StringUtils.isBlank(customerId) || StringUtils.equalsIgnoreCase(CustomerReferralConstants.EMPTY_ID, customerId)) {
            return new HashMap<>();
        }
        Map<String, String> dataMap = redisService.getMap(RedisKeyConst.ACTIVITY_KOL_EXTRA_REWARD_KEY.getPrefix());
        if (dataMap == null || dataMap.isEmpty()) {
            reload();
            dataMap = redisService.getMap(RedisKeyConst.ACTIVITY_KOL_EXTRA_REWARD_KEY.getPrefix());
        }
        return dataMap.entrySet().stream().filter(stringStringEntry -> stringStringEntry.getKey().startsWith(customerId + "_"))
                .collect(Collectors.toMap(keyEntry -> StringUtils.substringAfter(keyEntry.getKey(), customerId + "_"), valueEntry -> new BigDecimal(valueEntry.getValue())));
    }
}
