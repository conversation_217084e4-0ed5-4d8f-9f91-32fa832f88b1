package com.kikitrade.activity.service.rpc.twitter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.framework.redis.ratelimiter.RedisCounterRateLimiter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.kikitrade.activity.model.response.ActivityResponseCode.ACTIVITY_HOT;

@Slf4j
public class TwitterApiUtil {

    private KactivityProperties kactivityProperties;
    private RedisCounterRateLimiter rateLimiter;

    public TwitterApiUtil(KactivityProperties kactivityProperties, RedisCounterRateLimiter rateLimiter) {
        this.kactivityProperties = kactivityProperties;
        this.rateLimiter = rateLimiter;
    }

    /**
     * 判断两个twitter用户间的关系
     * curl --request GET \
     *   --url 'https://api.twitterapi.io/twitter/user/check_follow_relationship?source_user_name=1&target_user_name=2' \
     *   --header 'X-API-Key: e33e4933ec684e1eab43936cf95572d3'
     *
     * @param sourceUserName 源用户ID
     * @param targetUserName 目标用户ID
     * @return true表示源用户关注了目标用户，false表示没有关注
     * @throws ActivityException 如果请求失败或解析响应失败
     */
    public boolean isFollowing(String sourceUserName, String targetUserName) throws ActivityException {
        try {
            if(kactivityProperties.getLimiter() != null && kactivityProperties.getLimiter().get("twitterFollowing") != null) {
                String key = "TWITTER_FOLLOWING:LIMIT:" + sourceUserName;
                boolean acquire = rateLimiter.tryAcquire(key, (int)TimeUnit.DAYS.toMillis(1), Integer.parseInt(kactivityProperties.getLimiter().get("twitterFollowing")));
                if(!acquire){
                    throw new ActivityException(ACTIVITY_HOT, "twitter api request too much");
                }
            }

            // 构建请求URL
            String url = String.format("https://api.twitterapi.io/twitter/user/check_follow_relationship?source_user_name=%s&target_user_name=%s",
                sourceUserName, targetUserName);

            // 创建请求
            Request request = new Request.Builder()
                .url(url)
                .header("X-API-Key", this.kactivityProperties.getTwitterApiKey())
                .build();

            // 使用HttpPoolUtil的xHttpClient发送请求
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();

            if (!response.isSuccessful()) {
                throw new ActivityException(ActivityResponseCode.TWITTER_API_ERROR,
                    "Twitter API request failed: " + response.code());
            }

            // 解析响应
            String responseBody = response.body().string();
            JSONObject json = JSON.parseObject(responseBody);
            JSONObject data = json.getJSONObject("data");
            return data != null && data.getBoolean("following");

        } catch (ActivityException e) {
            log.error("Error checking Twitter follow relationship", e);
            throw e;
        } catch (Exception e) {
            log.error("Error checking Twitter follow relationship", e);
            throw new ActivityException(ActivityResponseCode.TWITTER_API_ERROR,
                "Failed to check Twitter follow relationship: " + e.getMessage());
        }
    }

    /**
     * 返回今天 UTC 0 点的时间字符串，格式为 YYYY-MM-DD_00:00:00_UTC
     * @return 今天 UTC 0 点的时间字符串
     */
    private String getTodayUTCMidnight() {
        try {
            // 获取当前 UTC 日期
            LocalDate today = LocalDate.now(ZoneId.of("UTC"));
            // 格式化日期为指定格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_00:00:00");
            return today.format(formatter) + "_UTC";
        } catch (Exception e) {
            log.error("Error getting today's UTC midnight", e);
            return null;
        }
    }

    /**
     * 查询我最近的发帖信息
     * curl --request GET \
     *   --url 'https://api.twitterapi.io/twitter/tweet/advanced_search?queryType=Latest&query=from%3Abruce_lee0338' \
     *   --header 'X-API-Key: e33e4933ec684e1eab43936cf95572d3'
     */
    public String searchTweet(String sourceUserName, String keyWord, String taskId) throws ActivityException {
        try {
            if(kactivityProperties.getLimiter() != null && kactivityProperties.getLimiter().get("twitterPost") != null) {
                String key = "TWITTER_POST:LIMIT:" + sourceUserName + ":" + taskId;
                boolean acquire = rateLimiter.tryAcquire(key, (int)TimeUnit.DAYS.toMillis(1), Integer.parseInt(kactivityProperties.getLimiter().get("twitterPost")));
                if(!acquire){
                    throw new ActivityException(ACTIVITY_HOT, "twitter api request too much");
                }
            }
            String todayUTCMidnight = getTodayUTCMidnight();
            // 构建请求URL
            String url = "https://api.twitterapi.io/twitter/tweet/advanced_search?queryType=Latest&query="+keyWord+"%20from%3A"+sourceUserName+"%20since%3A"+todayUTCMidnight;
            log.info("getLatestTweet url:{}", url);
            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("X-API-Key", this.kactivityProperties.getTwitterApiKey())
                    .build();

            // 使用HttpPoolUtil的xHttpClient发送请求
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();

            if (!response.isSuccessful()) {
                throw new ActivityException(ActivityResponseCode.TWITTER_API_ERROR,
                        "Twitter API request failed: " + response.code());
            }
            // 解析响应
            String responseBody = response.body().string();
            log.info("getLatestTweet responseBody:{}", responseBody);
            JSONObject data = JSON.parseObject(responseBody);
            JSONArray tweets = data.getJSONArray("tweets");
            if(tweets.isEmpty()){
                return null;
            }
            for(Tweet tweet : tweets.toJavaList(Tweet.class)){
                return tweet.getId();
            }
            return null;
        } catch (ActivityException e) {
            log.error("Error checking Twitter follow relationship", e);
            throw e;
        } catch (Exception e) {
            log.error("Error getting latest tweet", e);
            throw new ActivityException(ActivityResponseCode.TWITTER_API_ERROR,
                    "Failed to get latest tweet: " + e.getMessage());
        }
    }

    /**
     * 查询我最近的转贴
     * curl --request GET \
     *   --url 'https://api.twitterapi.io/twitter/tweet/advanced_search?queryType=Latest&query=from%3Abruce_lee0338' \
     *   --header 'X-API-Key: e33e4933ec684e1eab43936cf95572d3'
     */
    public String searchRetweet(String sourceUserName, String targetUserName, String taskId) throws ActivityException {
        try {
            if(kactivityProperties.getLimiter() != null && kactivityProperties.getLimiter().get("twitterRetweet") != null) {
                String key = "TWITTER_RETWEET:LIMIT:" + sourceUserName + ":" + taskId;
                boolean acquire = rateLimiter.tryAcquire(key, (int)TimeUnit.DAYS.toMillis(1), Integer.parseInt(kactivityProperties.getLimiter().get("twitterRetweet")));
                if(!acquire){
                    throw new ActivityException(ACTIVITY_HOT, "twitter api request too much");
                }
            }
            String todayUTCMidnight = getTodayUTCMidnight();
            // 构建请求URL
            String url = "https://api.twitterapi.io/twitter/tweet/advanced_search?queryType=Latest&query=filter:nativeretweets%20from%3A"+sourceUserName+"%20since%3A"+todayUTCMidnight;
            log.info("getLatestTweet url:{}", url);
            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("X-API-Key", this.kactivityProperties.getTwitterApiKey())
                    .build();

            // 使用HttpPoolUtil的xHttpClient发送请求
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();

            if (!response.isSuccessful()) {
                throw new ActivityException(ActivityResponseCode.TWITTER_API_ERROR,
                        "Twitter API request failed: " + response.code());
            }
            // 解析响应
            String responseBody = response.body().string();
            log.info("getLatestTweet responseBody:{}", responseBody);
            JSONObject data = JSON.parseObject(responseBody);
            JSONArray tweets = data.getJSONArray("tweets");
            for(Tweet tweet : tweets.toJavaList(Tweet.class)){
                if(tweet.getRetweeted_tweet() != null && targetUserName.equals(tweet.getRetweeted_tweet().getAuthor().getUserName())){
                    return tweet.getId();
                }
            }
            return null;
        } catch (ActivityException e) {
            log.error("Error checking Twitter follow relationship", e);
            throw e;
        } catch (Exception e) {
            log.error("Error getting latest tweet", e);
            throw new ActivityException(ActivityResponseCode.TWITTER_API_ERROR,
                    "Failed to get latest tweet: " + e.getMessage());
        }
    }

    @Data
    public static class Tweet {
        private String type;
        private String id;
        private String createdAt;
        private RetweetedTweet retweeted_tweet;
        private Object article;

        @Data
        public static class RetweetedTweet {
            private String url;
            private Author author;
        }

        @Data
        public static class Author {
            private String userName;
            private String url;
            private String id;
            private String name;
        }
    }
}
