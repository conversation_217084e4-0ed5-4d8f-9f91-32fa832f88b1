package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.dao.ActivityMaterialDao;
import com.kikitrade.activity.dal.mysql.model.ActivityMaterial;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityMaterialService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.model.ActivityMaterialVO;
import com.kikitrade.activity.service.model.MaterialVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class ActivityMaterialServiceImpl implements ActivityMaterialService {

    @Resource
    private ActivityMaterialDao activityMaterialDao;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private RedisService redisService;

    private static final String ACTIVITY_MATERIAL_OFFLINE_KEY = "_status";
    private static final String ACTIVITY_MATERIAL_STATUS_KEY = "status";
    /**
     * 保存物料
     *
     * @param activityMaterialVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result save(ActivityMaterialVO activityMaterialVO) {
        List<ActivityMaterial> activityMaterialList = new ArrayList<>();

        for(MaterialVO materialVO : activityMaterialVO.getMaterialVOList()){
            if(StringUtils.isBlank(materialVO.getValue())){
                continue;
            }
            ActivityMaterial material = new ActivityMaterial();
            material.setId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(ActivityMaterial.getTableName())));
            material.setActivityId(activityMaterialVO.getActivityId());
            material.setCode(materialVO.getCode());
            material.setValue(materialVO.getValue());
            material.setStatus("Active".equalsIgnoreCase(activityMaterialVO.getTemplateStatus()) ? ActivityConstant.ValidStatus.VALID.getStatus() : ActivityConstant.ValidStatus.INVALID.getStatus());
            material.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            material.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            material.setSaasId(kactivityProperties.getSaasId());
            activityMaterialList.add(material);
        }

        ActivityMaterial material = new ActivityMaterial();
        material.setActivityId(activityMaterialVO.getActivityId());
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_MATERIAL_VALUE_KEY.getPrefix(), activityMaterialVO.getActivityId());
        //删除缓存
        redisService.del(key);
        activityMaterialDao.delete(material);
        activityMaterialDao.batchInsert(activityMaterialList);
        //删除缓存
        redisService.del(key);
        return new Result();
    }

    /**
     * 查询物料信息
     *
     * @return
     */
    @Override
    public List<ActivityMaterial> findByActivityId(String activityId) {
        ActivityMaterial activityMaterial = new ActivityMaterial();
        activityMaterial.setActivityId(activityId);
        activityMaterial.setStatus(ActivityConstant.ValidStatus.VALID.getStatus());
        return activityMaterialDao.select(activityMaterial);
    }

    /**
     * 从缓存中查询物料信息
     *
     * @param activityId
     * @return
     */
    @Override
    public Optional<Map<String,ActivityMaterial>> findByActivityIdFromCache(String activityId) {
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_MATERIAL_VALUE_KEY.getPrefix(), activityId);
        Map<String,String> materialCache = redisService.hGetAllMap(key);
        if(MapUtils.isNotEmpty(materialCache)){
            //如果存在特殊key，直接返回空
            if(redisService.hHasKey(key, ACTIVITY_MATERIAL_OFFLINE_KEY)){
                return Optional.empty();
            }
            //解析缓存信息
            Map<String, ActivityMaterial> result = new HashMap<>();
            for(Map.Entry<String,String> entry : materialCache.entrySet()){
                result.put(entry.getKey(), JSON.parseObject(entry.getValue(), ActivityMaterial.class));
            }
            return Optional.of(result);
        }
        //查询数据库
        List<ActivityMaterial> materials = this.findByActivityId(activityId);
        if(CollectionUtils.isEmpty(materials)){
            //如果空，设置特殊key
            redisService.hSet(key, ACTIVITY_MATERIAL_OFFLINE_KEY, ActivityConstant.ActivityMaterialStatus.EMPTY.getStatus());
            return Optional.empty();
        }
        Map<String, ActivityMaterial> materialMap = materials.stream().collect(Collectors.toMap(ActivityMaterial::getCode, Function.identity(), (v1, v2) -> v2));
        //根据状态排除下线的物料
        if(MapUtils.isEmpty(doFilter(materialMap))){
            redisService.hSet(key, ACTIVITY_MATERIAL_OFFLINE_KEY, ActivityConstant.ActivityMaterialStatus.EMPTY.getStatus());
            return Optional.empty();
        }
        //有效物料存入缓存
        Map<String, String> materialSetCache = new HashMap<>();
        for(Map.Entry<String, ActivityMaterial> materialEntry : materialMap.entrySet()){
            materialSetCache.put(materialEntry.getKey(), JSON.toJSONString(materialEntry.getValue()));
        }
        redisService.hSetAll(key, materialSetCache);
        return Optional.of(materialMap);
    }

    /**
     * 根据某些key过滤
     * @param materialMap
     * @return
     */
    private Map<String,ActivityMaterial> doFilter(Map<String,ActivityMaterial> materialMap){
        if(materialMap.get(ACTIVITY_MATERIAL_STATUS_KEY) == null){
            return materialMap;
        }
        if(ActivityConstant.ActivityMaterialStatus.UNABLE.isEquals(materialMap.get(ACTIVITY_MATERIAL_STATUS_KEY).getValue())){
            return null;
        }
        return materialMap;
    }
}
