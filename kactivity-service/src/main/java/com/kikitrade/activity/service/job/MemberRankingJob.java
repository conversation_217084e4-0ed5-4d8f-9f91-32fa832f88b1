package com.kikitrade.activity.service.job;

import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.common.config.RankingProperties;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.elasticjob.KiKiSimpleJob;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.QuestsLadderDTO;
import com.kikitrade.member.model.request.QuestLadderRequest;
import com.kikitrade.member.model.request.QuestLadderTokenPageRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/7 17:10
 */
@Component
@Slf4j
public class MemberRankingJob extends KiKiSimpleJob {

    @DubboReference(check = false)
    @Lazy
    private RemoteMemberService remoteMemberService;
    @Resource
    @Lazy
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    @Lazy
    private RewardService rewardService;
    @Resource
    @Lazy
    private RedisService redisService;
    @Resource
    @Lazy
    private NoticeService noticeService;

    private static final String DINGTALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=53f9e144057599dc640203a13f385167e12a757fdee7c0b45ec0a871c16eff52";

    private static final String SEASON = "season";

    private static final String LEADERBOARD = "Leaderboard";

    @Override
    protected void doExecute(ShardingContext shardingContext) throws Exception {
        ex();
    }

    public void ex(){
        try{
            log.info("MemberRankingJob start");
            Long season1StartTime = kactivityModuleProperties.getRank().getSeasonStartTime();
            log.info("MemberRankingJob season1StartTime: {}", season1StartTime);
            LocalDate season1LocalDate = new Date(season1StartTime).toInstant().atOffset(ZoneOffset.UTC).toLocalDate();
            LocalDateTime seasonStartTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0);
            LocalDateTime seasonEndTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDate().with(TemporalAdjusters.firstDayOfNextMonth()).atTime(0, 0, 0);
            //判断与season1间隔几个月，得知当前第几赛季
            int seasonIdx = Period.between(season1LocalDate, seasonStartTime.toLocalDate()).getYears() * 12 + Period.between(season1LocalDate, seasonStartTime.toLocalDate()).getMonths() + 1;
            String currentSeasonName = SEASON + seasonIdx;
            log.info("MemberRankingJob currentSeasonName:{}, seasonStartTime: {}, seasonEndTime: {}", currentSeasonName, seasonStartTime, seasonEndTime);
            long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.WEDNESDAY)).toInstant(ZoneOffset.UTC)).getTime();
            if(OffsetDateTime.now().toEpochSecond() * 1000L < firstLadderTime + 10 * 60 * 1000){
                log.info("MemberRankingJob not to time");
                return;
            }
            //计算获取cycle
            long c = 1;
            if(OffsetDateTime.now().toEpochSecond() * 1000L >= firstLadderTime){
                c = ((OffsetDateTime.now().toEpochSecond() * 1000L  - firstLadderTime)/1000 / 60 / 60 / 24 / 7) + 2;
            }

            String cycle = String.format("%02d", c);
            QuestLadderTokenPageRequest request = new QuestLadderTokenPageRequest();
            request.setSaasId(kactivityModuleProperties.getRank().getSaasId());
            request.setSeason(currentSeasonName);
            request.setLimit(kactivityModuleProperties.getRank().getLimit());
            request.setCycle(cycle);

            if(redisService.hHasKey(RedisKeyConst.ACTIVITY_MEMBER_RANK.getPrefix(), request.getSeason() + request.getCycle())){
                log.info("MemberRankingJob {}已发放过徽章", request.getSeason() + request.getCycle());
                return;
            }
            for(int level = kactivityModuleProperties.getRank().getLevel(); level >= 2; level--){
                request.setLevel(level);
                TokenPage<QuestsLadderDTO> pageLadderResults;
                String nextToken = null;
                do{
                    request.setNextToken(nextToken);
                    pageLadderResults = remoteMemberService.pageLaddersByLevel(request);
                    if(CollectionUtils.isEmpty(pageLadderResults.getRows())){
                        break;
                    }
                    if(nextToken == null){
                        redisService.hSet(RedisKeyConst.ACTIVITY_MEMBER_RANK.getPrefix(), request.getSeason() + request.getCycle(), true);
                        String key = String.format("%s%s%s", request.getSaasId(), request.getSeason(), request.getCycle());
                        noticeService.noticeDingTalk(key, DINGTALK_URL, "somon 徽章开始发放");
                    }
                    nextToken = pageLadderResults.getNextToken();
                    reward(pageLadderResults.getRows(), seasonIdx, seasonEndTime, c, cycle);
                }while (pageLadderResults.getNextToken() != null);
                String key = String.format("%s%s%s%s",request.getSaasId(), request.getSeason(), request.getCycle(), level);
                noticeService.noticeDingTalk(key, DINGTALK_URL, "somon " + level + " 徽章发放完成");
            }
        }catch (Exception ex){
            log.error("MemberRankingJob", ex);
        }
    }

    private void reward(List<QuestsLadderDTO> ladderDTOS, int seasonIdx, LocalDateTime seasonEndTime, long cycle, String cycleStr) {
        if(CollectionUtils.isEmpty(ladderDTOS)){
            return;
        }
        for(QuestsLadderDTO dto : ladderDTOS){
            if(dto.getCustomerId().startsWith("0x")){
                continue;
            }
            // 第一赛季 seasonId传1、level 2~6 代表白银～王者
            String badgeInfo = seasonIdx + "_" + dto.getLevel() + "_" + LEADERBOARD;
            RewardRequest rewardRequest = RewardRequest.builder()
                    .customerId(dto.getCustomerId())
                    .rewardId(String.format("%s%03d%s", dto.getCustomerId(), seasonIdx, cycleStr))
                    .amount(BigDecimal.ONE)
                    .currency(badgeInfo)
                    .type(ActivityConstant.AwardTypeEnum.BADGE.name())
                    .businessType(AssetBusinessType.ACTIVITY_TASK.getCodeDesc())
                    .receiveEndTime(seasonEndTime.toEpochSecond(ZoneOffset.UTC))
                    .desc("S"+ seasonIdx +" week " + (cycle - 1))
                    .saasId(kactivityModuleProperties.getRank().getSaasId())
                    .build();
            try{
                log.info("[reward] ranking badge:{}", rewardRequest);
                rewardService.reward(rewardRequest);
            }catch (Exception ex){
                log.error("[reward] ranking badge:{}", rewardRequest, ex);
            }
        }
    }
}
