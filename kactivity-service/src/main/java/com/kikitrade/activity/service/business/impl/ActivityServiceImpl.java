package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.model.ActivityActionDTO;
import com.kikitrade.activity.api.model.ActivityDTO;
import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.api.model.ActivityRuleDTO;
import com.kikitrade.activity.dal.mysql.dao.ActivityContentsDao;
import com.kikitrade.activity.dal.mysql.dao.ActivityDao;
import com.kikitrade.activity.dal.mysql.model.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityEventMassage;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.service.business.ActivityService;
import com.kikitrade.activity.service.business.SchedLogService;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.engine.process.ActivityAsyncEventBus;
import com.kikitrade.activity.service.meta.ActivityActionMapService;
import com.kikitrade.activity.service.meta.ActivityActionsService;
import com.kikitrade.activity.service.meta.ActivityRuleMapService;
import com.kikitrade.activity.service.meta.ActivityRulesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityServiceImpl implements ActivityService {

    @Resource
    private ActivityDao activityDao;
    @Resource
    private ActivityContentsDao activityContentsDao;
    @Resource
    private ActivityActionMapService activityActionMapService;
    @Resource
    private ActivityRuleMapService activityRuleMapService;
    @Resource
    private ActivityActionsService activityActionsService;
    @Resource
    private ActivityRulesService activityRulesService;
    @Resource
    @Lazy
    private ActivityAsyncEventBus activityAsyncEventBus;
    @Resource
    private SchedLogService schedLogService;
    @Resource
    private KactivityProperties kactivityProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult save(Activity activity) throws Exception {
        JsonResult result = new JsonResult();
        //  do some convert;
        try {
            activity.setStatus(ActivityConstant.Status.CREATED.getCode());
            Date date = TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
            activity.setCreated(date);
            activity.setModified(date);
            int count = activityDao.insert(activity);
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityserviceimpl activitydao insert process failed.", e);
            throw e;
        }
        if (result.getSuccess()) {
            //save activity_rule_map
            List<ActivityRuleMap> activityRuleMapList = activity.getRuleConfig();
            //set activity id
            for (ActivityRuleMap ruleMap : activityRuleMapList) {
                result = activityRulesService.findById(ruleMap.getRule_id());
                if (result.getSuccess() && result.getObj() != null) {
                    ActivityRules rulpara = (ActivityRules) result.getObj();
                    ruleMap.setActivity_id(activity.getId());
                    ruleMap.setPriority(rulpara.getPriority());
                    ruleMap.setStatus(rulpara.getStatus());
                    ruleMap.setRule_name(rulpara.getRule_name());
                } else {
                    throw new Exception(result.getMsg());
                }

            }

            if (activityRuleMapList != null && activityRuleMapList.size() > 0) {
                result = activityRuleMapService.batchInsert(activityRuleMapList);
                if (!result.getSuccess()) {

                    throw new Exception(result.getMsg());
                }
            }

            //save activity_action_map
            List<ActivityActionMap> activityActionMapList = activity.getActionConfig();
            //set activity id
            for (ActivityActionMap actionMap : activityActionMapList) {
                actionMap.setActivity_id(activity.getId());
                result = activityActionsService.findById(actionMap.getAction_id());
                if (result.getSuccess() && result.getObj() != null) {
                    ActivityActions rulpara = (ActivityActions) result.getObj();
                    actionMap.setActivity_id(activity.getId());
                    actionMap.setPriority(rulpara.getPriority());
                    actionMap.setStatus(rulpara.getStatus());
                    actionMap.setAction_name(rulpara.getAction_name());
                } else {
                    throw new Exception(result.getMsg());
                }
            }
            if (activityActionMapList != null && activityActionMapList.size() > 0) {
                result = activityActionMapService.batchInsert(activityActionMapList);
                if (!result.getSuccess()) {
                    throw new Exception(result.getMsg());
                }
            }
            result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult update(Activity activity) throws Exception {
        JsonResult result = new JsonResult();
        int count = 0;
        Activity act = activityDao.findById(activity.getId());
        log.info("activityserviceimpl before activitydao update :{}", JSONObject.toJSONString(act));
        try {
            if (act == null) {
                result.setSuccess(false).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                count = activityDao.update(activity);
            }
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }

        } catch (Exception e) {
            log.error("activityserviceimpl activitydao update process failed.", e);
            throw e;
        }

        if (count > 0 && result.getSuccess()) {
            //update activity_rule_map

            List<ActivityRuleMap> activityRuleMapList = activity.getRuleConfig();
            //set activity id
            for (ActivityRuleMap ruleMap : activityRuleMapList) {
                result = activityRulesService.findById(ruleMap.getRule_id());
                if (result.getSuccess() && result.getObj() != null) {
                    ActivityRules rulpara = (ActivityRules) result.getObj();
                    ruleMap.setActivity_id(activity.getId());
                    ruleMap.setPriority(rulpara.getPriority());
                    ruleMap.setStatus(rulpara.getStatus());
                    ruleMap.setRule_name(rulpara.getRule_name());
                } else {
                    throw new Exception(result.getMsg());
                }
            }
            if (activityRuleMapList != null && activityRuleMapList.size() > 0) {
                //delete first then insert
                result = activityRuleMapService.delete(activity.getId());
                if (result.getSuccess()) {
                    result = activityRuleMapService.batchInsert(activityRuleMapList);

                    if (!result.getSuccess()) {
                        throw new Exception(result.getMsg());
                    }
                }
            }

            //update activity_action_map
            List<ActivityActionMap> activityActionMapList = activity.getActionConfig();
            //set activity id
            for (ActivityActionMap actionMap : activityActionMapList) {
                actionMap.setActivity_id(activity.getId());
                result = activityActionsService.findById(actionMap.getAction_id());
                if (result.getSuccess() && result.getObj() != null) {
                    ActivityActions rulpara = (ActivityActions) result.getObj();
                    actionMap.setActivity_id(activity.getId());
                    actionMap.setPriority(rulpara.getPriority());
                    actionMap.setStatus(rulpara.getStatus());
                    actionMap.setAction_name(rulpara.getAction_name());
                } else {
                    throw new Exception(result.getMsg());
                }
            }
            if (activityActionMapList != null && activityActionMapList.size() > 0) {
                //delete first then insert
                result = activityActionMapService.delete(activity.getId());
                if (result.getSuccess()) {
                    result = activityActionMapService.batchInsert(activityActionMapList);
                    if (!result.getSuccess()) {
                        throw new Exception(result.getMsg());
                    }
                }
            }
            result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
        }

        return result;
    }

    @Override
    public JsonResult findById(Integer id) {

        JsonResult result = new JsonResult();
        try {
            Activity activity = activityDao.findById(id);
            if (activity == null) {
                result.setObj(activity).setSuccess(false).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityserviceimpl findById process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;

    }

    @Override
    public JsonResult findDetailById(Integer activity_id) {

        JsonResult result = new JsonResult();
        try {
            //Query activity
            result = findById(activity_id);

            if (result.getSuccess()) {
                Activity activity = (Activity) result.getObj();
                //Query activity rule map
                List<ActivityRuleMap> ruleMapList = activityRuleMapService.findByActivityId(activity_id);
                activity.setRuleConfig(ruleMapList);

                //Query activity action map
                List<ActivityActionMap> actionMapList = activityActionMapService.findByActivityId(activity_id);
                activity.setActionConfig(actionMapList);

                result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityserviceimpl findDetailById process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(Integer activityId, boolean flag) throws Exception {
        JsonResult result = new JsonResult();
        Activity activity;
        try {
            activity = activityDao.findById(activityId);
        } catch (Exception e) {
            log.error("activitydao findById process failed.", e);
            return result.setSuccess(false).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
        }
        if (activity != null && activity.getStatus() != ActivityConstant.Status.PROCESSING.getCode() &&
                activity.getStatus() != ActivityConstant.Status.PAUSE.getCode()) {

            if (!flag) {
                //逻辑删除
                try {
                    int count = activityDao.updateStatus(activityId, ActivityConstant.Status.INVALID.getCode());
                    if (count > 0) {
                        activity.setStatus(ActivityConstant.Status.INVALID.getCode());
                        result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
                    } else {
                        return result.setSuccess(false).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
                    }
                } catch (Exception e) {
                    log.error("activitydao updateStatus process failed.", e);
                    throw e;
                }
            } else {
                //物理删除
                try {
                    Integer count = activityDao.deleteByPrimaryKey(activityId);
                    if (count > 0) {
                        result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
                    }
                    if (result.getSuccess()) {
                        result = activityRuleMapService.delete(activityId);
                    }
                    if (result.getSuccess()) {
                        result = activityActionMapService.delete(activityId);
                    }
                } catch (Exception e) {
                    log.error("activitydao deleteById process failed.", e);
                    throw e;
                }
            }
            result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());

        } else {
            result.setObj(activity).setSuccess(false).setCode(ActivityExceptionType.CAN_NOT_BE_DELETE.getCode()).setMsg(ActivityExceptionType.CAN_NOT_BE_DELETE.getParaMsg("activity Id [" + activityId + "]"));
        }

        if (!result.getSuccess()) {
            throw new Exception(result.getMsg());
        }
        return result;
    }


    @Override
    public JsonResult findAll(String saasId, Integer offset, Integer limit, boolean flag, Integer type) {

        JsonResult result = new JsonResult();
        try {
            List<Activity> activityList = activityDao.findAll(saasId, offset, limit, flag, type);
            if (activityList == null || activityList.size() == 0) {
                result.setObj(activityList).setSuccess(false).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityList).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityserviceimpl findAll process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;

    }

    @Override
    public JsonResult findForStatsUpdate() {

        JsonResult result = new JsonResult();
        try {
            List<Activity> activityList = activityDao.findForStatsUpdate();
            if (activityList == null || activityList.size() == 0) {
                result.setObj(activityList).setSuccess(false).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityList).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityserviceimpl findForStatsUpdate process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult updateStatus(Integer id, Integer status) throws Exception {

        JsonResult result = new JsonResult();
        try {
            int count = activityDao.updateStatus(id, status);
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityserviceimpl updateStatus process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }
        return result;
    }


    @Override
    public Activity findActiveByTypeAndLocale(String saasId, Integer type, String locale) {
        Activity activity = activityDao.queryActivityByTypeAndStatus(saasId, type, Arrays.asList(ActivityConstant.Status.PROCESSING.getCode()));//进行中
        if (activity == null) {
            log.warn("No processing activities， type:{}", type);
            return null;
        }
        ActivityContents content = activityContentsDao.findByPara(activity.getId(), locale);
        if (content != null) {
            activity.setContent(content.getContent());
            activity.setLocale(locale);
            activity.setName(content.getName());
            activity.setUrl(content.getUrl());
        }

        return activity;
    }

    @Override
    public ActivityResponse activityManualReward(ActivityEventMassage activityEventMassage) {
        ActivityResponse result = ActivityResponse.builder().success(false).build();
        log.info("activity manual reward activityEventMassage:{}", JSONObject.toJSONString(activityEventMassage));

        if (activityEventMassage.getId() != null && activityEventMassage.getType().equals(ActivityConstant.RewardType.MANUAL_REWARD.getCode())) {
            try {
                activityAsyncEventBus.pushActivityEvent(activityEventMassage);
                result = ActivityResponse.builder().success(true).code(ActivityExceptionType.EXECUTION_SUCCEED.getCode())
                        .msg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage()).build();
            } catch (Exception e) {
                log.error("activity manual reward process failed.", e);
                result = ActivityResponse.builder().success(false).code(ActivityExceptionType.UNKNOWN_REASON.getCode())
                        .msg(ActivityExceptionType.UNKNOWN_REASON.getMessage()).build();
            }
        }
        log.info("ActivityResponse return :{}", JSONObject.toJSONString(result));
        return result;
    }

    @Override
    public ActivityResponse activityDataProcess(ActivityEventMassage activityEventMassage) {
        ActivityResponse result = new ActivityResponse();
        log.info("activity data process activityEventMassage:{}", JSONObject.toJSONString(activityEventMassage));

        if (activityEventMassage.getId() != null && activityEventMassage.getType().equals(ActivityConstant.RewardType.DATA_PROCESS.getCode())) {
            try {
                JsonResult jsonResult = findById(activityEventMassage.getId());
                if (jsonResult != null && jsonResult.getSuccess() && jsonResult.getObj() != null) {

                    Activity activity = (Activity) jsonResult.getObj();
                    if (activity == null) {
                        return ActivityResponse.builder().success(false).code(ActivityExceptionType.UNKNOWN_REASON.getCode()).msg("invalid activity id !").build();
                    }

                    if (activityEventMassage.isAutoDataPrepare()) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(activity.getEnd_time());
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                        String batchPt = TimeUtil.getUtcTime(calendar.getTime(), new SimpleDateFormat("yyyyMMdd"));
                        SchedLog schedLog = schedLogService.findByBatch(batchPt, activityEventMassage.getJobName());
                        if (schedLog == null) {
                            log.warn("not find[{}] schedlog ! batch_pt={}", activityEventMassage.getJobName(), batchPt);
                            return ActivityResponse.builder().success(false).code(ActivityExceptionType.UNKNOWN_REASON.getCode()).msg("not find schedlog !").build();
                        }
                    }
                }

                activityAsyncEventBus.pushActivityEvent(activityEventMassage);
                return ActivityResponse.builder().success(true).code(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).msg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage()).build();
            } catch (Exception e) {
                log.error("activity data process failed.", e);
                return ActivityResponse.builder().success(false).code(ActivityExceptionType.UNKNOWN_REASON.getCode()).msg(ActivityExceptionType.UNKNOWN_REASON.getMessage()).build();
            }
        }

        log.info("ActivityResponse return :{}", JSONObject.toJSONString(result));
        return ActivityResponse.builder().success(false).code(ActivityExceptionType.UNKNOWN_REASON.getCode()).msg("活动id无效或非手动发奖活动！").build();
    }

    @Override
    public ActivityDTO findActivity(Integer type, String locale) {
        Activity activity = findActiveByTypeAndLocale(kactivityProperties.getSaasId(), type, locale);
        List<ActivityRuleMap> ruleMap = activityRuleMapService.findByActivityId(activity.getId());
        List<ActivityActionMap> actionMap = activityActionMapService.findByActivityId(activity.getId());

        return translate(activity, ruleMap, actionMap);
    }

    public ActivityDTO translate(Activity activity, List<ActivityRuleMap> rules, List<ActivityActionMap> actions) {
        ActivityDTO activityDTO = new ActivityDTO();
        BeanUtils.copyProperties(activity, activityDTO);

        List<ActivityRuleDTO> ruleMap = rules.stream().map(rule -> {
            ActivityRuleDTO activityRuleDTO = new ActivityRuleDTO();
            BeanUtils.copyProperties(rule, activityRuleDTO);
            return activityRuleDTO;
        }).collect(Collectors.toList());

        List<ActivityActionDTO> actionMap = actions.stream().map(action -> {
            ActivityActionDTO activityActionDTO = new ActivityActionDTO();
            BeanUtils.copyProperties(action, activityActionDTO);
            return activityActionDTO;
        }).collect(Collectors.toList());

        activityDTO.setRuleConfig(ruleMap);
        activityDTO.setActionConfig(actionMap);

        return activityDTO;
    }

}
