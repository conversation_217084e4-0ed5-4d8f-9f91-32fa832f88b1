package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.PutObjectResult;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.common.CronUtil;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.service.config.KactivityTextConfig;
import com.kikitrade.activity.service.job.ActivityImportJob;
import com.kikitrade.activity.service.job.ActivityRewardJob;
import com.kikitrade.activity.service.job.ElasticJobService;
import com.kikitrade.framework.ots.RangeResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service("rewardCsvService")
public class RewardCsvServiceImpl extends CsvCommonServiceImpl<ActivityCustomReward> {

    @Value("${batch.csv.head}")
    private String head;
    @Value("${batch.csv.show-head}")
    private String showHead;
    @Value("${batch.csv.path}")
    private String path;

    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private UploadOssUtil uploadOssUtil;
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ElasticJobService elasticJobService;
    @Resource
    private ActivityRewardJob activityRewardJob;
    @Resource
    private ActivityImportJob activityImportJob;

    @Override
    List<String> head(String activityType) {
        return Arrays.asList(KactivityTextConfig.getCsvHead(activityType).split(","));
    }

    @Override
    List<String> showHead(String activityType) {
        return Arrays.asList(KactivityTextConfig.getCsvShowHead(activityType).split(","));
    }

    @Override
    public String getPath() {
        return path;
    }

    @Override
    List<ActivityCustomReward> read(ActivityRewardPageParam activityRewardPageParam) {

        RangeResult<ActivityCustomReward> result = activityCustomRewardStoreBuilder.findAllByBatchId(activityRewardPageParam);
        if(result == null || (activityRewardPageParam.getPageNo() > 0 && activityRewardPageParam.getNextToken() == null)){
            return null;
        }
        activityRewardPageParam.setNextToken(result.nextToken);
        int page = activityRewardPageParam.getPageNo() + 1;
        activityRewardPageParam.setPageNo(page);
        return result.list;
    }

    @Override
    ThreadPoolTaskExecutor getThreadPool() {
        return threadPoolTaskExecutor;
    }

    @Override
    protected void complete(ActivityRewardPageParam request, File file, CsvResult csvResult) {

        log.info("batchId: {}, csvResult:{}", request.getBatchId(), JSON.toJSONString(csvResult));
        ActivityBatch batch = activityBatchNewService.findByBatchId(request.getBatchId());

        try{
            if(file == null){
                log.warn("generate_file fail：{}, 文件为空", request.getBatchId());
                batch.setOssUrl("csv文件生成失败");
                activityBatchNewService.updateBatch(batch);
                return;
            }
            if(csvResult == null || csvResult.getTotal() == null || csvResult.getTotal() <= 0){
                log.warn("generate file fail：{}", request.getBatchId());
                batch.setOssUrl("发奖名单为空");
                activityBatchNewService.updateBatch(batch);
                return;
            }

            //上传到oss
            String path = getOssPath();
            Map<String, Integer> map = Objects.isNull(csvResult.getStatusCount()) ? new HashMap<>() : csvResult.getStatusCount();

            PutObjectResult result = uploadOssUtil.putObject(String.format("%s/%s", path, file.getName()), file);
            log.info("upload oss fileName:{}, result:{}", file.getName(), JSON.toJSONString(result));
            batch.setOssUrl(uploadOssUtil.getLocation(String.format("%s/%s", path, file.getName())));
            batch.setGeneralTime(TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS));
            file.deleteOnExit();

            //统计各个状态数量
            int total = 0;
            for(Map.Entry<String,Integer> entry : map.entrySet()){
                total += entry.getValue() == null ? 0 : entry.getValue();
            }

            List<PrizeAmount> prizeAmounts = new ArrayList<>();
            for(Map.Entry<String, BigDecimal> entry : csvResult.getAmount().entrySet()){
                if(StringUtils.isEmpty(entry.getKey()) || entry.getValue() == null || entry.getKey().split("_").length <= 0 || entry.getValue().compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                if(entry.getKey().split("_").length <= 1){
                    prizeAmounts.add(new PrizeAmount(entry.getKey().split("_")[0], null, entry.getValue().stripTrailingZeros().toPlainString()));
                }else{
                    prizeAmounts.add(new PrizeAmount(entry.getKey().split("_")[0], entry.getKey().split("_")[1], entry.getValue().stripTrailingZeros().toPlainString()));
                }
            }

            batch.setPrizeAmount(JSON.toJSONString(prizeAmounts));
            batch.setWinners(total);

            //AWARD_SUCCESS 已完成发奖，二次校验
            if(ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.isEquals(batch.getRewardStatus())) {
                //校验是否存在待发奖名单，重新唤起rewardJob
                if (map.getOrDefault(ActivityConstant.RewardStatusEnum.NOT_AWARD.name(), 0) > 0) {
                    batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.AWARDING.name());
                    Map<String, String> param = new HashMap<String, String>() {{
                        put("activityId", batch.getActivityId());
                        put("batchId", batch.getBatchId());
                    }};
                    batch.setOssUrl("数据完整性校验中。。。");
                    elasticJobService.createJob(activityRewardJob, elasticJobService.getJobNameForReward(batch.getBatchId()), CronUtil.getCronForMinute(1), batch.getShardCount(), param);
                }else{
                    //全部名单都走过发奖流程
                    rewardNotice(map, batch);
                    elasticJobService.removeJob(elasticJobService.getJobNameForReward(batch.getBatchId()));
                }
            }
            //UNAUDITED 完成导入， 二次校验，是否重新唤起importJob
            if(ActivityConstant.BatchRewardStatusEnum.UNAUDITED.isEquals(batch.getRewardStatus())){
                ActivityBatchStatus batchStatus = activityBatchStatusStoreBuilder.getById(batch.getBatchId());
                if(csvResult.getTotal() < batchStatus.getNum()){
                    batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.IMPORTING.name());
                    Map<String, String> param = new HashMap<String, String>() {{
                        put("activityId", batch.getActivityId());
                        put("batchId", batch.getBatchId());
                    }};
                    elasticJobService.createJob(activityImportJob, elasticJobService.getJobNameForImport(batch.getBatchId()), CronUtil.getCronForMinute(1), 1, param);
                    batch.setOssUrl("数据完整性校验中。。。");
                }else{
                    //所有都走过导入流程
                    importNotice(map, batch);
                    elasticJobService.removeJob(elasticJobService.getJobNameForImport(batch.getBatchId()));
                }
            }

        }catch (Exception ex){
            log.error("generate_file fail, batchId:{}", request.getBatchId(), ex);
            batch.setOssUrl(String.format("文件生成失败%s", ex.getMessage()));
            batch.setGeneralTime(TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS));
            if(ActivityConstant.BatchRewardStatusEnum.IMPORTING.isEquals(batch.getRewardStatus())){
                batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.IMPORT_FAILED.name());
            }else if(ActivityConstant.BatchRewardStatusEnum.AWARDING.isEquals(batch.getRewardStatus())){
                batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.name());
            }
        }
        activityBatchNewService.updateBatch(batch);
    }

    @Override
    protected void before(ActivityRewardPageParam request) {
        ActivityBatch batch = activityBatchNewService.findByBatchId(request.getBatchId());
        batch.setOssUrl("奖励名单文件生成中。。。");
        batch.setGeneralTime(TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS));
        activityBatchNewService.updateBatch(batch);
    }

    //notice，通知发奖结果
    private void importNotice(Map<String, Integer> map, ActivityBatch batch){
        log.info(ActivityConstant.LogKey.BUSINESS_NOTICE.format(String.format("批次id：%s，批次名称：%s，导入完成，成功：%s条，校验不通过：%s条，被删除：%s条"
                , batch.getBatchId()
                , batch.getName()
                , map.getOrDefault(ActivityConstant.RewardStatusEnum.NOT_AWARD.name(), 0)
                , map.getOrDefault(ActivityConstant.RewardStatusEnum.VALID_FAIL.name(), 0)
                , map.getOrDefault(ActivityConstant.RewardStatusEnum.DELETE.name(), 0)
                )));
    }

    //notice，通知发奖结果
    private void rewardNotice(Map<String, Integer> map, ActivityBatch batch){
        if(map.getOrDefault(ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name(), 0) > 0){
            String content;
            if(map.getOrDefault(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name(), 0) > 0){
                content = String.format("批次id：%s，批次名称：%s，发奖部分成功，成功：%s条，失败：%s条",
                        batch.getBatchId(),
                        batch.getName(),
                        map.get(ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()),
                        map.get(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name()));
                log.warn(ActivityConstant.LogKey.BUSINESS_NOTICE.format(content));
            }else{
                content = String.format("批次id：%s，批次名称：%s，发奖全部成功，成功：%s条"
                        , batch.getBatchId(),batch.getName(), map.get(ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()));
                log.info(ActivityConstant.LogKey.BUSINESS_NOTICE.format(content));
            }
        }else if(map.getOrDefault(ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name(), 0) == 0
                && map.getOrDefault(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name(), 0) > 0){
            batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.AWARD_FAILED.name());
            log.error(ActivityConstant.LogKey.BUSINESS_NOTICE.format(String.format("批次id：%s，批次名称：%s，发奖全部失败，失败：%s条"
                    , batch.getBatchId(), batch.getName(), map.get(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name()))));
        }
    }

    private String getOssPath(){
        return String.format("reward/cvs/%s", TimeUtil.getDataStr(new Date(), TimeUtil.YYYYMM));
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class PrizeAmount{
        private String type;
        private String currency;
        private String amount;
    }
}
