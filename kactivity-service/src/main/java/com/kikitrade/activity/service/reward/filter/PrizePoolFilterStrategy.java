package com.kikitrade.activity.service.reward.filter;

import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;

import java.util.List;

/**
 * 奖池过滤策略接口
 * 支持多维度的奖品过滤，如英雄、VIP等级、用户等级等
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface PrizePoolFilterStrategy {

    /**
     * 策略名称（唯一标识）
     */
    String getStrategyName();

    /**
     * 获取用户的过滤条件值
     *
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 过滤条件值，如果用户没有设置则返回null
     */
    String getUserFilterValue(String userId, String saasId);

    /**
     * 根据过滤条件查询奖品配置
     *
     * @param prizePoolCode 奖池编码
     * @param filterValue 过滤条件值
     * @return 符合条件的奖品配置列表
     */
    List<PrizeConfig> filterPrizes(String prizePoolCode, String filterValue);

    /**
     * 策略优先级（数字越小优先级越高）
     * 用于控制多个策略的执行顺序
     *
     * @return 优先级数值
     */
    int getPriority();

    /**
     * 策略是否启用
     *
     * @return true表示启用，false表示禁用
     */
    default boolean isEnabled() {
        return true;
    }
}