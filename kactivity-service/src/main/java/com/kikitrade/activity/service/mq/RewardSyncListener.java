package com.kikitrade.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.framework.ons.OnsMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
@Slf4j
public class RewardSyncListener implements OnsMessageListener {

    @Resource
    private TopicConfig topicConfig;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;

    @Override
    public String topic() {
        return topicConfig.getTopicReward();
    }

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody());
        log.info("RewardSyncListener: message:{}, {}", message.getMsgID(), body);
        ActivityCustomReward activityCustomReward = JSON.parseObject(body, ActivityCustomReward.class);
        LauncherParameter launcherParameter = new LauncherParameter();
        launcherParameter.setActivityCustomReward(activityCustomReward);
        try {
            activityRealTimeRewardTccService.reward(launcherParameter);
            return Action.CommitMessage;
        }catch (Exception exception){
            log.error("rewardSyncListener error, request:{}",message, exception);
            return Action.ReconsumeLater;
        }
    }
}
