package com.kikitrade.activity.service.importing.award;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.RewardRequest;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.CsvService;
import com.kikitrade.activity.service.common.config.RateLimiterConfig;
import com.kikitrade.activity.service.importing.ActivityLauncher;
import com.kikitrade.activity.service.importing.roster.domain.ActivityCustomRewardItem;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.job.ElasticJobService;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.framework.ots.RangeResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;

@Component
@Slf4j
public class ActivityOfflineRewardLauncher extends ActivityLauncher<ActivityCustomRewardItem> {

    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Autowired
    private RewardService rewardService;
    @Resource
    private ElasticJobService elasticJobService;
    @Resource
    private CsvService<ActivityCustomReward> rewardCsvService;
    @Resource
    private RedisService redisService;
    @Resource
    private CustomerService customerService;

    @Override
    protected boolean before(LauncherParameter launcherParameter) {
        String rewardStatus = launcherParameter.getBatch().getRewardStatus();
        ActivityConstant.BatchRewardStatusEnum rewardStatusEnum
                = ActivityConstant.BatchRewardStatusEnum.valueOf(rewardStatus);
        if(rewardStatusEnum.getCode() >= ActivityConstant.BatchRewardStatusEnum.APPROVE.getCode()
                && rewardStatusEnum.getCode() < ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.getCode()){
            activityBatchNewService.updateBatchStatus(launcherParameter.getBatch().getBatchId(), ActivityConstant.BatchRewardStatusEnum.AWARDING.name());
            return true;
        }
        return false;
    }

    @Override
    protected ActivityCustomRewardItem reader(LauncherParameter launcherParameter) {
        ActivityCustomRewardItem item = new ActivityCustomRewardItem();
        ActivityRewardPageParam activityRewardPageParam = new ActivityRewardPageParam();
        activityRewardPageParam.setBatchId(launcherParameter.getBatch().getBatchId());
        activityRewardPageParam.setStatusList(Arrays.asList(ActivityConstant.RewardStatusEnum.NOT_AWARD.name(), ActivityConstant.RewardStatusEnum.AWARD_FAILED.name()));
        activityRewardPageParam.setNextToken(launcherParameter.getNextToken());
        activityRewardPageParam.setShard(launcherParameter.getShard());
        RangeResult<ActivityCustomReward> rewardRangeResult = activityCustomRewardStoreBuilder.findByBatchIdAndStatus(activityRewardPageParam);
        if(rewardRangeResult != null && CollectionUtils.isNotEmpty(rewardRangeResult.list)){
            item.setActivityCustomRewards(rewardRangeResult.list);
        }
        if(rewardRangeResult == null || rewardRangeResult.nextToken == null){
            item.finish();
            return item;
        }
        launcherParameter.setNextToken(rewardRangeResult.nextToken);
        return item;
    }

    @Override
    protected void exec(ActivityCustomRewardItem activityCustomRewardItem, LauncherParameter launcherParameter) throws Exception {
        if(CollectionUtils.isEmpty(activityCustomRewardItem.getActivityCustomRewards())){
            return;
        }
        activityCustomRewardItem.getActivityCustomRewards().forEach(reward -> {
                //通过dubbo接口调用
                RewardRequest rewardRequest = RewardRequest.builder()
                        .rewardId(reward.getBusinessId())
                        .customerId(reward.getCustomerId())
                        .currency(reward.getCurrency())
                        .amount(StringUtils.isBlank(reward.getAmount()) ? null : new BigDecimal(reward.getAmount()))
                        .type(reward.getRewardType())
                        .build();
            ActivityCustomReward customReward = activityCustomRewardStoreBuilder.findByPrimaryId(reward.getBatchId(), reward.getCustomerId(), reward.getSeq());
            reward.setActivityName(customReward.getActivityName());
            reward.setNickName(customReward.getNickName());
            try{
                reward(rewardRequest, reward);
            }catch (Exception ex){
                log.error("发奖失败:{}",rewardRequest, ex);
            }
        });
    }

    @Override
    protected void after(LauncherParameter launcherParameter) {
        String key = String.format("REWARD_PROCESS:%s", launcherParameter.getBatch().getBatchId());
        Long increase = redisService.increaseBy(key, 1);
        redisService.expire(key, 24 * 60 *60);
        //生成csv
        if(increase >= launcherParameter.getTotalShard()){
            activityBatchNewService.updateBatchStatus(launcherParameter.getBatch().getBatchId(), ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.name());
            elasticJobService.removeJob(elasticJobService.getJobNameForImport(launcherParameter.getBatch().getBatchId()));
            uploadOss(launcherParameter.getBatch());
        }
    }

    private void reward(RewardRequest rewardRequest, ActivityCustomReward activityCustomReward){
        log.info("remoteRewardService request:{}", JSON.toJSONString(rewardRequest));
        try{
            //调用发奖接口
            com.kikitrade.activity.service.reward.model.RewardRequest request = com.kikitrade.activity.service.reward.model.RewardRequest.builder()
                    .rewardId(rewardRequest.getRewardId())
                    .customerId(rewardRequest.getCustomerId())
                    .currency(rewardRequest.getCurrency())
                    .amount(rewardRequest.getAmount())
                    .type(rewardRequest.getType())
                    .nickName(activityCustomReward.getNickName())
                    .side(StringUtils.isNotBlank(activityCustomReward.getSide()) ? ActivityConstant.SideEnum.valueOf(activityCustomReward.getSide()) : null)
                    .activityName(activityCustomReward.getActivityName())
                    .build();

            ActivityResponse response = rewardService.reward(request);
            activityCustomReward.setStatus(response.getCode() == ActivityResponseCode.SUCCESS ? ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()
                    : ActivityConstant.RewardStatusEnum.AWARD_FAILED.name());
            if(response.getCode() != ActivityResponseCode.SUCCESS){
                log.info("remoteRewardService response:{}",response);
                activityCustomReward.setMessage(response.getMsg());
            }
            activityCustomReward.setRewardTime(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            activityCustomRewardStoreBuilder.updateStatus(activityCustomReward);
        }catch (Exception ex){
            log.error("manualReward error，rewardId:{}", rewardRequest.getRewardId(), ex);
            activityCustomReward.setMessage(ex.getMessage());
            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name());
            activityCustomRewardStoreBuilder.updateStatus(activityCustomReward);
        }
    }

    private void uploadOss(ActivityBatch batch){
        ActivityRewardPageParam pageParam = new ActivityRewardPageParam();
        pageParam.setBatchId(batch.getBatchId());
        pageParam.setActivityType(batch.getActivityType());
        pageParam.setPageNo(0);
        pageParam.setStatusList(Arrays.asList(ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.name()));
        rewardCsvService.write(String.format("%s-%s.%s",batch.getName(), batch.getBatchId(), "csv"), pageParam);
    }
}
