package com.kikitrade.activity.service.business;

import com.kikitrade.activity.model.EmailTemplateType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.kcustomer.api.constants.FirebaseTemplateType;

/**
 * NoticeService
 *
 * <AUTHOR>
 * @create 2021/7/6 2:28 下午
 * @modify
 */
public interface NoticeService {

    void dispatchFailNotice(String msg);

    void doActionsFailNotice(String business_id, Integer activity_id);

    void doActionsPrepareFailNotice(String business_id, Integer activity_id);

    void notice(String saasId, String customerId, EmailTemplateType emailTemplate, RewardRequest request, ActivityConstant.FirebaseTemplateCode notificationEnum);

    void noticeDingTalk(String msgId,String url, String msg);
}
