package com.kikitrade.activity.service.question.impl;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.CustomerQuestionSetsBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.CustomerQuestionSets;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.question.QuestionSetsService;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class QuestionSetsServiceImpl implements QuestionSetsService {

    private static final String TK_SETS_NATURE_REWARD = "sets_nature_reward";

    @Resource
    private CustomerQuestionSetsBuilder customerQuestionSetsBuilder;

    @Resource
    private TaskConfigService taskConfigService;

    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;

    @Resource
    private SeqClient seqClient;

    @Override
    public boolean insert(CustomerQuestionSets customerQuestionSets) {
        return customerQuestionSetsBuilder.insert(customerQuestionSets);
    }

    @Override
    public Long incrementAvailableSets(CustomerQuestionSets customerQuestionSets, int inc) {
        return customerQuestionSetsBuilder.incrementAvailableSets(customerQuestionSets, inc);
    }

    @Override
    public Long incrementUsedSets(CustomerQuestionSets customerQuestionSets) {
        return customerQuestionSetsBuilder.incrementUsedSets(customerQuestionSets);
    }

    @Override
    public boolean update(CustomerQuestionSets customerQuestionSets) {
        List<TaskConfigDTO> setsNatureRewardTasks = taskConfigService.findByTaskCode(customerQuestionSets.getSaasId(), TK_SETS_NATURE_REWARD);
        if (!setsNatureRewardTasks.isEmpty()) {
            TaskConfigDTO setsNatureRewardTask = setsNatureRewardTasks.get(0);
            int availableSetsRewardThreshold = Integer.parseInt(setsNatureRewardTask.getAttr().get("availableSetsRewardThreshold"));
            // 每当 availableSets 小于 availableSetsRewardThreshold，记录开启奖励时间 beginRewardTime
            if (customerQuestionSets.getAvailableSets() < availableSetsRewardThreshold && customerQuestionSets.getRewardRemainTime() == 0L) {
                customerQuestionSets.setBeginRewardTime(System.currentTimeMillis());
            }
        }
        customerQuestionSets.setModified(System.currentTimeMillis());
        return customerQuestionSetsBuilder.update(customerQuestionSets);
    }

    @Override
    public CustomerQuestionSets findByUid(String saasId, String customerId) {
        return customerQuestionSetsBuilder.findByUser(saasId, customerId);
    }

    @Override
    public CustomerQuestionSets findByUser(String saasId, String customerId) {
        CustomerQuestionSets questionSets = customerQuestionSetsBuilder.findByUser(saasId, customerId);
        if (questionSets == null) {
            questionSets = new CustomerQuestionSets();
            questionSets.setSaasId(saasId);
            questionSets.setCustomerId(customerId);
            questionSets.setAvailableSets(2);
            questionSets.setCreated(System.currentTimeMillis());
            questionSets.setModified(System.currentTimeMillis());
            insert(questionSets);
        }
        availableSetsNatureReward(questionSets);
        return questionSets;
    }

    /**
     * 可答题组数自然奖励
     * 规则：
     *  当用户的答题可用次数小于 2，每隔 8 小时给用户增加可用次数 1
     *  最多增加到可用次数 >=2 不再继续增加
     * @param questionSets
     */
    private void availableSetsNatureReward(CustomerQuestionSets questionSets) {
        log.info("rewardAvailableSets before questionSets = {}", questionSets);
        if (questionSets.getBeginRewardTime() == 0L) {
            return;
        }
        List<TaskConfigDTO> setsNatureRewardTasks = taskConfigService.findByTaskCode(questionSets.getSaasId(), TK_SETS_NATURE_REWARD);
        if (setsNatureRewardTasks.isEmpty()) {
            return;
        }
        TaskConfigDTO setsNatureRewardTask = setsNatureRewardTasks.get(0);
        int availableSetsRewardThreshold = Integer.parseInt(setsNatureRewardTask.getAttr().get("availableSetsRewardThreshold"));
        long availableSetsRewardIntervalTimes = Long.parseLong(setsNatureRewardTask.getAttr().get("availableSetsRewardIntervalTimes"));
        int availableSetsRewardMaxTo = Integer.parseInt(setsNatureRewardTask.getAttr().get("availableSetsRewardMaxTo"));
        log.info("[availableSetsNatureReward] threshold = {}, intervalTimes = {}, maxTo = {}",
                availableSetsRewardThreshold, availableSetsRewardIntervalTimes, availableSetsRewardMaxTo);

        long now = System.currentTimeMillis();
        long beginRewardTime = questionSets.getBeginRewardTime();
        long intervalTimes = (now - beginRewardTime);  // 距离上一次发奖的时间差 (毫秒)

        int newAvailableSets = questionSets.getAvailableSets();
        // 如果可用次数小于 availableSetsRewardThreshold (默认 2)，则根据时间间隔更新次数
        if (questionSets.getAvailableSets() < availableSetsRewardThreshold) {

            // 计算需要增加的次数 : 每间隔 availableSetsIntervalHours (默认 8 小时) 小时增加 1
            int additionalSets = (int) (intervalTimes / availableSetsRewardIntervalTimes);
            log.info("additionalSets = {}", additionalSets);

            // 奖励增加最大到 availableSetsRewardMaxTo (默认 2)
            newAvailableSets = Math.min(questionSets.getAvailableSets() + additionalSets, availableSetsRewardMaxTo);
            log.info("newAvailableSets = {}", newAvailableSets);

            int needAdd = newAvailableSets - questionSets.getAvailableSets();
            log.info("needAdd = {}", needAdd);
            if (needAdd > 0) {
                try {
                    // 直接发奖
                    ActivityCustomReward activityCustomReward = new ActivityCustomReward();
                    activityCustomReward.setSaasId(questionSets.getSaasId());
                    activityCustomReward.setCustomerId(questionSets.getCustomerId());
                    activityCustomReward.setAmount(String.valueOf(needAdd));

                    Award reward = setsNatureRewardTask.getReward().get("0").get(0);
                    activityCustomReward.setCurrency(reward.getCurrency());
                    activityCustomReward.setRewardType(reward.getType());
                    activityCustomReward.setBusinessType(ActivityConstant.RewardBusinessType.reward.name());
                    activityCustomReward.setBatchId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHmmss));
                    activityCustomReward.setBusinessId(seqClient.next(new SeqRule("IDX_ACTIVITY_REWARD", 1, "yyyyMMddHHmmss", null)));
                    activityCustomReward.setSeq(setsNatureRewardTask.getCode() + ":" + activityCustomReward.getBusinessId());

                    LauncherParameter launcherParameter = new LauncherParameter();
                    launcherParameter.setActivityCustomReward(activityCustomReward);
                    launcherParameter.setProvideType(setsNatureRewardTask.getProvideType());
                    activityRealTimeRewardTccService.reward(launcherParameter);
                } catch (Exception e) {
                    log.error("[availableSetsNatureReward] invoke exception, setsNatureRewardTask id = {}",setsNatureRewardTask.getTaskId(), e);
                }
                log.info("rewardAvailableSets now add to newAvailableSets = {} questionSets = {}", newAvailableSets, questionSets);

                // 如果已经达到 availableSetsRewardMaxTo (默认 2) 次，则关闭发奖
                if (newAvailableSets >= availableSetsRewardMaxTo) {
                    questionSets.setBeginRewardTime(0l); // 关闭发奖
                    questionSets.setRewardRemainTime(0l);
                } else {
                    // 本次奖励 N，更新开始发奖时间 后移 N * interval
                    questionSets.setBeginRewardTime(beginRewardTime + availableSetsRewardIntervalTimes * needAdd);
                    // 更新剩余时间：(N + 1) * interval - now
                    questionSets.setRewardRemainTime(beginRewardTime + availableSetsRewardIntervalTimes * (needAdd + 1) - now);
                }
            } else {
                // 本次未奖励，更新剩余时间:1 * interval - now
                questionSets.setRewardRemainTime(beginRewardTime + availableSetsRewardIntervalTimes - now);
            }
        } else {
            questionSets.setBeginRewardTime(0l); // 关闭发奖
            questionSets.setRewardRemainTime(0l);
        }
        CustomerQuestionSets byUid = findByUid(questionSets.getSaasId(), questionSets.getCustomerId());
        questionSets.setAvailableSets(byUid.getAvailableSets());
        questionSets.setModified(byUid.getModified());
        // 更新 BeginRewardTime RewardRemainTime
        update(questionSets);
    }

}
