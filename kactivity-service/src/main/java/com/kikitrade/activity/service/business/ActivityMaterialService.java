package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityMaterial;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.service.model.ActivityMaterialVO;
import com.kikitrade.activity.service.view.model.ActivityInfo;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ActivityMaterialService {

    /**
     * 保存物料
     * @param activityMaterialVO
     * @return
     */
    Result save(ActivityMaterialVO activityMaterialVO);

    /**
     * 查询物料信息
     * @return
     */
    List<ActivityMaterial> findByActivityId(String activityId);

    /**
     * 从缓存中查询物料信息
     * @param activityId
     * @return
     */
    Optional<Map<String, ActivityMaterial>> findByActivityIdFromCache(String activityId);
}
