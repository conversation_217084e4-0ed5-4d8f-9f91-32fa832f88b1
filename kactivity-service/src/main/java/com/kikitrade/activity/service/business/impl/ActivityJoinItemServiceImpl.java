package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.tablestore.builder.ActivityJoinItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityJoinItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityJoinItemService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-13 10:46
 */
@Slf4j
@Service
public class ActivityJoinItemServiceImpl implements ActivityJoinItemService {

    @Resource
    private ActivityJoinItemBuilder activityJoinItemBuilder;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    private CustomerService customerService;

    @Override
    public boolean insert(String userName, String businessCode, String businessType) {
        CustomerCacheDTO customerDTO = customerService.getByPhoneOrEmail(userName);
        if(customerDTO != null){
            log.warn("非新用户不能参加新用户空投活动{},{}", userName, businessCode);
            return false;
        }
        ActivityJoinItem joinItem = queryByUserName(userName);
        if(joinItem != null && ActivityConstant.ActivityJoinStatus.DONE.name().equals(joinItem.getStatus())){
            log.warn("已经参加新用户空投活动{},{}", userName, businessCode);
            return false;
        }
        ActivityJoinItem item = new ActivityJoinItem();
        item.setActivityCode(getActivityCode());
        item.setBusinessCode(businessCode);
        item.setUserName(userName);
        item.setBusinessType(businessType);
        item.setStatus(ActivityConstant.ActivityJoinStatus.APPENDING.name());
        return activityJoinItemBuilder.insert(item);
    }

    @Override
    public boolean done(String userName, String businessCode) {
        ActivityJoinItem joinItem = queryByUserName(userName);
        CustomerCacheDTO customerDTO = customerService.getByPhoneOrEmail(userName);
        if(customerDTO == null){
            log.warn("未完成新用户空投活动{},{}", userName, businessCode);
            return false;
        }
        if(joinItem != null && ActivityConstant.ActivityJoinStatus.APPENDING.name().equals(joinItem.getStatus())
            && joinItem.getBusinessCode().equals(businessCode)){
            return activityJoinItemBuilder.update(joinItem);
        }
        return false;
    }

    public ActivityJoinItem queryByUserName(String userName) {
        return activityJoinItemBuilder.queryById(userName, getActivityCode());
    }

    @Override
    public String getActivityCode(){
        return kactivityModuleProperties.getLuck().getActivityCode();
    }

    @Override
    public Pair<Boolean, String> isContainsActivityCode(String businessCode){
        String activityCode = String.format("%s%s%s%s", businessCode.charAt(2), businessCode.charAt(10), businessCode.charAt(15) ,businessCode.charAt(18));
        return new ImmutablePair<>(getActivityCode().equals(activityCode), activityCode);
    }

    @Override
    public String reGenerateCode(String businessCode){
        String activityCode = getActivityCode();
        return String.format("%s%s%s%s%s%s%s%s%s",
                businessCode.substring(0, 2), activityCode.charAt(0),
                businessCode.substring(2, 9), activityCode.charAt(1),
                businessCode.substring(9, 13), activityCode.charAt(2),
                businessCode.substring(13, 15), activityCode.charAt(3), businessCode.substring(15));
    }
}
