package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityCustomerFeeDao;
import com.kikitrade.activity.service.business.ActivityCustomerFeeService;
import com.kikitrade.activity.dal.mysql.model.ActivityCustomerFee;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityCustomerFeeServiceImpl implements ActivityCustomerFeeService {


    @Resource
    private ActivityCustomerFeeDao activityCustomerFeeDao;


    @Override
    public List<ActivityCustomerFee> findAll(String tran_date, Integer offset, Integer limit, Integer type) {
        List<ActivityCustomerFee> activityCustomerFeeList = null;
        try {
            activityCustomerFeeList = activityCustomerFeeDao.findAll(tran_date, offset, limit, type);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl findAll process failed.", e);
        }
        return activityCustomerFeeList;
    }

    @Override
    public List<ActivityCustomerFee> findByDateAndStatus(String tran_date, Integer activityType, Integer status, int offset, int limit) {
        return  activityCustomerFeeDao.findByDateAndStatus(tran_date,activityType,status,offset, limit);
    }
    @Override
    public List<ActivityCustomerFee> findByDate(String tran_date,  int offset,int limit) {
        return activityCustomerFeeDao.findByDate(tran_date,offset,limit);
    }

    @Override
    public List<ActivityCustomerFee> findByCurrencyDate(String currency, String date, int offset, int limit) {
        return activityCustomerFeeDao.findByCurrencyDate(currency,date, offset, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(String tran_date, String customer_id, String currency, Integer status, Integer type) {
        int count = 0;
        try {
            count = activityCustomerFeeDao.updateStatus(tran_date, customer_id, currency, status, type);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl updateStatus process failed.", e);
            throw e;
        }
        return count;
    }

    @Override
    public Long countByTypeAndTye(String tran_date, int type, int status) {
        try{
            return activityCustomerFeeDao.countByTypeAndTye(tran_date, type, status);
        }catch (Exception e){
            log.error("activitycustomerfeeserviceimpl countByTypeAndTye process failed. type:{}, status:{}", type, status, e);
            return -1L;
        }
    }


}
