package com.kikitrade.activity.service.business.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.CouponConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.dal.tablestore.model.CouponConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ClaimItemService;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.model.CouponClaimInfo;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.order.api.RemoteOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem.DEFAULT_CYCLE;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/6 18:30
 */
@Service
@Slf4j
public class ClaimRedeemSimpleServiceImpl implements ClaimItemService {

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @Resource
    private CouponConfigBuilder couponConfigBuilder;

    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;

    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;

    @Resource
    private ActivityEventAction activityEventAction;


    @Override
    public Boolean isContinue(String businessType) {
        return "redeem-simple".equals(businessType);
    }

    @Override
    public Boolean allowClaim(String businessType, String customerId, String address, List<String> addresses) {
        return true;
    }

    @Override
    public ClaimResponse claim(String businessType, String address, String customerId, String code, String saasId, List<String> addresses) throws ActivityException {
        if(StringUtils.isEmpty(code)){
            throw new ActivityException(ActivityResponseCode.CLAIM_CODE_INVALID);
        }
        code = code.toLowerCase();
        CouponConfig config = couponConfigBuilder.getConfigByPK(code, saasId, businessType);
        if(config == null) {
            throw new ActivityException(ActivityResponseCode.CLAIM_CODE_INVALID);
        }

        boolean exist = activityTaskItemBuilder.exist(customerId, config.getTaskId(), TaskCycleDomain.getCycleOnce(), code);
        if(exist){
            throw new ActivityException(ActivityResponseCode.CLAIM_REPEAT);
        }

        CouponClaimInfo couponClaimInfo = new CouponClaimInfo();
        BeanUtils.copyProperties(config, couponClaimInfo);
        couponClaimInfo.setCustomerId(customerId);

        //发放奖励
        try {
            reward(couponClaimInfo);
            ClaimResponse claimResponse = new ClaimResponse();
            claimResponse.setSuccess(true);
            return claimResponse;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void reward(CouponClaimInfo couponClaimInfo) throws Exception{

        ActivityEventMessage activityEventMessage = new ActivityEventMessage();
        activityEventMessage.setCustomerId(couponClaimInfo.getCustomerId());
        activityEventMessage.setEventCode(AssetBusinessType.CLAIM_REDEEM.getCodeDesc());
        activityEventMessage.setEventTime(System.currentTimeMillis());
        activityEventMessage.setTargetId(couponClaimInfo.getCouponCode());
        Map<String,Object> body = new HashMap<>();
        body.put("taskId", couponClaimInfo.getTaskId());
        body.put("vipLevel", "NORMAL");
        body.put("amount", couponClaimInfo.getAwardAmount());
        body.put("saasId", couponClaimInfo.getSaasId());

        activityEventMessage.setBody(body);
        ActivityResponse response = activityEventAction.action(activityEventMessage);
        if(!response.getCode().isSuccess()){
            throw new ActivityException(response.getCode());
        }

    }
}
