package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityCustomerDiscount;

import java.util.List;

public interface ActivityCustomerDiscountService {

    List<ActivityCustomerDiscount> findAll(String discountDate, Integer offset, Integer limit);

    int updateStatus(String discountDate, String customerId, String investmentProductId, Integer status);

    Long countByStatus(String discountDate, Integer status);
}
