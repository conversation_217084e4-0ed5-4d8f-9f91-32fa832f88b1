package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.dal.mysql.dao.ActivityDao;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.mysql.model.ActivityActionMap;
import com.kikitrade.activity.dal.mysql.model.ActivityRuleMap;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityRecords;
import com.kikitrade.activity.model.*;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityMessageConstant;
import com.kikitrade.activity.service.business.ActivityCommonService;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.Combinations;
import com.kikitrade.activity.service.common.SpringUtil;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.service.engine.GlobalEventRuleEngine;
import com.kikitrade.activity.service.engine.action.base.AbstractBaseAction;
import com.kikitrade.activity.service.engine.action.base.BaseAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.api.Rule;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityCommonServiceImpl implements ActivityCommonService {

    @Resource
    private RedisService redisService;
    @Resource
    ActivityRecordsBuilder activityRecordsBuilder;
    @Resource
    private GlobalEventRuleEngine globalEventRuleEngine;
    @Resource
    private NoticeService noticeService;
    @Resource
    private ActivityDao activityDao;


    public List<Activity> checkRules(ActivityMessage activityMassage, List<Activity> activityList) throws Exception {
        try {
            if (CollectionUtils.isEmpty(activityList)) {
                return activityList;
            }
            //执行规则校验，返回通过规则列表
            activityList = doCheck(activityMassage, activityList);

        } catch (Exception e) {
            log.error("activitycommonserviceimpl CheckRules process failed", e);
            throw e;
        }

        return activityList;
    }


    public boolean doActions(ActivityMessage activityMassage, List<Activity> activityList) {

        boolean flag = false;

        for (Activity activity : activityList) {
            Integer status = null;
            String remark = null;
            try {
                //获取活动执行列表
                List<ActivityActionMap> activityActionMapList = activity.getActionConfig();
                //活动action，目前默认活动只配一个Action
                if (CollectionUtils.isEmpty(activityActionMapList)) {
                    log.error("CustomerCurrentInterestAction invalid activity action param ,skip action {}", JSONObject.toJSONString(activity));
                    status = ActivityConstant.RecordStatus.FAILED.getCode();
                    remark = ActivityConstant.MsgStatus.SKIP_ACTION.getCodeAndDesc();
                    break;
                }

                String actionName = activityActionMapList.get(0).getAction_name();
                BaseAction action = (BaseAction) SpringUtil.getBean(actionName);
                //log.info("doActions start activityMassage business_id [{}], activity id [{}] ################################################################## ", activityMassage.getBusiness_id(), activity.getId());

                ActivityMessage message = new ActivityMessage();
                BeanUtils.copyProperties(activityMassage, message);
                message.setActivityId(message.getActivityId() == null ? activity.getId() : message.getActivityId());
                flag = action.doAction(message, activity);
                if (flag) {
                    status = ActivityConstant.RecordStatus.COMPLETED.getCode();
                    remark = ActivityConstant.MsgStatus.ACTION_SUCCEEDED.getCodeAndDesc();
                } else {
                    status = ActivityConstant.RecordStatus.FAILED.getCode();
                    remark = ActivityConstant.MsgStatus.ACTION_FAILED.getCodeAndDesc();
                }

            } catch (Exception e) {
                flag = false;
                log.error("activitycommonserviceimpl doActions process failed. activity:{}", JSON.toJSONString(activity), e);
                status = ActivityConstant.RecordStatus.FAILED.getCode();
                remark = ActivityConstant.MsgStatus.PROCESS_FAILED.getParaMsg("activitycommonserviceimpl doActions process failed");
            } finally {
                try {
                    boolean uodateFlag = activityRecordsBuilder.updateStatus(activityMassage.getBusinessId(), activity.getId(), activity.getExecute_type(), status, remark);
                    log.info("activitycommonserviceimpl doActions end  activityMassage business_id [{}], activity id [{}],execute flag [{}] update flag [{}] ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~", activityMassage.getBusinessId(), activity.getId(), flag, uodateFlag);
                } catch (Exception e1) {
                    flag = false;
                    log.error("activitycommonserviceimpl doActions update record status fail.", e1);
                }

            }

            if (!flag) {
                //do actions fail，notice
                noticeService.doActionsFailNotice(activityMassage.getBusinessId(), activity.getId());
                break;
            }
        }

        return flag;

    }

    /**
     * //正在进行的活动列表【activity】
     * //获取活动规则对应关系【activity_rule_map】
     * //获取活动执行对应关系【activity_action_map】
     *
     * @return
     */
    public List<Activity> getActivityListFromRedis(ActivityMessage activityMassage, List<String> keyList) {
        List<Activity> activityList = new ArrayList<>();
        try {
            for (String key : keyList) {
                List<Activity> list = redisGet(key);
                if (!CollectionUtils.isEmpty(list)) {

                    for (Activity activity : list) {
                        //批量数据
                        if (activityMassage.isBatch()) {
                            //活动状态检查
                            if (activity.getStatus() != ActivityConstant.Status.PUBLISHED.getCode() && activity.getStatus() != ActivityConstant.Status.PROCESSING.getCode() && activity.getStatus() != ActivityConstant.Status.END.getCode()) {
                                log.info("getActivityListFromRedis batch statusCheck: business_id [{}] activity [{}]  status [{}] incorrect, so skip!", activityMassage.getBusinessId(), activity.getId(), activity.getStatus());
                                continue;
                            }

                            //活动ID检查，批量业务已指定参加活动的ID（默认取最大活动ID），其他满足活动规则的跳过
                            if (activityMassage.getActivityId() != null && activityMassage.getActivityId().intValue() != activity.getId().intValue()) {
                                log.info("getActivityListFromRedis batch activityIdCheck:  id [{}]  name [{}],activity id not [{}]，skip ！~~~~~~~~~~~~~~~", activity.getId(), activity.getName(), activityMassage.getActivityId());
                                continue;
                            }

                            //跳过自动处理活动类型
                            if (activity.getExecute_type() == ActivityConstant.ExecuteType.AUTO.getCode()) {
                                log.info("getActivityListFromRedis batch activityIdCheck:  id [{}]  name [{}],execute type is auto，skip ！~~~~~~~~~~~~~~~", activity.getId(), activity.getName());
                                continue;
                            }

                        } else {
                            //活动时间检查
                            long currettime = System.currentTimeMillis();
                            long startTime = activity.getStart_time().getTime();
                            long endTime = activity.getEnd_time().getTime();
                            if (currettime < startTime || currettime > endTime) {
                                log.info("getActivityListFromRedis timeCheck: business_id [{}] activity [{}] not start or already ended, so skip!", activityMassage.getBusinessId(), activity.getId());
                                continue;
                            }
                            //活动状态检查
                            if (activity.getStatus() != ActivityConstant.Status.PUBLISHED.getCode() && activity.getStatus() != ActivityConstant.Status.PROCESSING.getCode()) {
                                log.info("getActivityListFromRedis statusCheck: business_id [{}] activity [{}]  status [{}] incorrect, so skip!", activityMassage.getBusinessId(), activity.getId(), activity.getStatus());
                                continue;
                            }
                        }
                        activityList.add(activity);
                    }
                }
            }

            if (CollectionUtils.isEmpty(activityList)) {
                log.warn("activitycommonserviceimpl getActivityListFromRedis no activity to participate! ");
            }
            return activityList;
        } catch (Exception e) {
            log.error("activitycommonserviceimpl getActivityListFromRedis process fail.", e);
        }

        return null;
    }

    public void saveRecords(ActivityMessage activityMassage, List<Activity> activityList, Integer record_status, String remark) throws Exception {
        if (CollectionUtils.isEmpty(activityList)) {
            Integer activityId = -1, activityType = -1, executeType = -1;
            if (activityMassage.getActivityId() != null) {
                Activity activity = activityDao.findById(activityMassage.getActivityId());
                if (activity != null) {
                    activityId = activity.getId();
                    activityType = activity.getType();
                    executeType = activity.getExecute_type();
                }
            }
            recordSave(activityMassage, record_status, remark, activityId, activityType, executeType);

        } else {
            activityList.forEach(activity -> {
                activityMassage.setCheckResult(activity.getCheckResult());
                if (ActivityType.getType(activity.getType().intValue()).needTimesCheck()) {
                    String redisKey = RedisKeyConst.ACTIVITY_RECORD_KEY + "_" + activityMassage.getCustomerId() + activityMassage.getBusinessId() + "_" + activity.getId();
                    //log.info("activitycommonserviceimpl saveRecords redisKey is :{}", "lock_" + redisKey);
                    String lock = null;
                    try {
                        //（1）新增redis分布式锁，解决同一用户多次参加活动并发问题
                        //（2）锁有效期3s,存活时间30s 锁超时自动释放
                        lock = redisService.lock(redisKey);
                        if (StringUtils.isNotBlank(lock)) {
                            //检查是否参与活动，并达到参与次数上限
                            long count = activityRecordsBuilder.countByCustomerId(activity.getExecute_type(), activityMassage.getCustomerId(), activity.getId());
                            //log.info("saveRecords timesCheck business_id [{}] activity [{}] applytime [{}] count [{}]", activityMassage.getBusiness_id(), activityList.get(i).getId(), activityList.get(i).getApply_times(), count);
                            //检查是否参与活动，并达到参与次数上限
                            if (count < activity.getApply_times()) {
                                recordSave(activityMassage, record_status, remark, activity.getId(), activity.getType(), activity.getExecute_type());
                            }
                        } else {
                            log.info("activitycommonserviceimpl saveRecords  activity [{}] customer_id [{}] already in processing , so skip!", activity.getId(), activityMassage.getCustomerId());
                        }
                    } catch (Exception e) {
                        log.error("activitycommonserviceimpl saveRecords process fail.", e);
                    } finally {
                        if (StringUtils.isNotBlank(lock)) {
                            redisService.unLock(lock);
                        }
                    }
                } else {
                    recordSave(activityMassage, record_status, remark, activity.getId(), activity.getType(), activity.getExecute_type());
                }

            });
        }
    }

    private void recordSave(ActivityMessage activityMessage, Integer recordStatus, String remark, Integer activityId, Integer activityType, Integer execute_type) {
        ActivityRecords activityRecords = new ActivityRecords();
        activityRecords.setCreate_time(new Date());
        activityRecords.setCustomer_id(activityMessage.getCustomerId());
        activityRecords.setBusiness_id(activityMessage.getBusinessId());
        activityRecords.setParams(activityMessage.getParams());
        activityRecords.setActivity_id(activityId);
        activityRecords.setExecute_type(execute_type);
        activityRecords.setActivity_type(activityType);
        activityRecords.setCheck_result(activityMessage.getCheckResult());
        activityRecords.setStatus(recordStatus);
        activityRecords.setRemark(remark);
        activityRecords.setSaasId(activityMessage.getSaasId());

        // 设置报表所用的信息
        activityRecords.setUserName((String) activityMessage.fetchBusinessAttr(ActivityMessageConstant.BusinessAttrs.USER_NAME.getName()));
        ActivityMessage.RewardResult rewardResult = activityMessage.fetchRewardResult(activityId);
        if (rewardResult != null) {
            activityRecords.setRewardCurrency(rewardResult.getRewardCurrency());
            activityRecords.setRewardAmount(rewardResult.getRewardMoney());
            activityRecords.setExchangeCurrency(rewardResult.getExchangeCurrency());
        }
        String depositCurrency = activityMessage.fetchParamsJson().getString(ActivityMessageConstant.BusinessAttrs.DEPOSIT_CURRENCY.getName());
        BigDecimal depositAmount = activityMessage.fetchParamsJson().getBigDecimal(ActivityMessageConstant.BusinessAttrs.DEPOSIT_AMOUNT.getName());
        if (StringUtils.isNotEmpty(depositCurrency) && depositAmount != null) {
            Map<String, Object> businessAttrs = new HashMap<>();
            businessAttrs.put("depositCurrency", depositCurrency);
            businessAttrs.put("depositAmount", depositAmount);
            activityRecords.setBusinessAttrs(businessAttrs.toString()); // 本字段主要是给报表展示用，使用key=value的格式比json更直观一些，所以用toString
        }

        boolean flag = activityRecordsBuilder.saveIfNotExist(activityRecords);
        if (!flag && (activityId != -1)) {
            log.error("activitycommonserviceimpl saveRecords process failed, activityRecords is [{}]  ", JSONObject.toJSONString(activityRecords));
        }
    }

    /**
     * get cached activity from redis. return redis key.
     *
     * @param ruleList
     * @return
     */
    public List<String> getKeyList(List<Rule> ruleList) {
        List<String> keyList = new ArrayList<>();
        List<Integer> priorityList = new ArrayList<>();
        for (int i = 0; i < ruleList.size(); i++) {
            priorityList.add(ruleList.get(i).getPriority());
        }
        for (int i = 1; i < priorityList.size() + 1; i++) {
            Combinations combinations = new Combinations(priorityList, i);
            for (Object list : combinations) {
                String key = RedisKeyConst.ACTIVITY_KEY.getPrefix();
                List<Integer> li = (List<Integer>) list;
                for (int j = 0; j < li.size(); j++) {
                    key += "_" + li.get(j);
                }
                keyList.add(key);
            }
        }
        log.info("getKeyList:rules check passed redis key List {}", keyList);
        return keyList;
    }


    public List<Activity> doCheck(ActivityMessage activityMassage,
                                  List<Activity> activityList) {

        List<Activity> passList = new ArrayList<>();
        Map<Integer, Map<Rule, Boolean>> allruleCheckResult = new HashMap<>();
        for (Activity activity : activityList) {
            Map<Rule, Boolean> ruleCheckResult = globalEventRuleEngine.check(activity, activityMassage);
            boolean pass = ruleCheckResult.values().stream().allMatch(f -> f && true);
            activity.setCheckResult(ruleCheckResult.toString());
            allruleCheckResult.put(activity.getId(), ruleCheckResult);
            if (pass) {
                passList.add(activity);
            }
        }
        activityMassage.setCheckResult(allruleCheckResult.toString());
//        log.info("doCheck: easyruleEngine check passed List [{}]", passList);
        return passList;
    }

    public boolean actionPrepare(ActivityMessage activityMassage, List<Activity> activityList) {
        log.info("ActivityCommonServiceImpl actionPrepare start activityMassage {},activityList {}", JSONObject.toJSONString(activityMassage), JSONObject.toJSONString(activityList));
        if (CollectionUtils.isEmpty(activityList)) {
            return true;
        }
        boolean flag = true;
        for (Activity activity : activityList) {
            try {
                //活动action，目前默认活动只配一个Action
                if (activity.getExecute_type() != ActivityConstant.ExecuteType.MANUAL.getCode() && activity.getActionConfig().size() > 0) {
                    AbstractBaseAction action = (AbstractBaseAction) SpringUtil.getBean(activity.getActionConfig().get(0).getAction_name());
                    flag = action.actionPrepare(activityMassage, activity);
                    log.info("ActivityCommonServiceImpl actionPrepare process flag {}", flag);
                }
            } catch (Exception e) {
                flag = false;
                log.error("activitycommonserviceimpl actionPrepare process failed. activity:{}", JSON.toJSONString(activity), e);
            }

            if (!flag) {
                //do actions prepare fail，notice
                noticeService.doActionsPrepareFailNotice(activityMassage.getBusinessId(), activity.getId());
                break;
            }
        }

        log.info("ActivityCommonServiceImpl actionPrepare end  {}", flag);
        return flag;

    }

    public boolean timesCheck(ActivityMessage activityMassage, Activity activity) {

        boolean pass = true;
        try {
            //参与次数检查
            if (ActivityType.getType(activity.getType().intValue()).needTimesCheck()) {
                pass = activityRecordsBuilder.countByCustomerId(activity.getExecute_type(), activityMassage.getCustomerId(), activity.getId()) < activity.getApply_times();
            }
        } catch (Exception e) {
            log.error("timesCheck activity[{}] business_id [{}] activity participation check process failed, error msg is [{}]", activity.getId(), activity.getBusiness_id(), e);
        } finally {
            if (!pass) {
                log.info("timesCheck activity[{}] business_id [{}] activity participation check result [{}]", activity.getId(), activity.getBusiness_id(), pass);
                return pass;
            }
        }

        try {
            //报文重复性检查
            pass = activityRecordsBuilder.countByBusinessId(activity.getExecute_type(), activityMassage.getBusinessId(), activity.getId()) == 0;
        } catch (Exception e) {
            log.error("timesCheck activity[{}] business_id [{}] activity mq msg duplicate check process failed", activity.getId(), activityMassage.getBusinessId(), e);
        }

        if (!pass) {
            log.info("timesCheck activity[{}] business_id [{}] ctivity mq msg duplicate check result [{}]", activity.getId(), activityMassage.getBusinessId(), pass);
        }

        return pass;
    }


    public String getKey(List<ActivityRuleMap> ruleList) {

        String key = RedisKeyConst.ACTIVITY_KEY.getPrefix();
        if (ruleList.size() > 0) {
            for (int i = 0; i < ruleList.size(); i++) {
                key += "_" + String.valueOf(ruleList.get(i).getPriority());
            }
            return key;
        } else {
            return null;
        }
    }


    public JsonResult redisSave(Activity activity) {
        JsonResult result = new JsonResult();

        //clear some field
        activity.setModified(null);
        activity.setCreated(null);
        activity.setContent(null);
        activity.setUrl(null);
        activity.setCreated(null);
        activity.setModified(null);
        try {
            String key = getRedisKey(activity);
            List<Activity> activityList = new ArrayList<>();
            if (StringUtils.isNotBlank(key)) {
                String jsonstr = redisService.get(key);
                if (StringUtils.isBlank(jsonstr)) {
                    activityList.add(activity);
                } else {
                    activityList = JSONArray.parseArray(jsonstr, Activity.class);
                    for (int i = 0; i < activityList.size(); i++) {
                        if (activityList.get(i).getId().intValue() == activity.getId().intValue()) {
                            activityList.remove(i);
                            i--;
                        }
                    }
                    activityList.add(activity);
                }

                if (activityList.size() > 0) {
                    redisService.save(key, JSONArray.toJSONString(activityList));
                }

            }

            result.setObj(activity).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return result;
    }


    public String getRedisKey(Activity activity) {
        List<ActivityRuleMap> activityRuleMapList = activity.getRuleConfig();
        String key = "";
        if (activityRuleMapList != null && activityRuleMapList.size() > 0) {
            activityRuleMapList.sort(null);
            key = getKey(activityRuleMapList);
        }
        return key;
    }

    public String getRedisKey(List<ActivityRuleMap> activityRuleMapList) {
        String key = "";
        if (activityRuleMapList != null && activityRuleMapList.size() > 0) {
            activityRuleMapList.sort(null);
            key = getKey(activityRuleMapList);
        }
        return key;
    }


    public JsonResult redisDelete(Activity activity) {
        JsonResult result = new JsonResult();
        List<Activity> activityList = null;
        String key = getRedisKey(activity);
        try {
            String jsonStr = redisService.get(key);
            if (StringUtils.isNotBlank(jsonStr)) {
                activityList = JSONArray.parseArray(jsonStr, Activity.class);
                if (activityList != null && activityList.size() > 0) {
                    for (int i = 0; i < activityList.size(); i++) {
                        if (activityList.get(i).getId().intValue() == activity.getId().intValue()) {
                            activityList.remove(i);
                            i--;
                        }
                    }
                    if (activityList.size() > 0) {
                        redisService.save(key, JSONArray.toJSONString(activityList));
                    } else {
                        redisService.delete(key);
                    }
                    result.setObj(activityList).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
                }
            } else {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(e.getMessage());
        }
        return result;
    }

    public long redisDelete(String key) {
        long count = 0;
        try {
            count = redisService.delete(key);
        } catch (Exception e) {
            log.error("delete activity from redis fail, key [{}],error msg [{}]", key, e);
            count = -1;
        }
        return count;
    }


    public List<Activity> redisGet(String key) {
        List<Activity> activityList = null;
        try {
            String JsonStr = redisService.get(key);
            if (StringUtils.isNotBlank(JsonStr)) {
                activityList = JSONArray.parseArray(JsonStr, Activity.class);
            }
        } catch (Exception e) {
            log.error("get activity from redis fail, key [{}],error msg [{}]", key, e);
        }
        return activityList;
    }


    public List<Activity> getProcessingActivity(String saasId) {
        List finalList = new ArrayList();
        try {
            Map<String, List<Activity>> activityMap = new ConcurrentHashMap<>();
            // TODO 这里建议优化为 hset 的数据格式
            String keys = RedisKeyConst.ACTIVITY_KEY + "_*";
            Set<String> keyList = redisService.keys(keys);
            log.info("getProcessingActivity saasId[{}] keyList...{}", saasId, JSONObject.toJSONString(keyList));
            //获取所有活动
            for (String key : keyList) {
                String JsonStr = redisService.get(key);
                if (StringUtils.isNotBlank(JsonStr)) {
                    activityMap.put(key, JSONArray.parseArray(JsonStr, Activity.class));
                }
            }

            //log.info("getProcessingActivity activityMap saasId[{}] before...{}", saasId, JSON.toJSONString(activityMap));
            //过滤进行中活动
            for (List<Activity> list : activityMap.values()) {
                if (!CollectionUtils.isEmpty(list)) {
                    List<Activity> collect = list.stream()
                            .filter(f -> f != null)
                            // .filter(f -> SaasId.fromId(f.getSaas_id()) == saasId)
                            .filter(f -> f.getStatus() == ActivityConstant.Status.PROCESSING.getCode())
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(list)) {
                        finalList.addAll(collect);
                    }
                }
            }

        } catch (Exception e) {
            log.error("getProcessingActivity activity process fail.", e);
        }
        log.info("getProcessingActivity activityMap final saasId[{}]...finalList{}", saasId, JSON.toJSONString(finalList));
        return finalList;
    }
}
