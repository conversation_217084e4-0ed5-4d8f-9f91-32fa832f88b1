package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;

import java.util.List;

public interface ActivityLotteryDetailConfigService {

    /**
     * 查询奖池的奖品
     * @param lotteryId
     * @return
     */
    List<ActivityLotteryDetailConfig> findItemByLotteryId(String lotteryId);

    /**
     * 查询奖池的奖品
     * @return
     */
    ActivityLotteryDetailConfig findItemById(String id);
}
