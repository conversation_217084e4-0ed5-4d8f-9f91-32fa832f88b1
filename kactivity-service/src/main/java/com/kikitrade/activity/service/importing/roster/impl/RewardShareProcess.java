package com.kikitrade.activity.service.importing.roster.impl;

import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
@Order(3)
public class RewardShareProcess extends AbstractRewardProcess {

    @Override
    boolean support(ActivityEntity activityEntity, ActivityBatch activityBatch, CustomerCacheDTO customerDO) {
        return false;
    }

    @Override
    void doProcess(ActivityEntity activityEntity, ActivityBatch activityBatch, ActivityCustomReward activityCustomReward, CustomerCacheDTO customerDO) {
        activityCustomReward.setShard(Integer.parseInt(activityCustomReward.getCustomerId()) % activityBatch.getShardCount());
    }
}
