package com.kikitrade.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCumulateItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LotteryConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.ActivityCumulateConfig;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.lottery.LotteryCommonService;
import com.kikitrade.activity.service.model.CouponClaimInfo;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.framework.ons.OnsProperties;
import com.kikitrade.order.api.RemoteOrderService;
import com.kikitrade.order.model.OrderDTO;
import com.kikitrade.order.model.OrderEventDTO;
import com.kikitrade.order.model.constant.OrderEventEnum;
import com.kikitrade.order.model.constant.OrderStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem.DEFAULT_CYCLE;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/2 14:31
 */
@Component
@Slf4j
public class OrderEventListener implements OnsMessageListener {

    @Resource
    private TopicConfig topicConfig;
    @Resource
    private OnsProperties onsProperties;
    @Resource
    private RemoteOrderService remoteOrderService;
    @Resource
    private RemoteAssetOperateService remoteAssetOperateService;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;
    @Resource
    private ActivityLotteryItemBuilder activityLotteryItemBuilder;
    @Resource
    private ActivityCumulateItemBuilder activityCumulateItemBuilder;
    @Resource
    private ActivityEventAction activityEventAction;
    @Resource
    private LotteryCommonService lotteryCommonService;
    @Resource
    private LotteryConfigBuilder lotteryConfigBuilder;
    @Resource
    private RedisService redisService;

    @Override
    public String topic() {
        return topicConfig.getOrderEvent();
    }

    @Override
    public boolean traffic() {
        return onsProperties != null && onsProperties.isEnableTraffic();
    }

    @Override
    public Action consume(Message message, ConsumeContext context) {
        String body = new String(message.getBody());
        OrderEventDTO orderEventDTO = JSON.parseObject(body, OrderEventDTO.class);
        if(!orderEventDTO.getOrderId().startsWith(OrderEventEnum.lottery.getPrefix())
                && !orderEventDTO.getOrderId().startsWith(OrderEventEnum.redeem.getPrefix())){
            return Action.CommitMessage;
        }
        OrderDTO order = remoteOrderService.getOrder(orderEventDTO.getOrderId());
        if(order == null || order.getStatus() != OrderStatusEnum.pending){
            log.info("OrderEventListener order not match:{},{}", body, order);
            return Action.CommitMessage;
        }

        log.info("OrderEventListener consume order:{}", JSON.toJSONString(order));
        //发放奖励，扣减积分
        try {
            if (OrderEventEnum.redeem == order.getEvent()) {
                CouponClaimInfo config = JSON.parseObject(order.getParam(), CouponClaimInfo.class);
                reward(config);
            } else {
                ActivityLotteryItem activityLotteryItem = JSON.parseObject(order.getParam(), ActivityLotteryItem.class);
                LotteryConfig lotteryConfig = lotteryConfigBuilder.findByCode(activityLotteryItem.getCode());
                String cycleStart = lotteryCommonService.getLimitStartTime(lotteryConfig);
                boolean isSucceed = tryLottery(activityLotteryItem, cycleStart, lotteryConfig.getLimitTimes());
                if( isSucceed ){
                    if(activityLotteryItem.isCumulate()){
                        checkAndRewardCumulate(activityLotteryItem);
                    }
                    doTask(activityLotteryItem,orderEventDTO.getOrderId());
                }
            }
            remoteOrderService.confirmOrder(order.getOrderId());
        }catch (Exception ex){
            log.error("OrderEventListener error:{}", order.getParam(), ex);
            return Action.ReconsumeLater;
        }
        return Action.CommitMessage;


    }

    private boolean tryLottery(ActivityLotteryItem activityLotteryItem, String cycleStart, long limitCnt) throws Exception{

        LauncherParameter launcherParameter = buildRewardParam(activityLotteryItem);
        String cycleKey = RedisKeyConst.ACTIVITY_LOTTERY_CYCLE_COUNT.getKey(String.format("%s:%s:%s:%s", activityLotteryItem.getSaasId(),
                activityLotteryItem.getCustomerId(), activityLotteryItem.getCode(), cycleStart));
        try {
            Long cycleCount = redisService.increaseBy(cycleKey,1);
            if( cycleCount == 1 ){
                redisService.expire(cycleKey, 7 * 24 * 60 * 60);
            }
            if( cycleCount > limitCnt){
                log.info("[OrderEventListener] lottery count limit. count:{},activityLotteryItem:{}",
                        cycleCount, JSON.toJSONString(activityLotteryItem));
                return false;
            }
            log.info("[OrderEventListener] eventAction send reward: {}", JSON.toJSONString(launcherParameter));
            activityRealTimeRewardTccService.reward(launcherParameter);
        } catch (Exception e) {
            log.error("[OrderEventListener] tryLottery error:{}", launcherParameter, e);
            redisService.decreaseBy(cycleKey,1);
            throw e;
        }

        activityLotteryItem.setStatus(ActivityConstant.LotteryStatus.SUCCESS.name());
        activityLotteryItemBuilder.update(activityLotteryItem);
        return true;
    }

    private void checkAndRewardCumulate(ActivityLotteryItem activityLotteryItem){
        String cumulateKey = RedisKeyConst.ACTIVITY_LOTTERY_CUMULATE_COUNT.getKey(String.format("%s:%s:%s", activityLotteryItem.getSaasId(),
                activityLotteryItem.getCustomerId(), activityLotteryItem.getCode()));
        try{
            Long count = redisService.increaseBy(cumulateKey,1);
            ActivityCumulateItem activityCumulateItem = lotteryCommonService.getActivityCumulate(activityLotteryItem.getCustomerId(),
                    activityLotteryItem.getCode(), activityLotteryItem.getSaasId(), count);

            if(activityCumulateItem == null){
                return;
            }
            rewardCumulate(activityCumulateItem);

        }catch (Exception e){
            log.error("[OrderEventListener] checkAndRewardCumulate error:{}", activityLotteryItem, e);
            redisService.decreaseBy(cumulateKey,1);
        }
    }

    private void rewardCumulate(ActivityCumulateItem activityCumulateItem){

        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setSaasId(activityCumulateItem.getSaasId());
        reward.setBatchId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD));
        reward.setCustomerId(activityCumulateItem.getCustomerId());
        reward.setBusinessId(TimeUtil.getDataStr(TimeUtil.parseUnittime(activityCumulateItem.getCreated()), TimeUtil.YYYYMMDDHHmmss));
        reward.setSeq("lottery:" + reward.getBusinessId());
        //计算奖励金额
        reward.setAmount(activityCumulateItem.getRewardAmount().toPlainString());
        reward.setRewardType(activityCumulateItem.getRewardCurrency());
        reward.setCurrency(activityCumulateItem.getRewardCurrency());
        reward.setBusinessType(AssetBusinessType.ACTIVITY_LOTTERY.getCodeDesc());
        reward.addExtendParam("desc", "lottery cumulate award");
        LauncherParameter launcherParameter = new LauncherParameter();
        launcherParameter.setActivityCustomReward(reward);
        launcherParameter.setProvideType(ActivityTaskConstant.ProvideType.auto);
        try {
            log.info("[OrderEventListener] eventAction send rewardCumulate: {}", JSON.toJSONString(launcherParameter));
            activityRealTimeRewardTccService.reward(launcherParameter);
            activityCumulateItem.setStatus(ActivityConstant.LotteryStatus.SUCCESS.name());
            activityCumulateItem.setRewardTime(System.currentTimeMillis());
            activityCumulateItemBuilder.update(activityCumulateItem);
        } catch (Exception e) {
            log.error("rewardCumulate error:{}", launcherParameter, e);
        }
    }


    private void reward(CouponClaimInfo couponClaimInfo) throws Exception{

        ActivityTaskItem activityTaskItem = new ActivityTaskItem();
        activityTaskItem.setCustomerId(couponClaimInfo.getCustomerId());
        activityTaskItem.setTaskId(couponClaimInfo.getTaskId());
        activityTaskItem.setCycle(DEFAULT_CYCLE);
        activityTaskItem.setEvent(AssetBusinessType.CLAIM_REDEEM.getCodeDesc());
        activityTaskItem.setTargetId(couponClaimInfo.getCouponCode());
        activityTaskItem.setStatus(ActivityConstant.TaskStatusEnum.DONE.name());
        activityTaskItem.setCreated(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        boolean success = activityTaskItemBuilder.insert(activityTaskItem);
        if(!success){
            //插入记录失败不会下发积分,避免重复兑换
            log.warn("reward CouponClaimInfo, create taskItem failed. couponClaimInfo:{}", JSON.toJSONString(couponClaimInfo));
            return;
        }
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setSaasId(couponClaimInfo.getSaasId());
        reward.setBatchId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD));
        reward.setCustomerId(couponClaimInfo.getCustomerId());
        reward.setBusinessId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHmmss));
        reward.setSeq("redeem:" + reward.getBusinessId());
        //计算奖励金额
        reward.setAmount(couponClaimInfo.getAwardAmount());
        reward.setRewardType(couponClaimInfo.getAwardCurrency());
        reward.setCurrency(couponClaimInfo.getAwardCurrency());
        reward.setBusinessType(AssetBusinessType.CLAIM_REDEEM.getCodeDesc());
        reward.addExtendParam("desc", "redeem claim");
        LauncherParameter launcherParameter = new LauncherParameter();
        launcherParameter.setActivityCustomReward(reward);
        launcherParameter.setProvideType(ActivityTaskConstant.ProvideType.auto);
        try {
            log.info("[task] eventAction send CouponClaimInfo reward: {}", JSON.toJSONString(launcherParameter));
            activityRealTimeRewardTccService.reward(launcherParameter);
        } catch (Exception e) {
            log.error("reward CouponClaimInfo error:{}", launcherParameter, e);
            throw e;
        }
    }

    private void doTask(ActivityLotteryItem activityLotteryItem, String orderId) throws Exception{

        ActivityEventMessage activityEventMessage = new ActivityEventMessage();
        activityEventMessage.setCustomerId(activityLotteryItem.getCustomerId());
        activityEventMessage.setEventCode("activity_lottery");
        activityEventMessage.setEventTime(System.currentTimeMillis());
        activityEventMessage.setTargetId(orderId);
        Map<String,Object> body = new HashMap<>();
        body.put("vipLevel", "NORMAL");
        body.put("saasId", activityLotteryItem.getSaasId());
        activityEventMessage.setBody(body);
        log.info("[OrderEventListener] doTask activityEventMessage: {}", JSON.toJSONString(activityEventMessage));
        ActivityResponse response = activityEventAction.action(activityEventMessage);
        if(!response.getCode().isSuccess()){
            throw new ActivityException(response.getCode());
        }

    }

    private LauncherParameter buildRewardParam(ActivityLotteryItem activityLotteryItem){
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setSaasId(activityLotteryItem.getSaasId());
        reward.setBatchId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD));
        reward.setCustomerId(activityLotteryItem.getCustomerId());
        reward.setBusinessId(TimeUtil.getDataStr(TimeUtil.parseUnittime(activityLotteryItem.getCreated()), TimeUtil.YYYYMMDDHHmmss));
        reward.setSeq("lottery:" + reward.getBusinessId());
        //计算奖励金额
        reward.setAmount(activityLotteryItem.getAmount().toPlainString());
        reward.setRewardType(activityLotteryItem.getCurrency());
        reward.setCurrency(activityLotteryItem.getCurrency());
        reward.setBusinessType(AssetBusinessType.ACTIVITY_LOTTERY.getCodeDesc());
        reward.addExtendParam("desc", "lottery award");

        LauncherParameter launcherParameter = new LauncherParameter();
        launcherParameter.setActivityCustomReward(reward);
        launcherParameter.setProvideType(ActivityTaskConstant.ProvideType.auto);
        return launcherParameter;
    }
}
