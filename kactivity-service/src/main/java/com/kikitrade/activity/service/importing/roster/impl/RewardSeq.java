package com.kikitrade.activity.service.importing.roster.impl;

import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import lombok.Getter;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-16 16:47
 */
@Getter
@Component
public class RewardSeq {

    @Resource
    private RedisService redisService;

    private String batchId;

    private ActivityConstant.SeqPrefix prefix;

    public RewardSeq of(ActivityConstant.SeqPrefix prefix, String batchId){
        this.prefix = prefix;
        this.batchId = batchId;
        return this;
    }

    public String getValue(){
        return String.format("%s:%s", prefix.getPrefix(), redisService.increaseBy(String.format("%s:%s", prefix.name(), batchId), 1));
    }

    public void destroy(){
        redisService.del(String.format("%s:%s", prefix.name(), batchId));
    }
}
