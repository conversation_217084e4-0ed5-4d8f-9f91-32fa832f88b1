package com.kikitrade.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.dal.tablestore.model.ActivityJoinItem;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReceiveDTO;
import com.kikitrade.activity.luck.service.business.ReceiveService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityJoinItemService;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.kcustomer.api.constants.CustomerEvent;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-15 19:09
 */
@Service
public class CustomerEventListener implements OnsMessageListener {

    @Resource
    private TopicConfig topicConfig;
    @Resource
    private ReceiveService receiveService;
    @Resource
    private ActivityJoinItemService activityJoinItemService;
    @Resource
    private CustomerService customerService;

    @Override
    public String topic() {
        return topicConfig.getTopicActivityCustomer();
    }

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        CustomerEventMessage customerEventMessage = JSON.parseObject(new String(message.getBody()), CustomerEventMessage.class);
        if(CustomerEvent.EventType.EmailRegister.name().equals(customerEventMessage.getEventType())
                || CustomerEvent.EventType.PhoneRegister.name().equals(customerEventMessage.getEventType())){
            CustomerCacheDTO customer = customerService.getById(customerEventMessage.getCustomerId());
            ActivityJoinItem activityJoinItem = activityJoinItemService.queryByUserName(customer.getUserName());
            if(activityJoinItem != null && ActivityConstant.ActivityJoinStatus.APPENDING.name().equals(activityJoinItem.getStatus())){
                LuckFortuneReceiveDTO dto = new LuckFortuneReceiveDTO();
                dto.setReleaseId(activityJoinItem.getBusinessCode());
                dto.setReceiveCode(customer.getUserName());
                try{
                    receiveService.receive(dto);
                }catch (Exception ex){}
            }
        }
        return Action.CommitMessage;
    }
}
