package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kikitarde.contract.generated.IERC721;
import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.dal.tablestore.builder.ClaimItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ClaimItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ClaimItemService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;
import org.web3j.tx.ReadonlyTransactionManager;
import org.web3j.utils.Async;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicReference;

import static com.kikitrade.activity.api.exception.ActivityExceptionType.CLAIM_REPEAT;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/9 14:17
 */
@Service
@Slf4j
public class ClaimItemServiceImpl implements ClaimItemService {

    @Resource
    private ClaimItemBuilder claimItemBuilder;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private ThreePlatformProperties threePlatformProperties;
    private Web3j web3j;

    private static final String ZERO_ADDRESS = "******************************************";
    public static final ScheduledExecutorService scheduledExecutorService = Async.defaultExecutorService();

    @PostConstruct
    public void init() {
        HttpService web3jService = new HttpService(threePlatformProperties.getThirdWeb().getRpcUrlEth());
        web3jService.addHeader("x-secret-key", threePlatformProperties.getThirdWeb().getSecretKey());
        web3j = Web3j.build(web3jService, 10000L, scheduledExecutorService);
    }

    @Override
    public Boolean isContinue(String businessType) {
        return "nft".equals(businessType);
    }

    @Override
    public Boolean allowClaim(String businessType, String customerId, String address, List<String> addresses) {
        boolean exist = existClaim(businessType, customerId);
        if(exist){
            throw new com.kikitrade.activity.api.exception.ActivityException(CLAIM_REPEAT);
        }
        if (CollectionUtils.isEmpty(addresses)) {
            return false;
        }
        Set<String> addressSet = new HashSet<>(addresses);
        return addressSet.stream()
                .filter(Objects::nonNull)
                .map(this::getOdyNftCounts)
                .anyMatch(count -> count != null && count.compareTo(BigInteger.ZERO) > 0);
    }

    @Override
    public ClaimResponse claim(String businessType, String address, String customerId, String code, String saasId, List<String> addresses) throws ActivityException {
        boolean exist = existClaim(businessType, customerId);
        if(exist){
            throw new com.kikitrade.activity.api.exception.ActivityException(CLAIM_REPEAT);
        }
        ClaimResponse response = new ClaimResponse();
        Set<String> addressSet = new HashSet<>(addresses);
        boolean hasOdyNft = addressSet.stream()
                .filter(Objects::nonNull)
                .map(this::getOdyNftCounts)
                .anyMatch(count -> count != null && count.compareTo(BigInteger.ZERO) > 0);

        if(!hasOdyNft){
            log.info("nft not match :{}", address);
            response.setSuccess(false);
            return response;
        }
        long count = claimItemBuilder.countByIds(businessType, customerId, OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if(count > 0){
            log.info("nft all claim:{}", address);
            response.setSuccess(false);
            return response;
        }
        List<ClaimItem> claimItems = new ArrayList<>();
        boolean success = false;
        ClaimItem claimItem = new ClaimItem();
        claimItem.setBusinessType(businessType);
        claimItem.setCode(customerId);
        claimItem.setCreated(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        claimItem.setTs(System.currentTimeMillis());
        claimItem.setCustomerId(customerId);
        claimItem.setSaasId(saasId);
        Map<String,String> map = new HashMap<>();
        map.put("amount", String.valueOf(kactivityProperties.getNftRewardPoint()));
        map.put("currency", ActivityConstant.AwardTypeEnum.POINT.name());
        claimItem.setAward(JSON.toJSONString(map));
        claimItems.add(claimItem);
        Boolean insert = claimItemBuilder.insert(claimItem);
        success = success || insert;
        if(success){
            reward(claimItems.get(0), new BigDecimal(kactivityProperties.getNftRewardPoint()), ActivityConstant.AwardTypeEnum.POINT.name());
        }
        response.setSuccess(true);
        return response;
    }

    private boolean existClaim(String businessType, String customerId) {
        return claimItemBuilder.exist(businessType, customerId, OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    private List<String> getNfts(List<String> addresses) {
        List<String> nfts = new ArrayList<>();
        for(String address : addresses){
            List<String> nft = getNfts(address);
            if(nft != null && !nft.isEmpty()){
                nfts.addAll(nft);
            }
        }
        return nfts;
    }

    public BigInteger getOdyNftCounts(String address) {
        return balanceOf(web3j, threePlatformProperties.getMugen().getOdysseyPassNFTAddress(), address);
    }

    public BigInteger balanceOf(Web3j web3j, String ca, String wallet) {
        IERC721 ierc721 = load(web3j, ca);
        try {
            BigInteger balanceOf = ierc721.balanceOf(wallet).send();
            log.info("ClaimItemServiceImpl.balanceOf, ca: {}, wallet: {}, balanceOf:{}", ca, wallet, balanceOf);
            return balanceOf;
        } catch (Exception e) {
            log.error("ClaimItemServiceImpl.balanceOf error", e);
        }
        return null;
    }

    private IERC721 load(Web3j web3j, String ca) {
        return IERC721.load(ca, web3j, new ReadonlyTransactionManager(web3j, ZERO_ADDRESS), null);
    }

    private List<String> getNfts(String address) {
        String url = String.format("https://api.thirdweb.com/v1/wallets/%s/nfts?chainId=%s&contractAddresses=%s", address, kactivityProperties.getNftChain(), threePlatformProperties.getMugen().getOdysseyPassNFTAddress());
        HttpGet request = new HttpGet(url);
        request.addHeader("accept", "application/json");
        request.addHeader("x-client-id", threePlatformProperties.getThirdWeb().getClientId());
        request.addHeader("x-secret-key", threePlatformProperties.getThirdWeb().getSecretKey());

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000) // 连接超时，5秒
                .setSocketTimeout(threePlatformProperties.getThirdWeb().getTimeout()) // 读取超时，10秒
                .setConnectionRequestTimeout(2000) // 从连接池中获取连接的超时时间
                .build();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();

        try (CloseableHttpResponse response = httpClient.execute(request)){
            String responseBody = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = JSON.parseObject(responseBody);
            log.info("getNft response:{}", responseBody);
            JSONObject result = jsonObject.getJSONObject("result");
            JSONArray nfts = result.getJSONArray("nfts");
            if (nfts != null && !nfts.isEmpty()) {
                Set<String> tokenIds = new HashSet<>();
                for (int i = 0; i < nfts.size(); i++) {
                    JSONObject nft = nfts.getJSONObject(i);
                    if (nft != null && Integer.parseInt(nft.getString("balance")) > 0) {
                        tokenIds.add(nft.getString("token_id"));
                    }
                }
                log.info("getNft response:{}", tokenIds);
                return tokenIds.stream().toList();
            }
        }catch (Exception e){
            log.error("getNfts error. address: {}", address, e);
        }
        return Collections.emptyList();
    }

    private void reward(ClaimItem claimItem, BigDecimal amount, String currency) {
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setSaasId(claimItem.getSaasId());
        reward.setBatchId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD));
        reward.setCustomerId(claimItem.getCustomerId());
        reward.setBusinessId(TimeUtil.getDataStr(TimeUtil.parseUnittime(claimItem.getTs()), TimeUtil.YYYYMMDDHHmmss) + claimItem.getCustomerId().substring(claimItem.getCustomerId().length() - 4));
        reward.setSeq("claim_nft:" + reward.getBusinessId());
        //计算奖励金额
        reward.setAmount(amount.toPlainString());
        reward.setRewardType(currency);
        reward.setCurrency(currency);
        reward.setBusinessType(AssetBusinessType.REWARD.getCodeDesc());
        reward.addExtendParam("desc", "nft claim");
        LauncherParameter launcherParameter = new LauncherParameter();
        launcherParameter.setActivityCustomReward(reward);
        launcherParameter.setProvideType(ActivityTaskConstant.ProvideType.auto);
        try {
            log.info("[task] eventAction send reward: {}", launcherParameter);
            activityRealTimeRewardTccService.reward(launcherParameter);
        } catch (Exception e) {
            log.error("reward error:{}", launcherParameter, e);
        }
    }
}
