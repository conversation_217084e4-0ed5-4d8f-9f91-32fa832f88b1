package com.kikitrade.activity.service.question;

import com.kikitrade.activity.api.model.RespondentIdentity;
import com.kikitrade.activity.api.model.request.UserAnswerRequest;
import com.kikitrade.activity.api.model.response.QuestionSetsResponse;
import com.kikitrade.activity.api.model.response.QuestionListResponse;
import com.kikitrade.activity.api.model.response.QuestionSettleResponse;
import com.kikitrade.activity.model.exception.ActivityException;

public interface QuestionService {

    /**
     * get customer question sets
     * @param identity
     * @return
     */
    QuestionSetsResponse getQuestionSets(RespondentIdentity identity);

    /**
     * acquire a set of questions
     * @param identity
     * @return
     */
    QuestionListResponse acquireQuestions(RespondentIdentity identity) throws ActivityException;

    /**
     * submit a set of questions
     * @param identity
     * @param userAnswerRequest
     * @return
     */
    QuestionSettleResponse submitQuestions(RespondentIdentity identity, UserAnswerRequest userAnswerRequest) throws ActivityException;

}
