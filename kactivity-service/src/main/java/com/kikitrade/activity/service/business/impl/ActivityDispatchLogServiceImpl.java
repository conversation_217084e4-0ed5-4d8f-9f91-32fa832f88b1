package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityDispatchLogDao;
import com.kikitrade.activity.service.business.ActivityDispatchLogService;
import com.kikitrade.activity.dal.mysql.model.ActivityDispatchLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityDispatchLogServiceImpl implements ActivityDispatchLogService {


    @Resource
    private ActivityDispatchLogDao activityDispatchLogDao;


    @Override
    public int save(ActivityDispatchLog activityDispatchLog)  {
        int count = 0;
        try {
            count = activityDispatchLogDao.insert(activityDispatchLog);

        } catch (Exception e) {
            log.error("ActivityDispatchLogserviceimpl save process failed.");
        }
        return count;
    }

    @Override
    public int updateStatus(String tran_date, Integer activity_id, Integer dispatch_status) {
        int count = 0;
        try {
            count = activityDispatchLogDao.updateStatus(tran_date, activity_id, dispatch_status);

        } catch (Exception e) {
            log.error("ActivityDispatchLogserviceimpl updateStatus process failed.", e);
        }
        return count;
    }

    @Override
    public List<ActivityDispatchLog> findAll() {
        List<ActivityDispatchLog> activityDispatchLogList = null;
        try {
            activityDispatchLogList = activityDispatchLogDao.findAll();

        } catch (Exception e) {
            log.error("ActivityDispatchLogserviceimpl findAll process failed.", e);
        }
        return activityDispatchLogList;
    }

    @Override
    public ActivityDispatchLog findByActivityId(String tran_date, Integer activity_id) {
        ActivityDispatchLog activityDispatchLog = null;
        try {
            activityDispatchLog = activityDispatchLogDao.findByActivityId(tran_date, activity_id);

        } catch (Exception e) {
            log.error("ActivityDispatchLogserviceimpl findByActivityId process failed.", e);
        }
        return activityDispatchLog;
    }


}
