package com.kikitrade.activity.service.importing.roster;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;

import java.util.List;

public interface RewardImportingProcess {

    void process(ActivityEntity activityEntity, ActivityBatch activityBatch, ActivityCustomReward activityCustomReward, CustomerCacheDTO customerDO);
}
