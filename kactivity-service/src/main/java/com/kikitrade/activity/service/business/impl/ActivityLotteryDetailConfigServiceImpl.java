package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryDetailConfigDao;
import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryStoreLogDao;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryStoreLog;
import com.kikitrade.activity.service.business.ActivityLotteryDetailConfigService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ActivityLotteryDetailConfigServiceImpl implements ActivityLotteryDetailConfigService {

    @Resource
    private ActivityLotteryDetailConfigDao activityLotteryDetailConfigDao;
    @Resource
    private ActivityLotteryStoreLogDao activityLotteryStoreLogDao;
    @Resource
    private KactivityProperties kactivityProperties;

    /**
     * 查询奖池的奖品
     *
     * @param lotteryId
     * @return
     */
    @Override
    public List<ActivityLotteryDetailConfig> findItemByLotteryId(String lotteryId) {
        Example example = new Example(ActivityLotteryDetailConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lotteryId", lotteryId);
        return activityLotteryDetailConfigDao.selectByExample(example);
    }

    /**
     * 查询奖池的奖品
     *
     * @param id
     * @return
     */
    @Override
    public ActivityLotteryDetailConfig findItemById(String id) {
        ActivityLotteryDetailConfig config = new ActivityLotteryDetailConfig();
        config.setId(id);
        return activityLotteryDetailConfigDao.selectOne(config);
    }
}
