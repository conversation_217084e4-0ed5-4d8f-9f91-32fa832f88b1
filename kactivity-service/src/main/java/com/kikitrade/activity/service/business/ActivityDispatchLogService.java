package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityDispatchLog;
import java.util.List;

public interface ActivityDispatchLogService {


    int save(ActivityDispatchLog activityDispatchLog);

    int updateStatus(String tran_date, Integer activity_id, Integer dispatch_status);

    List<ActivityDispatchLog> findAll();

    ActivityDispatchLog findByActivityId(String tran_date, Integer activity_id);
}
