package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityCustomerCurrentInterest;

import java.util.List;

public interface ActivityCustomerInterestService {


    List<ActivityCustomerCurrentInterest> findAll(String transDate, Integer offset, Integer limit);

    int updateStatus(String transDate, String customerId, String currency, Integer status);

    Long countByStatus(String transDate, Integer status);
}
