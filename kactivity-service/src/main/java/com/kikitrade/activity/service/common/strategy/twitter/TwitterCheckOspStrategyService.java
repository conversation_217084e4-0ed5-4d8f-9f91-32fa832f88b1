package com.kikitrade.activity.service.common.strategy.twitter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 17:10
 */
@Component
@Slf4j
public class TwitterCheckOspStrategyService implements TwitterSaasStrategyService{

    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public String strategy() {
        return SaasStrategyConstant.TwitterStrategyEnum.CHECK_OSP.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        //登陆和授权不是同一个twitter账户,
        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(request.getSaasId(), request.getCustomerId());
        String xId = getXId(request.getSaasId(), customerBindDTO.getCid());
        if(!request.getAuthId().equals(xId) && !request.getAuthHandleName().equals(xId)){
            log.info("twitter check osp not same:{}", request);
            throw new com.kikitrade.activity.model.exception.ActivityException(ActivityResponseCode.AUTH_NO_SAME_LOGIN, ActivityResponseCode.AUTH_NO_SAME_LOGIN.getKey());
        }
    }

    private String getXId(String saasId,String ownerId) {
        try {
            SaasConfig config = SaasConfigLoader.getConfig(saasId);
            HttpUrl.Builder urlBuilder = HttpUrl.parse(config.getApiHost() + "/v2/s2s/owners/"+ownerId+"/binds/TWITTER").newBuilder();
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("os-app-id", config.getOspAppId())
                    .add("os-Api-Key", config.getOspAppKey());
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                log.info("[Osp] getXId response:{}", jsonObject);
                return Optional.ofNullable(jsonObject.getJSONObject("data"))
                        .map(o -> o.getString("name")).orElse((null));
            }
            log.info("[Osp] getXId failed. cid:{}, response:{}", ownerId, response);
            return null;
        } catch (Exception e) {
            log.error("[OspConnectTGRequest] getTGId Exception:{}, error:", ownerId, e);
            return null;
        }
    }
}
