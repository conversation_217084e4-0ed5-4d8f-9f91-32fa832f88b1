package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityCustomerDiscountDao;
import com.kikitrade.activity.dal.mysql.model.ActivityCustomerDiscount;
import com.kikitrade.activity.service.business.ActivityCustomerDiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityCustomerDiscountServiceImpl implements ActivityCustomerDiscountService {

    @Resource
    private ActivityCustomerDiscountDao activityCustomerDiscountDao;


    @Override
    public List<ActivityCustomerDiscount> findAll(String discountDate, Integer offset, Integer limit) {
        List<ActivityCustomerDiscount> activityCustomerFeeList = null;
        try {
            activityCustomerFeeList = activityCustomerDiscountDao.findAll(discountDate, offset, limit);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl findAll process failed.", e);
        }
        return activityCustomerFeeList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(String discountDate, String customer_id, String investmentProductId, Integer status) {
        int count = 0;
        try {
            count = activityCustomerDiscountDao.updateStatus(discountDate, customer_id, investmentProductId, status);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl updateStatus process failed.", e);
            throw e;
        }
        return count;
    }

    @Override
    public Long countByStatus(String discountDate, Integer status) {
        try {
            return activityCustomerDiscountDao.countByStatus(discountDate, status);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl countByTypeAndTye process failed.discountDate{} status:{}", discountDate, status, e);
            return -1L;
        }
    }


}
