package com.kikitrade.activity.service.business.impl;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendCallback;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.activity.dal.mysql.model.*;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityUserTotalDataBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityRecords;
import com.kikitrade.activity.dal.tablestore.model.ActivityUserTotalData;
import com.kikitrade.activity.model.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.*;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.service.engine.process.ActivityBatchProcessExecutor;
import com.kikitrade.activity.service.meta.ActivityBatchInfoService;
import com.kikitrade.activity.service.mq.TopicConfig;
import com.kikitrade.framework.common.saas.SaasId;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import jakarta.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
@DependsOn("propertiesUtils")
public class ActivityBatchServiceImpl implements ActivityBatchService {

    //小片发送笔数，需要考虑Http get请求报文长度（调度节点每次向执行节点发送笔数）
    private static int PAGE_NUM = 100;
    private static boolean ACTIVITY_SCHEDULE_SINGLE = true;

    @Resource
    private ActivityService activityService;
    @Resource
    private ActivityBatchProcessExecutor activityBatchProcessExecutor;
    @Resource
    private ActivityRecordsBuilder activityRecordsAccessorBuilder;
    @Resource
    private ActivityCustomerFeeService activityCustomerFeeService;
    @Resource
    private ActivityBatchInfoService activityBatchInfoService;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private ActivityDispatchLogService activityDispatchLogService;
    @Resource
    private ActivityCustomerDiscountService activityCustomerDiscountService;

    @Resource
    private ActivityCustomerInterestService activityCustomerInterestService;
    @Resource
    private ActivityUserTotalDataBuilder activityUserTotalDataBuilder;

    @Resource
    private TopicConfig topicConfig;

    private static final DateFormat sdf = new SimpleDateFormat("yyyyMMdd");


    @Override
    public void jobDispatch(Integer activity_id, Integer execute_type, Integer status, Integer limit, Date deadline, String notifyUrl) {

        log.info("ActivityBatchServiceImpl jobDispatch start~~~~~~~~~~~~");

        String tran_date = sdf.format(new Date());
        Integer activityId = activity_id != null ? activity_id : -1;
        //save activityDispatchLog
        ActivityDispatchLog activityDispatchLog = new ActivityDispatchLog();
        activityDispatchLog.setTranDate(tran_date);
        activityDispatchLog.setDispatchStatus(ActivityConstant.BatchSatus.RECORDED.getCode());
        activityDispatchLog.setActivityId(activityId);
        activityDispatchLog.setExecuteType(execute_type);
        activityDispatchLog.setStatus(status);
        activityDispatchLog.setLimit(limit);
        activityDispatchLog.setDeadline(deadline);
        activityDispatchLog.setNotifyUrl(notifyUrl);

        boolean flag = true;
        int count = 0;
        ActivityDispatchLog activityDispatchLog1 = activityDispatchLogService.findByActivityId(tran_date, activityId);
        if (activityDispatchLog1 != null) {
            //失败补偿处理
            if (activityDispatchLog1.getDispatchStatus().equals(ActivityConstant.BatchSatus.FAILED.getCode())) {
                count = 1;
            }
        } else {
            count = activityDispatchLogService.save(activityDispatchLog);
        }

        if (count == 0) {
            log.info("ActivityBatchServiceImpl jobDispatch already completed,skip processs~~~~~~~~~~~~~~{}", JSONObject.toJSONString(activityDispatchLog1));
            return;
        }

        try {
            //根据活动ID分发
            if (activity_id != null) {
                dispatchByActivityIdAndType(activity_id, execute_type, status, limit, deadline, notifyUrl);
            } else {
                dispatchByActivityExecuteType(execute_type, status, limit, deadline, notifyUrl);
            }

        } catch (Exception e) {
            flag = false;
            log.error("activity jobDispatch process failed.", e);
        } finally {
            if (count > 0) {
                Integer dispatchStatus = flag ? ActivityConstant.BatchSatus.SUCCEEDED.getCode() : ActivityConstant.BatchSatus.FAILED.getCode();
                activityDispatchLogService.updateStatus(tran_date, activityId, dispatchStatus);
            }
        }


        log.info("ActivityBatchServiceImpl jobDispatch end~~~~~~~~~~~~~~");
    }

    private void dispatchByActivityExecuteType(Integer execute_type, Integer status, Integer limit, Date deadline, String notifyUrl) {
        //根据活动类型分发
        long totalNum = activityRecordsAccessorBuilder.countByActivityType(execute_type, deadline, status);
        int totalPage = totalNum % limit == 0 ? (int) (totalNum / limit) : (int) (totalNum / limit + 1);
        log.info("ActivityBatchServiceImpl jobDispatch by execute_type [{}] status [{}] totalNum [{}]  limit[{}] totalPage[{}]", execute_type, status, totalNum, limit, totalPage);

        //下游系统需要做幂等处理，此处分片时间间隔太短，可能查到已分发信息
        Date startTime = new Date(0);

        for (int page = 1; page <= totalPage; page++) {
            List<ActivityRecords> businessIDList = activityRecordsAccessorBuilder.findForDispatchByType(execute_type, startTime, deadline, status, limit);
            log.info("ActivityBatchServiceImpl jobDispatch by execute_type [{}] status [{}] Limit [{}] page [{}]", execute_type, status, limit, page);
            //获取当前片最大时间
            startTime = getMaxTime(businessIDList);

            if (ACTIVITY_SCHEDULE_SINGLE) {
                localProcess(businessIDList);
            } else {
                sendToNode(notifyUrl, businessIDList);
            }
        }

    }


    private void dispatchByActivityIdAndType(Integer activity_id, Integer execute_type, Integer status, Integer limit, Date deadline, String notifyUrl) throws InterruptedException {
        long totalNum = activityRecordsAccessorBuilder.countByActivityIdAndType(activity_id, execute_type, deadline, status);
        int totalPage = totalNum % limit == 0 ? (int) (totalNum / limit) : (int) (totalNum / limit + 1);
        log.info("ActivityBatchServiceImpl jobDispatch by activity_id [{}] execute_type [{}] status [{}] totalNum [{}]  limit[{}] totalPage[{}]", activity_id, execute_type, status, totalNum, limit, totalPage);
        //下游系统需要做幂等处理，此处分片时间间隔太短，可能查到已分发信息
        Date startTime = new Date(0);

        for (int page = 1; page <= totalPage; page++) {
            List<ActivityRecords> businessIDList = activityRecordsAccessorBuilder.findForDispatchById(activity_id, execute_type, startTime, deadline, status, limit);
            log.info("ActivityBatchServiceImpl jobDispatch : activity_id [{}] execute_type [{}] status [{}] Limit [{}] page [{}]", activity_id, execute_type, status, limit, page);

            //获取当前片最大时间
            startTime = getMaxTime(businessIDList);
            if (ACTIVITY_SCHEDULE_SINGLE) {
                localProcess(businessIDList);
            } else {
                sendToNode(notifyUrl, businessIDList);
            }

        }


    }


    private Date getMaxTime(List<ActivityRecords> businessIDList) {
        Date maxdate = new Date(0);
        for (ActivityRecords activityRecords : businessIDList) {
            maxdate = maxdate.getTime() < activityRecords.getCreate_time().getTime() ? activityRecords.getCreate_time() : maxdate;
        }
        return maxdate;
    }

    private void sendToNode(String notifyUrl, List<ActivityRecords> businessIDList) {
        int pageNo = 1;
        int totalPage = businessIDList.size() % PAGE_NUM == 0 ? (int) (businessIDList.size() / PAGE_NUM) : (int) (businessIDList.size() / PAGE_NUM + 1);
        StringBuffer pageList = new StringBuffer();

        for (int i = 0; i < businessIDList.size(); i++) {
            try {
                if (i == pageNo * PAGE_NUM - 1) {
                    pageList.append(businessIDList.get(i).getBusiness_id() + ":" + businessIDList.get(i).getActivity_id() + ",");
                    log.info("ActivityBatchServiceImpl 1 pageSend : pageNo[{}] pageList length [{}]", pageNo, pageList.length());
                    //send node
                    Map<String, String> params = new HashMap<>();
                    params.put("pageList", pageList.toString());

                    for (int j = 0; j < 3; j++) {
                        /*if (HttpUtils.sendHttpGet(params, notifyUrl)) {
                            break;
                        }*/
                    }

                    pageList = new StringBuffer();
                    pageNo++;
                } else {
                    //last page
                    if (i == businessIDList.size() - 1) {
                        pageList.append(businessIDList.get(i).getBusiness_id() + ":" + businessIDList.get(i).getActivity_id());
                        //send node
                        log.info("ActivityBatchServiceImpl 2 pageSend :  pageNo[{}]   pageList length [{}]", pageNo, pageList.length());
                        Map<String, String> params = new HashMap<>();
                        params.put("pageList", pageList.toString());

                        for (int j = 0; j < 3; j++) {
                            /*if (HttpUtils.sendHttpGet(params, notifyUrl)) {
                                break;
                            }*/
                        }

                    } else {
                        pageList.append(businessIDList.get(i).getBusiness_id() + ":" + businessIDList.get(i).getActivity_id() + ",");
                    }
                }
            } catch (Exception e) {
                log.error("ActivityBatchServiceImpl pageSend process fail,error msg is {}", e);
            }
        }


    }

    private void localProcess(List<ActivityRecords> businessIDList) {
        Map<Integer, Activity> activityMap = new ConcurrentHashMap<Integer, Activity>();

        for (int i = 0; i < businessIDList.size(); i++) {
            Activity activity = null;
            if (activityMap.containsKey(businessIDList.get(i).getActivity_id())) {
                activity = activityMap.get(businessIDList.get(i).getActivity_id());
            } else {
                JsonResult result = activityService.findDetailById(businessIDList.get(i).getActivity_id());
                if (result.getSuccess() && result.getObj() != null) {
                    activity = (Activity) result.getObj();
                    activityMap.put(businessIDList.get(i).getActivity_id(), activity);
                }
            }
            if (activity != null) {
                try {
                    batchExecute(businessIDList.get(i).getBusiness_id(), activity);
                } catch (Exception e) {
                    log.error("ActivityBatchServiceImpl batchExecute fail businessId [{}] activityID [{}],error msg is {}", businessIDList.get(i).getBusiness_id(), businessIDList.get(i).getActivity_id(), e);

                }
            }
        }

    }


    @Override
    public void manualReward(Integer activity_id, Integer execute_type, Integer status, Date deadline, Integer limit, String notifyUrl) {
        log.info("ActivityBatchServiceImpl manual reword start~~~~~~~~~~~~");
        jobDispatch(activity_id, execute_type, status, limit, deadline, notifyUrl);
        log.info("ActivityBatchServiceImpl manual reword end~~~~~~~~~~~~~~");
    }

    @Override
    public void timedReward(String buisnessIdList) {

        log.info("ActivityBahStcerviceImpl timedReward start buisnessIdList [{}]~~~~~~~~~~~~~~~", buisnessIdList);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<Integer, Activity> activityMap = new ConcurrentHashMap<Integer, Activity>();
        String[] businessIdArray = buisnessIdList.split(",");

        for (String idstr : businessIdArray) {
            try {
                String[] idarray = idstr.split(":");
                if (idarray.length == 2) {
                    String business_id = idarray[0];
                    Integer activity_id = Integer.parseInt(idarray[1]);

                    Activity activity = null;
                    if (activityMap.containsKey(activity_id)) {
                        activity = activityMap.get(activity_id);
                    } else {
                        JsonResult result = activityService.findDetailById(activity_id);
                        if (result.getSuccess() && result.getObj() != null) {
                            activity = (Activity) result.getObj();
                            activityMap.put(activity_id, activity);
                        }
                    }
                    batchExecute(business_id, activity);
                }

            } catch (Exception e) {
                log.error("ActivityBatchServiceImpl do actions fail businessId [{}],error msg is {}", idstr, e);
            }

        }
        stopWatch.stop();
        log.info("ActivityBahStcerviceImpl timedReward end and total costs [{}] ms~~~~~~~~~~~~~~~", stopWatch.getTotalTimeMillis());

    }


    private void batchExecute(String business_id, Activity activity) throws InterruptedException {
        if (StringUtils.isNotBlank(business_id) && activity != null) {
            activityBatchProcessExecutor.submit(business_id, activity);
        }
    }


    @Override
    public void customerFeeExtract(SchedLog schedLog, String tran_date, int limit, ActivityConstant.RuleEvent event) {
        int offset = 0;
        Integer activity_id = null;
        String batch_id = schedLog.getJob_nm() + "_" + TimeUtil.getDataStr(schedLog.getBatch_pt(), new SimpleDateFormat("yyyyMMdd"));
        log.info("customerFeeExtract batch_id [{}], tran_date [{}],limit [{}], event [{}] ", batch_id, tran_date, limit, event);
        while (true) {
            try {
                List<ActivityCustomerFee> activityCustomerFeeList = activityCustomerFeeService.findAll(tran_date, offset, limit, event.code());

                if (activityCustomerFeeList != null) {
                    if (activityCustomerFeeList.size() > 0) {
                        activity_id = activityCustomerFeeList.get(0).getActivity_id();
                        ActivityBatchInfo batchInfo = activityBatchInfoService.findByPrimaryKey(tran_date, activity_id, batch_id);
                        if (batchInfo != null && batchInfo.getStatus() == ActivityConstant.BatchSatus.SUCCEEDED.getCode()) {
                            log.info("customerFeeExtract batch_id [{}] completed,so skip!", batch_id);
                            break;
                        }
                        activityCustomerFeeList.forEach(activityCustomerFee -> {
                            if (activityCustomerFee.getStatus() == ActivityConstant.BatchSatus.RECORDED.getCode()) {
                                ActivityMessage activityMessage = new ActivityMessage();
                                activityMessage.setBusinessId(activityCustomerFee.getCurrency() + event.code() + activityCustomerFee.getTran_date() + activityCustomerFee.getCustomer_id());
                                activityMessage.setEvent(event.name());
                                activityMessage.setCustomerId(activityCustomerFee.getCustomer_id());
                                activityMessage.setSaasId(SaasId.KIKI.id());
                                activityMessage.setParams(JSONObject.toJSONString(activityCustomerFee));
                                activityMessage.setActivityId(activityCustomerFee.getActivity_id());
                                activityMessage.setBatch(true);

                                onsProducer.asyncSend(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage), new SendCallback() {
                                    @Override
                                    public void onSuccess(SendResult sendResult) {
                                        activityCustomerFeeService.updateStatus(activityCustomerFee.getTran_date(), activityCustomerFee.getCustomer_id(), activityCustomerFee.getCurrency(), ActivityConstant.BatchSatus.SUCCEEDED.getCode(), event.code());
                                    }

                                    @Override
                                    public void onException(OnExceptionContext onExceptionContext) {
                                        onsProducer.send(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage));
                                        activityCustomerFeeService.updateStatus(activityCustomerFee.getTran_date(), activityCustomerFee.getCustomer_id(), activityCustomerFee.getCurrency(), ActivityConstant.BatchSatus.SUCCEEDED.getCode(), event.code());
                                    }

                                });

                            }
                        });

                        if (activityCustomerFeeList.size() < limit) {
                            activityBatchOperate(batch_id, tran_date, activity_id, event);
                            break;
                        }
                    } else {
                        if (activity_id != null) {
                            activityBatchOperate(batch_id, tran_date, activity_id, event);
                            break;
                        } else {
                            log.warn("customerFeeExtract no data found ! ......");
                            break;
                        }
                    }

                    offset += limit;
                } else {
                    log.warn("customerFeeExtract no data found ! ......");
                    break;
                }

            } catch (Exception e) {
                log.error("customerFeeExtract process fail ......", e);
                break;
            }

        }

    }

    private void activityBatchOperate(String batch_id, String tran_date, Integer activity_id, ActivityConstant.RuleEvent event) throws Exception {
        ActivityBatchInfo batchInfo = activityBatchInfoService.findByPrimaryKey(tran_date, activity_id, batch_id);
        if (batchInfo == null) {
            ActivityBatchInfo activityBatchInfo = new ActivityBatchInfo();
            activityBatchInfo.setActivity_id(activity_id);
            activityBatchInfo.setBatch_id(batch_id);
            activityBatchInfo.setTran_date(tran_date);
            activityBatchInfo.setStatus(ActivityConstant.BatchSatus.RECORDED.getCode());
            activityBatchInfoService.save(activityBatchInfo);
        }
        long count = 0;
        //update
        if (event == ActivityConstant.RuleEvent.REBATE || event == ActivityConstant.RuleEvent.RETURN_FEE) {
            count = activityCustomerFeeService.countByTypeAndTye(tran_date, event.code(), ActivityConstant.BatchSatus.RECORDED.getCode());
        } else if (event == ActivityConstant.RuleEvent.INTEREST_DISCOUNT) {
            count = activityCustomerDiscountService.countByStatus(tran_date, ActivityConstant.BatchSatus.RECORDED.getCode());
        }
        if (count == 0) {
            batchInfo = activityBatchInfoService.findByPrimaryKey(tran_date, activity_id, batch_id);
            if (batchInfo != null && batchInfo.getStatus() != ActivityConstant.BatchSatus.SUCCEEDED.getCode()) {
                activityBatchInfoService.updateStatus(batchInfo.getTran_date(), batchInfo.getActivity_id(), batchInfo.getBatch_id(), ActivityConstant.BatchSatus.SUCCEEDED.getCode());
            }
        }
    }


    @Override
    public void customerDiscountExtract(SchedLog schedLog, String discountDate, int limit, ActivityConstant.RuleEvent event) {
        log.info("customerDiscountExtract begin ................................................................................................");
        int offset = 0;
        String batchId = schedLog.getJob_nm() + "_" + TimeUtil.getDataStr(schedLog.getBatch_pt(), new SimpleDateFormat("yyyyMMdd"));
        log.info("customerDiscountExtract batch_id [{}], discountDate [{}], limit [{}], event [{}] ", batchId, discountDate, limit, event);
        Integer activityId = null;
        while (true) {
            try {
                List<ActivityCustomerDiscount> activityCustomerDiscounts = activityCustomerDiscountService.findAll(discountDate, offset, limit);
                if (activityCustomerDiscounts != null) {
                    if (activityCustomerDiscounts.size() > 0) {
                        activityId = activityCustomerDiscounts.get(0).getActivityId();
                        ActivityBatchInfo batchInfo = activityBatchInfoService.findByPrimaryKey(discountDate, activityId, batchId);
                        if (batchInfo != null && batchInfo.getStatus() == ActivityConstant.BatchSatus.SUCCEEDED.getCode()) {
                            log.info("customerDiscountExtract batch_id [{}] completed,so skip!", batchId);
                            break;
                        }

                        activityCustomerDiscounts.forEach(discount -> {
                            if (discount.getStatus() == ActivityConstant.BatchSatus.RECORDED.getCode()) {

                                ActivityMessage activityMessage = new ActivityMessage();
                                activityMessage.setBusinessId(discount.getDiscountCurrency() + discount.getDiscountDate() + discount.getInvestmentProductId() + discount.getCustomerId());
                                activityMessage.setEvent(event.name());
                                activityMessage.setCustomerId(discount.getCustomerId());
                                activityMessage.setSaasId(SaasId.KIKI.id());
                                activityMessage.setParams(JSONObject.toJSONString(discount));
                                activityMessage.setActivityId(discount.getActivityId());
                                activityMessage.setBatch(true);
                                onsProducer.asyncSend(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage), new SendCallback() {
                                    @Override
                                    public void onSuccess(SendResult sendResult) {
                                        log.info("customerDiscountExtract send Mq success, activityMessage {}", JSONObject.toJSONString(activityMessage));
                                        activityCustomerDiscountService.updateStatus(discount.getDiscountDate(), discount.getCustomerId(), discount.getInvestmentProductId(), ActivityConstant.BatchSatus.SUCCEEDED.getCode());
                                    }

                                    @Override
                                    public void onException(OnExceptionContext onExceptionContext) {
                                        log.info("customerDiscountExtract send Mq fail, activityMessage {}", JSONObject.toJSONString(activityMessage));
                                        onsProducer.send(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage));
                                        log.info("customerDiscountExtract resend Mq success, activityMessage {}", JSONObject.toJSONString(activityMessage));
                                        activityCustomerDiscountService.updateStatus(discount.getDiscountDate(), discount.getCustomerId(), discount.getInvestmentProductId(), ActivityConstant.BatchSatus.SUCCEEDED.getCode());
                                    }

                                });
                            }
                        });

                        if (activityCustomerDiscounts.size() < limit) {
                            activityBatchOperate(batchId, discountDate, activityId, event);
                            break;
                        }

                    } else {
                        if (activityId != null) {
                            activityBatchOperate(batchId, discountDate, activityId, event);
                            break;
                        } else {
                            log.warn("customerDiscountExtract no data found ! ......");
                            break;
                        }
                    }
                    offset += limit;
                } else {
                    log.warn("customerDiscountExtract no data found ! ......");
                    break;
                }
            } catch (Exception e) {
                log.error("customerDiscountExtract process fail ......", e);
                break;
            }

        }
        log.info("customerDiscountExtract end ................................................................................................");

    }


    @Override
    public void customerInterestExtract(SchedLog schedLog, String transDate, int limit, ActivityConstant.RuleEvent event) {
        log.info("customerInterestExtract begin ................................................................................................");
        int offset = 0;
        String batchId = schedLog.getJob_nm() + "_" + TimeUtil.getDataStr(schedLog.getBatch_pt(), new SimpleDateFormat("yyyyMMdd"));
        log.info("customerInterestExtract batch_id [{}], transDate [{}],limit [{}], event [{}] ", batchId, transDate, limit, event);
        Integer activityId = null;
        while (true) {
            try {
                List<ActivityCustomerCurrentInterest> activityCustomerCurrentInterests = activityCustomerInterestService.findAll(transDate, offset, limit);
                if (activityCustomerCurrentInterests != null) {
                    if (activityCustomerCurrentInterests.size() > 0) {
                        activityId = activityCustomerCurrentInterests.get(0).getActivityId();
                        ActivityBatchInfo batchInfo = activityBatchInfoService.findByPrimaryKey(transDate, activityId, batchId);
                        if (batchInfo != null && batchInfo.getStatus() == ActivityConstant.BatchSatus.SUCCEEDED.getCode()) {
                            log.info("customerInterestExtract batch_id [{}] completed,so skip!", batchId);
                            break;
                        }

                        activityCustomerCurrentInterests.forEach(interest -> {
                            if (interest.getStatus() == ActivityConstant.BatchSatus.RECORDED.getCode()) {

                                ActivityMessage activityMessage = new ActivityMessage();
                                activityMessage.setBusinessId(interest.getCurrency() + interest.getTransDate() + interest.getCustomerId());
                                activityMessage.setEvent(event.name());
                                activityMessage.setCustomerId(interest.getCustomerId());
                                activityMessage.setSaasId(SaasId.KIKI.id());
                                activityMessage.setParams(JSONObject.toJSONString(interest));
                                activityMessage.setActivityId(interest.getActivityId());
                                activityMessage.setBatch(true);
                                onsProducer.asyncSend(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage), new SendCallback() {

                                    @Override
                                    public void onSuccess(SendResult sendResult) {
                                        log.info("customerInterestExtract send Mq success, activityMessage {}", JSONObject.toJSONString(activityMessage));
                                        activityCustomerDiscountService.updateStatus(interest.getTransDate(), interest.getCustomerId(), interest.getCurrency(), ActivityConstant.BatchSatus.SUCCEEDED.getCode());
                                    }

                                    @Override
                                    public void onException(OnExceptionContext onExceptionContext) {
                                        log.info("customerInterestExtract send Mq fail, activityMessage {}", JSONObject.toJSONString(activityMessage));
                                        onsProducer.send(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage));
                                        log.info("customerInterestExtract resend Mq success, activityMessage {}", JSONObject.toJSONString(activityMessage));
                                        activityCustomerDiscountService.updateStatus(interest.getTransDate(), interest.getCustomerId(), interest.getCurrency(), ActivityConstant.BatchSatus.SUCCEEDED.getCode());
                                    }

                                });
                            }
                        });

                        if (activityCustomerCurrentInterests.size() < limit) {
                            activityBatchOperate(batchId, transDate, activityId, event);
                            break;
                        }

                    } else {
                        if (activityId != null) {
                            activityBatchOperate(batchId, transDate, activityId, event);
                            break;
                        } else {
                            log.warn("customerInterestExtract no data found ! ......");
                            break;
                        }
                    }
                    offset += limit;
                } else {
                    log.warn("customerInterestExtract no data found ! ......");
                    break;
                }
            } catch (Exception e) {
                log.error("customerInterestExtract process fail ......", e);
                break;
            }

        }
        log.info("customerInterestExtract end ................................................................................................");

    }

    @Override
    public void dataPrepare(ActivityEventMassage activityEventMassage) {

        log.info("dataPrepare begin...{}", JSONObject.toJSONString(activityEventMassage));
        try {
            JsonResult result = activityService.findDetailById(activityEventMassage.getId());
            if (!result.getSuccess() || result.getObj() == null) {
                return;
            }

            Activity activity = (Activity) result.getObj();
            JSONObject object = JSONObject.parseObject(activity.getRuleConfig().get(0).getParams());
            List<FiatDepositActivityConfig> configList = object.getObject("configList", new TypeReference<List<FiatDepositActivityConfig>>() {
            });
            List<String> currencyList = configList.stream().map(f -> f.getFiatCurrency()).distinct().collect(Collectors.toList());

            for (String currency : currencyList) {
                PrimaryKey nextToken = null;
                boolean hasNext = true;
                Integer limit = 50;
                while (hasNext) {
                    RangeResult<ActivityUserTotalData> records = activityUserTotalDataBuilder.list(activityEventMassage.getBatchNo(), activityEventMassage.getId(), currency, ActivityConstant.RecordStatus.RECORDED, nextToken, limit);
                    if (!CollectionUtils.isEmpty(records.list)) {
                        batchSend(records.list, activityEventMassage.getId());
                        nextToken = records.nextToken;
                        hasNext = records.list.size() == limit && nextToken != null;
                    } else {
                        hasNext = false;
                    }
                }
            }
        } catch (Exception e) {
            log.error("dataPrepare process fail.", e);
        }
        log.info("dataPrepare end ...");


    }


    public void batchSend(List<ActivityUserTotalData> activityUserTotalDataList, Integer activityId) {
        List<ActivityUserTotalData> success = new ArrayList<>();
        for (ActivityUserTotalData userTotalData : activityUserTotalDataList) {

            try {
                ActivityMessage activityMessage = new ActivityMessage();
                activityMessage.setBusinessId(userTotalData.getCustomerId() + userTotalData.getCurrency() + activityId + +ActivityConstant.RuleEvent.KIKI_FIAT_DEPOSIT_FINISH.code());
                activityMessage.setEvent(ActivityConstant.RuleEvent.KIKI_FIAT_DEPOSIT_FINISH.name());
                activityMessage.setCustomerId(userTotalData.getCustomerId());
                activityMessage.setSaasId(SaasId.KIKI.id());
                activityMessage.setParams(JSONObject.toJSONString(userTotalData));
                activityMessage.setActivityId(activityId);
                activityMessage.setBatch(true);
                userTotalData.setOldStatus(userTotalData.getStatus());
                userTotalData.setStatus(ActivityConstant.BatchSatus.SUCCEEDED.getCode());
                userTotalData.setModified(new Date());
                onsProducer.send(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage));
                success.add(userTotalData);
            } catch (Exception e) {
                log.error("userTotalData send to mq process fail,userTotalData...{}", JSONObject.toJSONString(userTotalData), e);
            }
        }
        if (!CollectionUtils.isEmpty(success)) {
            activityUserTotalDataBuilder.batchUpdate(success);
        }
    }

}
