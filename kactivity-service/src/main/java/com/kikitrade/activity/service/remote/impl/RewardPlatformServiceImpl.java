package com.kikitrade.activity.service.remote.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.kikitrade.activity.api.RewardPlatformService;
import com.kikitrade.activity.api.model.request.reward.*;
import com.kikitrade.activity.api.model.response.reward.*;
import com.kikitrade.activity.dal.tablestore.builder.PrizePoolBuilder;
import com.kikitrade.activity.dal.tablestore.builder.PrizeConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.UserLotteryProfileBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizePool;
import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.activity.service.reward.ProbabilityAlgorithmService;
import com.kikitrade.activity.service.reward.DynamicPrizePoolBuilder;
import com.kikitrade.activity.service.reward.preference.UserPreferenceService;
import com.kikitrade.activity.service.reward.LotteryTicketService;
import com.kikitrade.activity.service.reward.PackageService;
import com.kikitrade.activity.service.reward.ProgressTrackingService;
import com.kikitrade.activity.service.reward.UnifiedClaimService;
import com.kikitrade.activity.dal.tablestore.model.UserPackageEntitlement;
import com.kikitrade.activity.dal.tablestore.model.UserProgress;
import com.kikitrade.activity.dal.tablestore.model.ProgressChest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 奖励中台服务实现
 * 提供新的奖励中台功能，与现有RemoteLotteryService并行运行
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class RewardPlatformServiceImpl implements RewardPlatformService {

    @Resource
    private PrizePoolBuilder prizePoolBuilder;

    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;

    @Resource
    private UserLotteryProfileBuilder userLotteryProfileBuilder;

    @Resource
    private ProbabilityAlgorithmService probabilityAlgorithmService;

    @Resource
    private DynamicPrizePoolBuilder dynamicPrizePoolBuilder;

    @Resource
    private UserPreferenceService userPreferenceService;

    @Resource
    private LotteryTicketService lotteryTicketService;

    @Resource
    private PackageService packageService;

    @Resource
    private ProgressTrackingService progressTrackingService;

    @Resource
    private UnifiedClaimService unifiedClaimService;

    @Override
    public ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request) {
        log.info("用户兑换抽奖券（两阶段抽奖系统）: userId={}, assetType={}, assetAmount={}, ticketType={}",
                request.getUserId(), request.getAssetType(), request.getAssetAmount(), request.getTicketType());

        // 委托给专门的抽奖券服务处理
        return lotteryTicketService.exchangeTickets(request);
    }

    @Override
    public DrawBatchResponse drawBatch(DrawBatchRequest request) {
        log.info("用户批量抽奖: userId={}, prizePoolCode={}, drawCount={}",
                request.getUserId(), request.getPrizePoolCode(), request.getDrawCount());

        try {
            // 1. 验证奖池配置
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(
                    request.getPrizePoolCode(), request.getSaasId());
            if (prizePool == null) {
                return DrawBatchResponse.builder()
                        .success(false)
                        .errorCode("POOL_NOT_FOUND")
                        .message("奖池不存在")
                        .build();
            }

            // 2. 构建动态奖池（使用新的动态奖池构建器）
            List<PrizeConfig> dynamicPool = dynamicPrizePoolBuilder.buildDynamicPool(
                    request.getUserId(), request.getPrizePoolCode(), request.getSaasId());

            if (dynamicPool.isEmpty()) {
                return DrawBatchResponse.builder()
                        .success(false)
                        .errorCode("NO_PRIZES_AVAILABLE")
                        .message("暂无可用奖品")
                        .build();
            }

            // 3. 获取兜底奖品（SINGLE策略需要）
            PrizeConfig fallbackPrize = null;
            if ("SINGLE".equals(prizePool.getProbabilityStrategy()) && prizePool.getFallbackPrizeId() != null) {
                fallbackPrize = prizeConfigBuilder.findById(prizePool.getFallbackPrizeId().toString());
            }

            // 4. 执行批量抽奖
            List<PrizeConfig> drawResults = probabilityAlgorithmService.batchDraw(
                    dynamicPool, prizePool.getProbabilityStrategy(), fallbackPrize, request.getDrawCount());

            log.info("用户批量抽奖成功: userId={}, 中奖数量={}", request.getUserId(), drawResults.size());

            return DrawBatchResponse.builder()
                    .success(true)
                    .message("抽奖成功")
                    .build();

        } catch (Exception e) {
            log.error("用户批量抽奖失败", e);
            return DrawBatchResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    // ==================== 用户偏好管理API ====================

    @Override
    public SetUserPreferenceResponse setUserPreference(SetUserPreferenceRequest request) {
        log.info("设置用户偏好: userId={}, saasId={}, type={}, value={}",
                request.getUserId(), request.getSaasId(), request.getPreferenceType(), request.getPreferenceValue());

        try {
            // 获取设置前的旧值
            String previousValue = userPreferenceService.getUserPreference(
                    request.getUserId(), request.getSaasId(), request.getPreferenceType());

            // 设置新的偏好值
            boolean success = userPreferenceService.setUserPreference(
                    request.getUserId(), request.getSaasId(),
                    request.getPreferenceType(), request.getPreferenceValue());

            return SetUserPreferenceResponse.builder()
                    .success(success)
                    .preferenceType(request.getPreferenceType())
                    .preferenceValue(request.getPreferenceValue())
                    .previousValue(previousValue)
                    .message(success ? "偏好设置成功" : "偏好设置失败")
                    .build();

        } catch (Exception e) {
            log.error("设置用户偏好失败", e);
            return SetUserPreferenceResponse.builder()
                    .success(false)
                    .preferenceType(request.getPreferenceType())
                    .preferenceValue(request.getPreferenceValue())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public GetUserPreferenceResponse getUserPreference(GetUserPreferenceRequest request) {
        log.info("获取用户偏好: userId={}, saasId={}, type={}",
                request.getUserId(), request.getSaasId(), request.getPreferenceType());

        try {
            String preferenceValue = userPreferenceService.getUserPreference(
                    request.getUserId(), request.getSaasId(), request.getPreferenceType());

            return GetUserPreferenceResponse.builder()
                    .success(true)
                    .preferenceType(request.getPreferenceType())
                    .preferenceValue(preferenceValue)
                    .message("查询成功")
                    .build();

        } catch (Exception e) {
            log.error("获取用户偏好失败", e);
            return GetUserPreferenceResponse.builder()
                    .success(false)
                    .preferenceType(request.getPreferenceType())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    @Deprecated
    public SelectHeroResponse selectHero(SelectHeroRequest request) {
        log.info("用户选择英雄（兼容接口）: userId={}, heroId={}", request.getUserId(), request.getHeroId());

        // 委托给新的通用偏好设置接口
        SetUserPreferenceRequest preferenceRequest = new SetUserPreferenceRequest();
        preferenceRequest.setUserId(request.getUserId());
        preferenceRequest.setSaasId(request.getSaasId());
        preferenceRequest.setPreferenceType("SELECTED_HERO");
        preferenceRequest.setPreferenceValue(request.getHeroId());

        SetUserPreferenceResponse preferenceResponse = setUserPreference(preferenceRequest);

        return SelectHeroResponse.builder()
                .success(preferenceResponse.getSuccess())
                .message(preferenceResponse.getMessage())
                .build();
    }

    // ==================== 抽奖状态查询API ====================

    @Override
    public LotteryStateResponse getLotteryState(LotteryStateRequest request) {
        log.info("获取用户抽奖状态: userId={}, prizePoolCode={}",
                request.getUserId(), request.getPrizePoolCode());

        try {
            // 1. 获取用户基本信息
            LotteryStateResponse.UserBasicInfo userInfo = buildUserBasicInfo(request.getUserId(), request.getSaasId());

            // 2. 获取用户偏好设置
            LotteryStateResponse.UserPreferences preferences = buildUserPreferences(request.getUserId(), request.getSaasId());

            // 3. 获取抽奖券信息
            LotteryStateResponse.TicketInfo ticketInfo = buildTicketInfo(request.getUserId(), request.getSaasId());

            // 4. 获取奖池状态信息
            List<LotteryStateResponse.PoolStateInfo> poolStates = buildPoolStates(
                    request.getUserId(), request.getSaasId(), request.getPrizePoolCode(),
                    request.getIncludePoolDetails());

            // 5. 获取用户进度信息（如果需要）
            List<LotteryStateResponse.UserProgressInfo> progressInfo = new ArrayList<>();
            if (Boolean.TRUE.equals(request.getIncludeProgressInfo())) {
                progressInfo = buildUserProgressInfo(request.getUserId(), request.getSaasId());
            }

            // 6. 获取可领取奖励信息（如果需要）
            List<LotteryStateResponse.ClaimableRewardInfo> claimableRewards = new ArrayList<>();
            if (Boolean.TRUE.equals(request.getIncludeClaimableRewards())) {
                claimableRewards = buildClaimableRewardInfo(request.getUserId(), request.getSaasId());
            }

            log.info("用户抽奖状态查询成功: userId={}, poolCount={}, progressCount={}, rewardCount={}",
                    request.getUserId(), poolStates.size(), progressInfo.size(), claimableRewards.size());

            return LotteryStateResponse.builder()
                    .success(true)
                    .userInfo(userInfo)
                    .preferences(preferences)
                    .ticketInfo(ticketInfo)
                    .poolStates(poolStates)
                    .progressInfo(progressInfo)
                    .claimableRewards(claimableRewards)
                    .message("查询成功")
                    .build();

        } catch (Exception e) {
            log.error("获取用户抽奖状态异常", e);
            return LotteryStateResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public ClaimChestResponse claimChest(ClaimChestRequest request) {
        // TODO: 实现宝箱领取
        return null;
    }

    @Override
    public ClaimStatusResponse getClaimStatus(ClaimStatusRequest request) {
        // TODO: 实现领取状态查询
        return null;
    }

    @Override
    public AdminPoolResponse createOrUpdatePool(AdminPoolRequest request) {
        // TODO: 实现管理后台奖池管理
        return null;
    }

    @Override
    public AdminPrizeResponse createOrUpdatePrize(AdminPrizeRequest request) {
        // TODO: 实现管理后台奖品管理
        return null;
    }

    @Override
    public AdminStockResponse getRealtimeStock(AdminStockRequest request) {
        // TODO: 实现实时库存查询
        return null;
    }

    @Override
    public AdminCacheResponse refreshCache(AdminCacheRequest request) {
        // TODO: 实现缓存刷新
        return null;
    }

    @Override
    public GrantPackResponse grantPackEntitlement(GrantPackRequest request) {
        // TODO: 实现礼包资格授予
        return null;
    }

    @Override
    public IssuePackResponse issuePack(IssuePackRequest request) {
        // TODO: 实现礼包发放
        return null;
    }

    @Override
    public IssueDirectResponse issueDirect(IssueDirectRequest request) {
        // TODO: 实现直接奖励发放
        return null;
    }

    @Override
    public IssueLeaderboardResponse issueLeaderboard(IssueLeaderboardRequest request) {
        // TODO: 实现排行榜奖励发放
        return null;
    }

    // ==================== 礼包/宝箱系统API ====================

    @Override
    public ClaimPackageResponse claimPackage(ClaimPackageRequest request) {
        log.info("用户领取礼包: userId={}, packageId={}, channel={}",
                request.getUserId(), request.getPackageId(), request.getClaimChannel());

        try {
            PackageService.PackageIssueResult result = packageService.issuePack(
                    request.getUserId(), request.getSaasId(), request.getPackageId(),
                    request.getClaimChannel(), request.getClientIp(), request.getDeviceInfo());

            if (result.isSuccess()) {
                return ClaimPackageResponse.builder()
                        .success(true)
                        .packageId(request.getPackageId())
                        .packageName("礼包") // 可以从配置中获取
                        .receivedPrizes(result.getIssuedPrizes())
                        .entitlementId(result.getEntitlementId())
                        .claimTime(System.currentTimeMillis())
                        .message(result.getMessage())
                        .build();
            } else {
                return ClaimPackageResponse.builder()
                        .success(false)
                        .packageId(request.getPackageId())
                        .errorCode(result.getErrorCode())
                        .message(result.getMessage())
                        .build();
            }

        } catch (Exception e) {
            log.error("领取礼包异常", e);
            return ClaimPackageResponse.builder()
                    .success(false)
                    .packageId(request.getPackageId())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public GetUserPackagesResponse getUserPackages(GetUserPackagesRequest request) {
        log.info("查询用户礼包: userId={}, status={}, packageType={}",
                request.getUserId(), request.getStatus(), request.getPackageType());

        try {
            List<UserPackageEntitlement> entitlements = packageService.getUserPackageEntitlements(
                    request.getUserId(), request.getSaasId(), request.getStatus());

            // 转换为API响应格式
            List<GetUserPackagesResponse.UserPackageInfo> packageInfos = entitlements.stream()
                    .filter(e -> request.getPackageType() == null || request.getPackageType().equals(e.getPackageType()))
                    .map(this::convertToPackageInfo)
                    .collect(Collectors.toList());

            return GetUserPackagesResponse.builder()
                    .success(true)
                    .packages(packageInfos)
                    .totalCount(packageInfos.size())
                    .message("查询成功")
                    .build();

        } catch (Exception e) {
            log.error("查询用户礼包异常", e);
            return GetUserPackagesResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    /**
     * 转换权益为包信息
     */
    private GetUserPackagesResponse.UserPackageInfo convertToPackageInfo(UserPackageEntitlement entitlement) {
        return GetUserPackagesResponse.UserPackageInfo.builder()
                .packageId(entitlement.getPackageId())
                .packageName(entitlement.getPackageName())
                .packageType(entitlement.getPackageType())
                .status(entitlement.getStatus())
                .grantTime(entitlement.getGrantTime())
                .claimTime(entitlement.getClaimTime())
                .expireTime(entitlement.getExpireTime())
                .canClaim(packageService.canClaimPackage(entitlement.getUserId(), entitlement.getSaasId(), entitlement.getPackageId()))
                .prizePreview(parsePrizePreview(entitlement.getPrizeConfig()))
                .entitlementId(entitlement.getEntitlementId())
                .build();
    }

    /**
     * 解析奖品预览
     */
    private List<String> parsePrizePreview(String prizeConfig) {
        // 简单实现，实际项目中可以更复杂
        List<String> preview = new ArrayList<>();
        if (prizeConfig != null && prizeConfig.contains("points")) {
            preview.add("积分奖励");
        }
        if (prizeConfig != null && prizeConfig.contains("tickets")) {
            preview.add("抽奖券");
        }
        return preview;
    }

    // ==================== 进度宝箱系统API ====================

    @Override
    public ClaimChestResponse claimChest(ClaimChestRequest request) {
        log.info("用户领取进度宝箱: userId={}, chestId={}, channel={}",
                request.getUserId(), request.getChestId(), request.getClaimChannel());

        try {
            ProgressTrackingService.ChestClaimResult result = progressTrackingService.claimChest(
                    request.getUserId(), request.getSaasId(), request.getChestId(),
                    request.getClaimChannel(), request.getClientIp(), request.getDeviceInfo());

            if (result.isSuccess()) {
                // 获取宝箱状态以获取进度信息
                ProgressTrackingService.ChestClaimStatus status = progressTrackingService.getChestClaimStatus(
                        request.getUserId(), request.getSaasId(), request.getChestId());

                return ClaimChestResponse.builder()
                        .success(true)
                        .chestId(request.getChestId())
                        .chestName(status.getChestName())
                        .receivedPrizes(result.getReceivedPrizes())
                        .claimId(result.getClaimId())
                        .claimTime(result.getClaimTime())
                        .currentProgress(status.getCurrentProgress())
                        .requiredProgress(status.getRequiredProgress())
                        .message(result.getMessage())
                        .build();
            } else {
                return ClaimChestResponse.builder()
                        .success(false)
                        .chestId(request.getChestId())
                        .errorCode(result.getErrorCode())
                        .message(result.getMessage())
                        .build();
            }

        } catch (Exception e) {
            log.error("领取进度宝箱异常", e);
            return ClaimChestResponse.builder()
                    .success(false)
                    .chestId(request.getChestId())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public GetClaimStatusResponse getClaimStatus(GetClaimStatusRequest request) {
        log.info("获取宝箱领取状态: userId={}, chestId={}, chestType={}",
                request.getUserId(), request.getChestId(), request.getChestType());

        try {
            List<GetClaimStatusResponse.ChestStatusInfo> chestStatuses = new ArrayList<>();

            if (StringUtils.hasText(request.getChestId())) {
                // 查询单个宝箱状态
                ProgressTrackingService.ChestClaimStatus status = progressTrackingService.getChestClaimStatus(
                        request.getUserId(), request.getSaasId(), request.getChestId());
                chestStatuses.add(convertToChestStatusInfo(status));
            } else {
                // 查询所有宝箱状态
                List<ProgressChest> activeChests = progressTrackingService.getActiveProgressChests(
                        request.getSaasId(), request.getChestType());

                for (ProgressChest chest : activeChests) {
                    ProgressTrackingService.ChestClaimStatus status = progressTrackingService.getChestClaimStatus(
                            request.getUserId(), request.getSaasId(), chest.getChestId());

                    // 状态过滤
                    if (request.getStatus() == null || request.getStatus().equals(status.getStatus())) {
                        chestStatuses.add(convertToChestStatusInfo(status));
                    }
                }
            }

            // 获取用户进度信息
            List<GetClaimStatusResponse.UserProgressInfo> userProgressInfos = new ArrayList<>();
            List<UserProgress> userProgressList = progressTrackingService.getAllUserProgress(
                    request.getUserId(), request.getSaasId());

            for (UserProgress progress : userProgressList) {
                userProgressInfos.add(convertToUserProgressInfo(progress));
            }

            return GetClaimStatusResponse.builder()
                    .success(true)
                    .chestStatuses(chestStatuses)
                    .userProgress(userProgressInfos)
                    .message("查询成功")
                    .build();

        } catch (Exception e) {
            log.error("获取宝箱领取状态异常", e);
            return GetClaimStatusResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    /**
     * 转换宝箱状态信息
     */
    private GetClaimStatusResponse.ChestStatusInfo convertToChestStatusInfo(ProgressTrackingService.ChestClaimStatus status) {
        return GetClaimStatusResponse.ChestStatusInfo.builder()
                .chestId(status.getChestId())
                .chestName(status.getChestName())
                .chestType("DAILY") // 可以从宝箱配置中获取
                .status(status.getStatus())
                .currentProgress(status.getCurrentProgress())
                .requiredProgress(status.getRequiredProgress())
                .canClaim(status.isCanClaim())
                .lastClaimTime(status.getLastClaimTime())
                .claimCount(status.getClaimCount())
                .maxClaimCount(status.getMaxClaimCount())
                .prizePreview(Arrays.asList("积分奖励", "抽奖券")) // 可以从宝箱配置中解析
                .build();
    }

    /**
     * 转换用户进度信息
     */
    private GetClaimStatusResponse.UserProgressInfo convertToUserProgressInfo(UserProgress progress) {
        return GetClaimStatusResponse.UserProgressInfo.builder()
                .progressType(progress.getProgressType())
                .currentProgress(progress.getCurrentProgress())
                .maxProgress(progress.getMaxProgress())
                .dailyProgress(progress.getDailyProgress())
                .weeklyProgress(progress.getWeeklyProgress())
                .monthlyProgress(progress.getMonthlyProgress())
                .lastUpdateTime(progress.getLastUpdateTime())
                .build();
    }

    // ==================== 统一领奖系统API ====================

    @Override
    public UnifiedClaimResponse claimReward(UnifiedClaimRequest request) {
        log.info("统一领奖: userId={}, claimId={}", request.getUserId(), request.getClaimId());

        try {
            UnifiedClaimService.UnifiedClaimResult result = unifiedClaimService.claimReward(
                    request.getUserId(), request.getSaasId(), request.getClaimId());

            if (result.isSuccess()) {
                // 转换奖励信息
                List<UnifiedClaimResponse.RewardItem> rewardItems = result.getRewards().stream()
                        .map(reward -> UnifiedClaimResponse.RewardItem.builder()
                                .itemId(reward.getItemId())
                                .itemName(reward.getItemName())
                                .itemType(reward.getItemType())
                                .quantity(reward.getQuantity())
                                .description(reward.getDescription())
                                .build())
                        .collect(Collectors.toList());

                return UnifiedClaimResponse.builder()
                        .success(true)
                        .claimId(request.getClaimId())
                        .rewards(rewardItems)
                        .claimTime(System.currentTimeMillis())
                        .message(result.getMessage())
                        .build();
            } else {
                return UnifiedClaimResponse.builder()
                        .success(false)
                        .claimId(request.getClaimId())
                        .errorCode(result.getErrorCode())
                        .message(result.getMessage())
                        .build();
            }

        } catch (Exception e) {
            log.error("统一领奖异常", e);
            return UnifiedClaimResponse.builder()
                    .success(false)
                    .claimId(request.getClaimId())
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public GetClaimableRewardsResponse getClaimableRewards(GetClaimableRewardsRequest request) {
        log.info("获取可领取奖励: userId={}, rewardType={}", request.getUserId(), request.getRewardType());

        try {
            List<UnifiedClaimService.ClaimableReward> claimableRewards = unifiedClaimService.getClaimableRewards(
                    request.getUserId(), request.getSaasId());

            // 类型过滤
            if (StringUtils.hasText(request.getRewardType())) {
                claimableRewards = claimableRewards.stream()
                        .filter(reward -> request.getRewardType().equals(reward.getRewardType()))
                        .collect(Collectors.toList());
            }

            // 转换为响应格式
            List<GetClaimableRewardsResponse.ClaimableRewardInfo> rewardInfos = claimableRewards.stream()
                    .map(reward -> {
                        boolean expiringSoon = reward.getExpireTime() != null &&
                                (reward.getExpireTime() - System.currentTimeMillis()) < 24 * 60 * 60 * 1000L;

                        return GetClaimableRewardsResponse.ClaimableRewardInfo.builder()
                                .claimId(reward.getClaimId())
                                .rewardType(reward.getRewardType())
                                .rewardName(reward.getRewardName())
                                .rewardIcon(reward.getRewardIcon())
                                .rewardDescription(reward.getRewardDescription())
                                .createTime(reward.getCreateTime())
                                .expireTime(reward.getExpireTime())
                                .expiringSoon(expiringSoon)
                                .build();
                    })
                    .collect(Collectors.toList());

            return GetClaimableRewardsResponse.builder()
                    .success(true)
                    .rewards(rewardInfos)
                    .totalCount(rewardInfos.size())
                    .message("查询成功")
                    .build();

        } catch (Exception e) {
            log.error("获取可领取奖励异常", e);
            return GetClaimableRewardsResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    // ==================== getLotteryState 辅助方法 ====================

    /**
     * 构建用户基本信息
     */
    private LotteryStateResponse.UserBasicInfo buildUserBasicInfo(String userId, String saasId) {
        try {
            // 从用户偏好服务获取用户基本信息
            String userLevelStr = userPreferenceService.getUserPreference(userId, saasId, "USER_LEVEL");
            String vipLevelStr = userPreferenceService.getUserPreference(userId, saasId, "VIP_LEVEL");
            String totalDrawCountStr = userPreferenceService.getUserPreference(userId, saasId, "TOTAL_DRAW_COUNT");
            String totalWinCountStr = userPreferenceService.getUserPreference(userId, saasId, "TOTAL_WIN_COUNT");
            String todayDrawCountStr = userPreferenceService.getUserPreference(userId, saasId, "TODAY_DRAW_COUNT");
            String consecutiveDaysStr = userPreferenceService.getUserPreference(userId, saasId, "CONSECUTIVE_DAYS");

            return LotteryStateResponse.UserBasicInfo.builder()
                    .userId(userId)
                    .userLevel(parseIntegerSafely(userLevelStr, 1))
                    .vipLevel(parseIntegerSafely(vipLevelStr, 0))
                    .totalDrawCount(parseLongSafely(totalDrawCountStr, 0L))
                    .totalWinCount(parseIntegerSafely(totalWinCountStr, 0))
                    .todayDrawCount(parseIntegerSafely(todayDrawCountStr, 0))
                    .consecutiveDays(parseIntegerSafely(consecutiveDaysStr, 0))
                    .build();

        } catch (Exception e) {
            log.error("构建用户基本信息异常: userId={}", userId, e);
            return LotteryStateResponse.UserBasicInfo.builder()
                    .userId(userId)
                    .userLevel(1)
                    .vipLevel(0)
                    .totalDrawCount(0L)
                    .totalWinCount(0)
                    .todayDrawCount(0)
                    .consecutiveDays(0)
                    .build();
        }
    }

    /**
     * 构建用户偏好设置
     */
    private LotteryStateResponse.UserPreferences buildUserPreferences(String userId, String saasId) {
        try {
            String selectedHero = userPreferenceService.getUserPreference(userId, saasId, "SELECTED_HERO");

            return LotteryStateResponse.UserPreferences.builder()
                    .selectedHero(selectedHero)
                    .build();

        } catch (Exception e) {
            log.error("构建用户偏好设置异常: userId={}", userId, e);
            return LotteryStateResponse.UserPreferences.builder()
                    .selectedHero("DEFAULT_HERO")
                    .build();
        }
    }

    /**
     * 构建抽奖券信息
     */
    private LotteryStateResponse.TicketInfo buildTicketInfo(String userId, String saasId) {
        try {
            Integer normalTickets = lotteryTicketService.getUserTicketBalance(userId, saasId, "NORMAL");
            Integer premiumTickets = lotteryTicketService.getUserTicketBalance(userId, saasId, "PREMIUM");
            Integer specialTickets = lotteryTicketService.getUserTicketBalance(userId, saasId, "SPECIAL");

            Integer totalTickets = normalTickets + premiumTickets + specialTickets;

            return LotteryStateResponse.TicketInfo.builder()
                    .normalTickets(normalTickets)
                    .premiumTickets(premiumTickets)
                    .specialTickets(specialTickets)
                    .totalTickets(totalTickets)
                    .build();

        } catch (Exception e) {
            log.error("构建抽奖券信息异常: userId={}", userId, e);
            return LotteryStateResponse.TicketInfo.builder()
                    .normalTickets(0)
                    .premiumTickets(0)
                    .specialTickets(0)
                    .totalTickets(0)
                    .build();
        }
    }

    /**
     * 构建奖池状态信息
     */
    private List<LotteryStateResponse.PoolStateInfo> buildPoolStates(String userId, String saasId,
                                                                   String prizePoolCode, Boolean includeDetails) {
        List<LotteryStateResponse.PoolStateInfo> poolStates = new ArrayList<>();

        try {
            // 如果指定了奖池编码，只查询该奖池
            if (StringUtils.hasText(prizePoolCode)) {
                PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(prizePoolCode, saasId);
                if (prizePool != null) {
                    LotteryStateResponse.PoolStateInfo poolState = buildSinglePoolState(userId, saasId, prizePool, includeDetails);
                    poolStates.add(poolState);
                }
            } else {
                // 查询所有活跃的奖池
                List<PrizePool> activePools = prizePoolBuilder.findActiveBySaasId(saasId);
                for (PrizePool prizePool : activePools) {
                    LotteryStateResponse.PoolStateInfo poolState = buildSinglePoolState(userId, saasId, prizePool, includeDetails);
                    poolStates.add(poolState);
                }
            }

        } catch (Exception e) {
            log.error("构建奖池状态信息异常: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
        }

        return poolStates;
    }

    /**
     * 构建单个奖池状态信息
     */
    private LotteryStateResponse.PoolStateInfo buildSinglePoolState(String userId, String saasId,
                                                                   PrizePool prizePool, Boolean includeDetails) {
        try {
            // 获取用户在该奖池的抽奖统计
            String userDrawCountKey = "POOL_DRAW_COUNT_" + prizePool.getPoolCode();
            String userWinCountKey = "POOL_WIN_COUNT_" + prizePool.getPoolCode();

            Integer userDrawCount = parseIntegerSafely(
                    userPreferenceService.getUserPreference(userId, saasId, userDrawCountKey), 0);
            Integer userWinCount = parseIntegerSafely(
                    userPreferenceService.getUserPreference(userId, saasId, userWinCountKey), 0);

            // 构建奖池状态
            LotteryStateResponse.PoolStateInfo.PoolStateInfoBuilder builder = LotteryStateResponse.PoolStateInfo.builder()
                    .poolCode(prizePool.getPoolCode())
                    .poolName(prizePool.getPoolName())
                    .poolType(prizePool.getPoolType())
                    .isActive("ACTIVE".equals(prizePool.getStatus()))
                    .userDrawCount(userDrawCount)
                    .userWinCount(userWinCount)
                    .requiredTickets(prizePool.getRequiredTickets() != null ? prizePool.getRequiredTickets() : 1);

            // 如果需要详细信息
            if (Boolean.TRUE.equals(includeDetails)) {
                LotteryStateResponse.PoolDetails poolDetails = LotteryStateResponse.PoolDetails.builder()
                        .description(prizePool.getDescription())
                        .prizePreview(parsePrizePreview(prizePool.getPrizeConfig()))
                        .startTime(prizePool.getStartTime())
                        .endTime(prizePool.getEndTime())
                        .maxDrawLimit(prizePool.getMaxDrawLimit())
                        .build();
                builder.poolDetails(poolDetails);
            }

            return builder.build();

        } catch (Exception e) {
            log.error("构建单个奖池状态异常: poolCode={}", prizePool.getPoolCode(), e);
            return LotteryStateResponse.PoolStateInfo.builder()
                    .poolCode(prizePool.getPoolCode())
                    .poolName(prizePool.getPoolName())
                    .isActive(false)
                    .userDrawCount(0)
                    .userWinCount(0)
                    .requiredTickets(1)
                    .build();
        }
    }

    /**
     * 构建用户进度信息
     */
    private List<LotteryStateResponse.UserProgressInfo> buildUserProgressInfo(String userId, String saasId) {
        List<LotteryStateResponse.UserProgressInfo> progressInfos = new ArrayList<>();

        try {
            List<UserProgress> userProgressList = progressTrackingService.getAllUserProgress(userId, saasId);

            for (UserProgress progress : userProgressList) {
                // 查找关联的进度宝箱
                List<ProgressChest> relatedChests = progressTrackingService.getActiveProgressChests(saasId, null)
                        .stream()
                        .filter(chest -> progress.getProgressType().equals(chest.getProgressType()))
                        .collect(Collectors.toList());

                for (ProgressChest chest : relatedChests) {
                    Integer currentProgress = getCurrentProgressByType(progress, chest.getChestType());
                    Integer targetProgress = chest.getRequiredProgress();
                    Double progressPercentage = targetProgress > 0 ?
                            (currentProgress.doubleValue() / targetProgress.doubleValue()) * 100 : 0.0;

                    boolean canClaimChest = progressTrackingService.checkChestUnlockCondition(userId, saasId, chest.getChestId());

                    LotteryStateResponse.UserProgressInfo progressInfo = LotteryStateResponse.UserProgressInfo.builder()
                            .progressType(progress.getProgressType())
                            .currentProgress(currentProgress)
                            .targetProgress(targetProgress)
                            .progressPercentage(Math.min(progressPercentage, 100.0))
                            .associatedChestId(chest.getChestId())
                            .canClaimChest(canClaimChest)
                            .build();

                    progressInfos.add(progressInfo);
                }
            }

        } catch (Exception e) {
            log.error("构建用户进度信息异常: userId={}", userId, e);
        }

        return progressInfos;
    }

    /**
     * 构建可领取奖励信息
     */
    private List<LotteryStateResponse.ClaimableRewardInfo> buildClaimableRewardInfo(String userId, String saasId) {
        List<LotteryStateResponse.ClaimableRewardInfo> claimableRewards = new ArrayList<>();

        try {
            List<UnifiedClaimService.ClaimableReward> rewards = unifiedClaimService.getClaimableRewards(userId, saasId);

            for (UnifiedClaimService.ClaimableReward reward : rewards) {
                boolean expiringSoon = reward.getExpireTime() != null &&
                        (reward.getExpireTime() - System.currentTimeMillis()) < 24 * 60 * 60 * 1000L;

                LotteryStateResponse.ClaimableRewardInfo rewardInfo = LotteryStateResponse.ClaimableRewardInfo.builder()
                        .claimId(reward.getClaimId())
                        .rewardType(reward.getRewardType())
                        .rewardName(reward.getRewardName())
                        .rewardDescription(reward.getRewardDescription())
                        .expireTime(reward.getExpireTime())
                        .expiringSoon(expiringSoon)
                        .build();

                claimableRewards.add(rewardInfo);
            }

        } catch (Exception e) {
            log.error("构建可领取奖励信息异常: userId={}", userId, e);
        }

        return claimableRewards;
    }

    /**
     * 根据宝箱类型获取对应的进度值
     */
    private Integer getCurrentProgressByType(UserProgress progress, String chestType) {
        if (progress == null) return 0;

        switch (chestType) {
            case "DAILY":
                return progress.getDailyProgress() != null ? progress.getDailyProgress() : 0;
            case "WEEKLY":
                return progress.getWeeklyProgress() != null ? progress.getWeeklyProgress() : 0;
            case "MONTHLY":
                return progress.getMonthlyProgress() != null ? progress.getMonthlyProgress() : 0;
            default:
                return progress.getCurrentProgress() != null ? progress.getCurrentProgress() : 0;
        }
    }

    /**
     * 解析奖品预览
     */
    private List<String> parsePrizePreview(String prizeConfig) {
        List<String> preview = new ArrayList<>();

        try {
            if (StringUtils.hasText(prizeConfig)) {
                Map<String, Object> config = objectMapper.readValue(prizeConfig, new TypeReference<Map<String, Object>>() {});

                if (config.containsKey("points")) {
                    preview.add("积分奖励");
                }
                if (config.containsKey("tickets")) {
                    preview.add("抽奖券");
                }
                if (config.containsKey("coins")) {
                    preview.add("金币");
                }
                if (config.containsKey("items")) {
                    preview.add("道具奖励");
                }
            }
        } catch (Exception e) {
            log.error("解析奖品预览异常: prizeConfig={}", prizeConfig, e);
        }

        return preview.isEmpty() ? Arrays.asList("神秘奖励") : preview;
    }

    /**
     * 安全解析整数
     */
    private Integer parseIntegerSafely(String value, Integer defaultValue) {
        try {
            return StringUtils.hasText(value) ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 安全解析长整数
     */
    private Long parseLongSafely(String value, Long defaultValue) {
        try {
            return StringUtils.hasText(value) ? Long.parseLong(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    // 私有辅助方法已移至专门的服务类中
}