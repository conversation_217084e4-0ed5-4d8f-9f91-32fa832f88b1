package com.kikitrade.activity.service.business;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.response.TaskCompletedResult;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.auth.AuthService;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.kikitrade.activity.service.rpc.ThreePlatformApi;
import com.kikitrade.activity.service.rpc.VerifyResult;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.activity.service.rpc.okx.OkxTaskService;
import com.kikitrade.activity.service.rpc.sahara.SaharaTaskService;
import com.kikitrade.activity.service.rpc.tg.QuestsTelegramBot;
import com.kikitrade.activity.service.rpc.tg.TGBotRequest;
import com.kikitrade.activity.service.rpc.twitter.TwitterApiUtil;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.member.api.RemoteMemberCastleService;
import com.kikitrade.member.model.MemberProductItemDTO;
import com.twitter.clientlib.model.Tweet;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.chatmember.ChatMember;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:48
 */
@Component
@Slf4j
public class ThreePlatformFilter {

    @Resource
    private ThreePlatformApi threePlatformApi;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @DubboReference
    private RemoteMemberCastleService remoteMemberCastleService;
    @Resource
    private AuthService authService;
    @Resource
    private QuestsTelegramBot questsTelegramBot;
    @Resource
    @Lazy
    private ActivityTaskService activityTaskService;
    @Resource
    private TwitterApiUtil twitterApiUtil;
    @Resource
    private SaharaTaskService saharaTaskService;
    @Resource
    private OkxTaskService okxTaskService;

    private static final String JWT_TOKEN = "JWT_TOKEN";

    public VerifyResult verify(TaskConfigDTO configDTO, AccessToken token, String customerId, String saasId, Map<String, String> ext) throws ActivityException{
        String code = configDTO.getCode();
        token.setSaasId(saasId);

        return switch (ActivityTaskConstant.TaskCodeEnum.valueOf(code)){
            case connect_x, connect_twitter -> verifyConnectX(token);
            case like_post_x -> verifyLikePostX(token, configDTO);
            case comment_post_x -> verifyCommentPostX(token, configDTO);
            case retweet_post_x -> verifyRetweetPostX(token, configDTO);
            case follow_x -> verifyFollowX(token, configDTO);
            case reply_post_x -> verifyReply(token, configDTO);
            case subject_post_x -> verifySubject(token, configDTO);
            case name_x -> verifyNameX(token, configDTO);
            case connect_dc, connect_discord -> verifyConnectDC(token);
            case join_dc -> verifyJoinDC(token, configDTO);
            case join_game -> verifyJoinGame(token, configDTO);
            case play_game -> verifyPlayGame(token, configDTO);
            case external_reward -> verifyExternalReward(token, configDTO);
            case x_authorize -> verifyXAuthorize(saasId, customerId);
            case osp_profile_create -> verifyOspProfile(token, configDTO);
            case osp_connect_tg -> verifyOspConnectTG(token);
            case osp_callback -> verifyOspCallback(token, configDTO);
            case join_tg -> verifyJoinTG(token);
            case boost_tg_channel -> verifyBoostTGChannel(token, configDTO, ext);
            case tg_premium -> verifyTGPremium(token, ext);
            case wallet_bind -> verifyConnectWallet(token);
            case member_castle_level -> verifyMemberCastleLevel(token, configDTO);
            case create_dapp -> verifyCreateDapp(token);
            case create_deek_profile -> verifyCreateDeekProfile(saasId, ext);
            case connect_sahara -> verifySahara(token, configDTO);
            case connect_okx -> verifyOkx(token, configDTO);
            case connect_email,connect_facebook,connect_google,connect_line,connect_telegram -> verifyCustomerBind(saasId, customerId, code);
            default -> VerifyResult.builder().result(false).build();
        };
    }

    private VerifyResult verifyCustomerBind(String saasId, String customerId, String code) {
        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(saasId, customerId);
        return switch (ActivityTaskConstant.TaskCodeEnum.valueOf(code)){
            case connect_email -> customerBindDTO.getEmailId() != null ? VerifyResult.builder().result(true).contentIds(List.of(customerId + "_" + code)).build() : VerifyResult.builder().result(false).build();
            case connect_facebook -> customerBindDTO.getFacebookId() != null ? VerifyResult.builder().result(true).contentIds(List.of(customerId + "_" + code)).build() : VerifyResult.builder().result(false).build();
            case connect_google -> customerBindDTO.getGoogleId() != null ? VerifyResult.builder().result(true).contentIds(List.of(customerId + "_" + code)).build() : VerifyResult.builder().result(false).build();
            case connect_twitter -> customerBindDTO.getTwitterId() != null ? VerifyResult.builder().result(true).contentIds(List.of(customerId + "_" + code)).build() : VerifyResult.builder().result(false).build();
            case connect_line -> customerBindDTO.getLineId() != null ? VerifyResult.builder().result(true).contentIds(List.of(customerId + "_" + code)).build() : VerifyResult.builder().result(false).build();
            case connect_telegram -> customerBindDTO.getTelegramId() != null ? VerifyResult.builder().result(true).contentIds(List.of(customerId + "_" + code)).build() : VerifyResult.builder().result(false).build();
            default -> VerifyResult.builder().result(false).build();
        };
    }

    private VerifyResult verifyCreateDeekProfile(String saasId, Map<String, String> ext) {
        if(null == ext || ext.isEmpty()){
            return VerifyResult.builder().result(false).build();
        }
        try {
            String jwtToken = ext.get(JWT_TOKEN);
            JWT jwt = JWTUtil.parseToken(jwtToken);
            String userId = (String)jwt.getPayload("user_id");
            HttpUrl.Builder urlBuilder = HttpUrl.parse(SaasConfigLoader.getConfig(saasId).getApiHost() + "/v1/profile/" + userId).newBuilder();
            Headers.Builder headerBuilder = new Headers.Builder()
                .add(JWT_TOKEN, jwtToken);
            Request request = new Request.Builder()
                .url(urlBuilder.build())
                .headers(headerBuilder.build())
                .build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            log.info("verifyCreateDeekProfile get profile userId:{}, response: isSuccessful:{}", userId, response.isSuccessful());
            if(response.isSuccessful() && response.body() != null){
                String responseBody = response.body().string();
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                //校验逻辑 basic下的chainProfileId和handle同时不为空则认为deek profile创建成功
                log.info("verifyCreateDeekProfile get profile responseBody: {}",JSON.toJSONString(jsonObject));
                com.alibaba.fastjson2.JSONObject obj = jsonObject.getJSONObject("obj");
                if (Objects.nonNull(obj)) {
                  com.alibaba.fastjson2.JSONObject basicObj = obj.getJSONObject("basic");
                  if (Objects.nonNull(basicObj)
                      && Objects.nonNull(basicObj.get("chainProfileId"))
                      && Objects.nonNull(basicObj.get("handle"))) {
                    return VerifyResult.builder().result(true).contentIds(List.of(userId)).build();
                  }
                }
            }
        } catch (Exception e) {
            log.error("verifyCreateDeekProfile get profile error", e);
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyConnectX(AccessToken token){
        try{
            SocialUserInfo user = threePlatformApi.twitter().getCurrentUserRequest(token).execute();
            if(user != null){
                return VerifyResult.builder().result(true).contentIds(List.of(user.getUserId()))
                        .socialId(user.getUserId()).socialName(user.getUserName()).build();
            }
            return VerifyResult.builder().result(false).build();
        }catch (Exception ex){
            log.error("verifyConnectX exception", ex);
            return VerifyResult.builder().result(false).build();
        }
    }

    private VerifyResult verifyLikePostX(AccessToken token, TaskConfigDTO configDTO) throws ActivityException {
        SocialUserInfo user = threePlatformApi.twitter().getCurrentUserRequest(token).execute();
        if(user == null){
            throw new ActivityException(ActivityResponseCode.ACCESS_TOKEN_EXPIRE);
        }
        List<Tweet> tweetList =
                threePlatformApi.twitter().getLikedTweetRequest(token).buildLikedTweetRequest(user.getUserId()).execute();
        log.info("verifyListPostX response:{}", tweetList);
        if(CollectionUtils.isNotEmpty(tweetList)){
            if(configDTO.getAttr().get("twitter-username") != null){
                String tweetName = configDTO.getAttr().get("twitter-username");
                List<Tweet> tweets = tweetList.stream().filter(t -> Objects.equals(t.getAuthorId(), tweetName)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(tweets)){
                    String twitterId = token.getUserId() + "_" + tweets.get(0).getId();
                    TaskCompletedResult completedResult = activityTaskService.getTaskResultBySocial("twitter", twitterId, TaskCycleDomain.getCurrencyCycle(configDTO, null), configDTO.getTaskId());
                    if(completedResult == null || !completedResult.isDone()){
                        return VerifyResult.builder().result(true).contentIds(List.of(twitterId)).build();
                    }
                }
            }else{
                String tweetId = configDTO.getUrl().substring(configDTO.getUrl().lastIndexOf("/") + 1);
                return VerifyResult.builder().result(tweetList.stream().anyMatch(t -> t.getId().equals(tweetId))).contentIds(List.of(tweetId)).build();
            }
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyRetweetPostX(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        if(token.getAccessToken() == null){
            String latestTweet = twitterApiUtil.searchRetweet(token.getTwitterHandleName(), configDTO.getAttr().get("twitter-username"), configDTO.getTaskId());
            if(latestTweet != null){
                return VerifyResult.builder().result(true).contentIds(List.of(latestTweet)).build();
            }
            return VerifyResult.builder().result(false).build();
        }else{
            SocialUserInfo user = threePlatformApi.twitter().getCurrentUserRequest(token).execute();
            if(user == null){
                throw new ActivityException(ActivityResponseCode.ACCESS_TOKEN_EXPIRE);
            }
            String tweetId = configDTO.getUrl().substring(configDTO.getUrl().lastIndexOf("/") + 1);
            String twitterUserName = configDTO.getAttr().get("twitter-username");
            OffsetDateTime startTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd 00:00:00")).toInstant().atOffset(ZoneOffset.UTC);
            OffsetDateTime endTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd HH:mm:ss")).toInstant().atOffset(ZoneOffset.UTC).plusSeconds(-10);
            List<Tweet> tweetList =
                    threePlatformApi.twitter().getReTweetRequest(token).buildRetweetNameAndTweet(user.getUserName(), tweetId, twitterUserName, startTime, endTime).execute();
            log.info("verifyRetweetPostX response:{}", tweetList);
            if(CollectionUtils.isNotEmpty(tweetList)){
                List<String> twitterIds = tweetList.stream().map(Tweet::getId).toList();
                return VerifyResult.builder().result(true).contentIds(List.of(twitterIds.get(0))).build();
            }
            return VerifyResult.builder().result(false).build();
        }
    }

    private VerifyResult verifyFollowX(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        if(token.getAccessToken() == null){
            boolean following = twitterApiUtil.isFollowing(token.getTwitterHandleName(), configDTO.getAttr().get("twitter-follow"));
            return VerifyResult.builder().result(following).contentIds(List.of(configDTO.getAttr().get("twitter-follow"))).build();
        }else{
            String followed = threePlatformApi.twitter().getFollowingRequest(token).buildFollowingRequest(configDTO.getAttr().get("twitter-follow")).execute();
            if(followed == null){
                return VerifyResult.builder().result(false).build();
            }
            return VerifyResult.builder().result(true).contentIds(List.of(followed)).build();
        }
    }

    public VerifyResult verifyFollowX(AccessToken token, String targetHandle) throws ActivityException{
        String followed = threePlatformApi.twitter().getFollowingRequest(token).buildFollowingRequest(targetHandle).execute();
        if(followed == null){
            return VerifyResult.builder().result(false).build();
        }
        return VerifyResult.builder().result(true).contentIds(List.of(followed)).build();
    }

    private VerifyResult verifyCommentPostX(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        SocialUserInfo user = threePlatformApi.twitter().getCurrentUserRequest(token).execute();
        if(user == null){
            throw new ActivityException(ActivityResponseCode.ACCESS_TOKEN_EXPIRE);
        }
        String tweetId = configDTO.getUrl().substring(configDTO.getUrl().lastIndexOf("/") + 1);
        List<Tweet> tweetList =
                threePlatformApi.twitter().getCommentRequest(token).buildCommentPostRequest(user.getUserName(), tweetId).execute();
        log.info("verifyCommentPostX response:{}", tweetList);
        if(CollectionUtils.isNotEmpty(tweetList)){
            return VerifyResult.builder().result(true).contentIds(tweetList.stream().map(Tweet::getId).collect(Collectors.toList())).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyReply(AccessToken token, TaskConfigDTO configDTO){
        SocialUserInfo user = threePlatformApi.twitter().getCurrentUserRequest(token).execute();
        OffsetDateTime endTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd HH:mm:ss")).toInstant().atOffset(ZoneOffset.UTC).plusSeconds(-10);
        OffsetDateTime startTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd 00:00:00")).toInstant().atOffset(ZoneOffset.UTC);

        List<Tweet> tweets = threePlatformApi.twitter().getReplyPostRequest(token)
                .build(user.getUserName(),
                        configDTO.getAttr().get("twitter-keyword"),
                        startTime,
                        endTime)
                .execute();
        if(CollectionUtils.isNotEmpty(tweets)){
            if(StringUtils.isNotBlank(configDTO.getAttr().get("twitter-kols"))){
                List<String> kols = Arrays.asList(configDTO.getAttr().get("twitter-kols").split(","));
                List<String> twitterIds = new ArrayList<>();
                for(Tweet tweet : tweets){
                    if(kols.contains(tweet.getInReplyToUserId())){
                        twitterIds.add(tweet.getId());
                    }
                }
                if(CollectionUtils.isNotEmpty(twitterIds)){
                    return VerifyResult.builder().result(true).contentIds(twitterIds).build();
                }
            }else{
                List<String> twitterIds = new ArrayList<>();
                for(Tweet tweet : tweets){
                    twitterIds.add(tweet.getId());
                }
                return VerifyResult.builder().result(true).contentIds(twitterIds).build();
            }
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifySubject(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        if(token.getAccessToken() == null){
            String latestTweet = twitterApiUtil.searchTweet(token.getTwitterHandleName(), configDTO.getAttr().get("twitter-keyword"), configDTO.getTaskId());
            if(latestTweet != null){
                return VerifyResult.builder().result(true).contentIds(List.of(latestTweet)).build();
            }
            return VerifyResult.builder().result(false).build();
        }else{
            SocialUserInfo user = threePlatformApi.twitter().getCurrentUserRequest(token).execute();
            if(user == null){
                throw new ActivityException(ActivityResponseCode.ACCESS_TOKEN_EXPIRE);
            }
            List<Tweet> tweets;
            if(ActivityTaskConstant.TaskCycleEnum.daily.equals(configDTO.getCycle()) || configDTO.getLastPost()){
                OffsetDateTime endTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd HH:mm:ss")).toInstant().atOffset(ZoneOffset.UTC);
                OffsetDateTime startTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd 00:00:00")).toInstant().atOffset(ZoneOffset.UTC);
                tweets = threePlatformApi.twitter().getCurrentUserPostRequest(token)
                        .build(configDTO.getAttr().get("twitter-keyword"),
                                startTime,
                                endTime)
                        .execute();
            }else{
                OffsetDateTime endTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd HH:mm:ss")).toInstant().atOffset(ZoneOffset.UTC).plusSeconds(-10);
                OffsetDateTime startTime = TimeUtil.parse(TimeUtil.getDataStr(new Date(), "yyyy-MM-dd 00:00:00")).toInstant().atOffset(ZoneOffset.UTC);
                tweets = threePlatformApi.twitter().getPostSubjectRequest(token)
                        .build(user.getUserName(),
                                configDTO.getAttr().get("twitter-keyword"),
                                startTime,
                                endTime,
                                configDTO.getLimit(token.getVipLevel()))
                        .execute();
            }
            if(CollectionUtils.isNotEmpty(tweets)){
                return VerifyResult.builder().result(true).contentIds(tweets.stream().map(Tweet::getId).collect(Collectors.toList())).build();
            }
            return VerifyResult.builder().result(false).build();
        }
    }

    private VerifyResult verifyConnectDC(AccessToken token){
        SocialUserInfo user = threePlatformApi.discord().getCurrentUserRequest(token).execute();
        return user != null
                ? VerifyResult.builder().result(true).contentIds(List.of(user.getUserId())).socialId(user.getUserId()).socialName(user.getUserName()).build()
                : VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyJoinDC(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        SocialUserInfo user = threePlatformApi.discord().getCurrentUserRequest(token).execute();
        if(user == null){
            throw new ActivityException(ActivityResponseCode.ACCESS_TOKEN_EXPIRE);
        }
        String guild = threePlatformApi.discord().getJoinChannelRequest(token).buildGetJoinRequest(configDTO.getAttr().get("discord-guild-role"), configDTO.getAttr().get("discord-guild"), user.getUserId()).execute();
        return guild != null
                ? VerifyResult.builder().result(true).contentIds(List.of(guild)).build()
                : VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyJoinGame(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        Boolean success = threePlatformApi.game().getGameRequest(token)
                .buildJoinGameRequest(configDTO.getAttr().get("verifyId"))
                .execute();
        return BooleanUtils.isTrue(success)
                ? VerifyResult.builder().result(true).contentIds(List.of(configDTO.getAttr().get("verifyId"))).build()
                : VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyPlayGame(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        Boolean success = threePlatformApi.game().getGameRequest(token)
                .buildJoinGameRequest(configDTO.getAttr().get("verifyId"))
                .execute();
        return BooleanUtils.isTrue(success)
                ? VerifyResult.builder().result(true).contentIds(List.of(configDTO.getAttr().get("verifyId"))).build()
                : VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyExternalReward(AccessToken token, TaskConfigDTO configDTO) {
        return VerifyResult.builder().result(true).contentIds(List.of(token.getUserId())).build();
    }

    private VerifyResult verifyXAuthorize(String saasId, String customerId) {
        boolean result;
        if (authService.getAuthToken(saasId, customerId, OpenSocialPlatformEnum.twitter.name()) != null) {
            result = true;
        } else {
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(saasId, customerId);
            result = customerBindDTO != null && StringUtils.isNotBlank(customerBindDTO.getTwitterId());
        }
        return result ? VerifyResult.builder().result(true).contentIds(List.of(customerId + "_" + saasId)).build()
                : VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyNameX(AccessToken token, TaskConfigDTO configDTO) throws ActivityException{
        SocialUserInfo user = threePlatformApi.twitter().getCurrentUserRequest(token).build(false).execute();
        if(user == null){
            throw new ActivityException(ActivityResponseCode.ACCESS_TOKEN_EXPIRE);
        }
        String[] twitterNames = configDTO.getAttr().get("twitter-username").split(",");
        for(String twitterName : twitterNames){
            boolean contains = user.getName().contains(twitterName);
            if(contains){
                return VerifyResult.builder().result(true).contentIds(List.of(user.getUserName())).build();
            }
        }
        return VerifyResult.builder().result(false).build();
    }

    public String parseText(String text, Map<String, Object> param){
        if(param == null){
            return text;
        }
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("map", param);
        Integer value = new SpelExpressionParser().parseExpression(text)
                .getValue(context, Integer.class);
        return value == null ? null : value.toString();
    }

    private VerifyResult verifyOspProfile(AccessToken token, TaskConfigDTO configDTO) {
        if(StringUtils.isBlank(token.getAddress())){
            return VerifyResult.builder().result(false).build();
        }
        String osChainId = Objects.nonNull(configDTO.getAttr()) ? configDTO.getAttr().get("osChainId") : null;
        String appId = Objects.nonNull(configDTO.getAttr()) ? configDTO.getAttr().get("appId") : null;
        String profileId = threePlatformApi.osp().getOspProfileRequest()
                .build(token.getAddress(), osChainId, appId).execute();
        if(StringUtils.isNotBlank(profileId)){
            return VerifyResult.builder().result(true).contentIds(List.of(profileId)).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyOspConnectTG(AccessToken token) {
        CustomerBindDTO bindDTO = remoteCustomerBindService.findByUid(token.getSaasId(), token.getUserId());
        String profileId = threePlatformApi.osp().getOspConnectTGRequest().build(token.getSaasId(), bindDTO.getCid()).execute();
        if(StringUtils.isNotBlank(profileId)){
            return VerifyResult.builder().result(true).contentIds(List.of(profileId)).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyOspCallback(AccessToken token, TaskConfigDTO configDTO) {
        CustomerBindDTO bindDTO = remoteCustomerBindService.findByUid(token.getSaasId(), token.getUserId());
        String taskType = configDTO.getAttr().get("taskType");
        JSONObject extra = new JSONObject().
                fluentPut("eventCode", StringUtils.isNotBlank(configDTO.getShowCode()) ? configDTO.getShowCode() : configDTO.getCode());
        Boolean result = threePlatformApi.osp().getOspCallBackRequest()
                .build(token.getSaasId(), bindDTO.getCid(), configDTO.getTaskId(), taskType, extra.toJSONString(), configDTO.getShowCode()).execute();
        if (Boolean.TRUE.equals(result)) {
            return VerifyResult.builder().result(true).contentIds(List.of(token.getUserId())).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyCreateDapp(AccessToken token) {
        CustomerBindDTO bindDTO = remoteCustomerBindService.findByUid(token.getSaasId(), token.getUserId());
        String result = threePlatformApi.osp().getOspCreateDappRequest()
                .build(token.getSaasId(), bindDTO.getCid()).execute();
        if (StringUtils.isNotBlank(result)) {
            return VerifyResult.builder().result(true).contentIds(List.of(result)).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyJoinTG(AccessToken token){
        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(token.getSaasId(), token.getUserId());
        String tgId = threePlatformApi.osp().getOspConnectTGRequest().build(token.getSaasId(), customerBindDTO.getCid()).execute();
        if(StringUtils.isBlank(tgId)){
            return VerifyResult.builder().result(false).build();
        }
        token.setTgId(Long.parseLong(tgId));
        TGBotRequest botRequest = threePlatformApi.tg().getTGBotRequest(token);
        ChatMember chatMember = questsTelegramBot.getChatMember(botRequest);
        if(chatMember != null){
            return VerifyResult.builder().result(true).contentIds(List.of(tgId)).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyBoostTGChannel(AccessToken token, TaskConfigDTO configDTO, Map<String, String> ext) {
        String initData = ext.get("initData");
        String tgChannel = configDTO.getAttr().get("tgChannel");
        if (StringUtils.isBlank(initData) || StringUtils.isBlank(tgChannel)) {
            return VerifyResult.builder().result(false).build();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("tgChannel", tgChannel);
        Boolean verify = threePlatformApi.telePulse().getTelePulseRequest().build(token.getUserId(), initData, params).verifyBoostTGChannel();
        if (verify) {
            return VerifyResult.builder().result(true).contentIds(List.of(token.getUserId() + "boost" + tgChannel)).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyTGPremium(AccessToken token, Map<String, String> ext) {
        String initData = ext.get("initData");
        if (StringUtils.isBlank(initData)) {
            return VerifyResult.builder().result(false).build();
        }
        Boolean verify = threePlatformApi.telePulse().getTelePulseRequest().build(token.getUserId(), initData, new HashMap<>()).verifyTGPremium();
        if (verify) {
            return VerifyResult.builder().result(true).contentIds(List.of(token.getUserId() + "premium")).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyConnectWallet(AccessToken token){
        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(token.getSaasId(), token.getUserId());
        String address = threePlatformApi.osp().getOspConnectWalletRequest().build(token.getSaasId(), customerBindDTO.getCid()).execute();
        if(StringUtils.isNotBlank(address)){
            return VerifyResult.builder().result(true).contentIds(List.of(address)).build();
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifyMemberCastleLevel(AccessToken token, TaskConfigDTO configDTO) {
        try {
            Integer levelLimit = Integer.valueOf(configDTO.getAttr().get("level_limit"));
            MemberProductItemDTO memberProductItemDTO = remoteMemberCastleService.memberCastle(token.getSaasId(),
                    token.getUserId());
            if (memberProductItemDTO.getSort() >= levelLimit) {
                return VerifyResult.builder().result(true).contentIds(List.of(memberProductItemDTO.getSort().toString())).build();
            }
        } catch (Exception e) {
            log.error("verifyMemberCastleLevel error, token:{}, taskConfig:{}", JSON.toJSONString(token),
                    JSON.toJSONString(configDTO), e);
        }
        return VerifyResult.builder().result(false).build();
    }

    private VerifyResult verifySahara(AccessToken token, TaskConfigDTO configDTO){
        String address = saharaTaskService.check(token.getAddress());
        if(StringUtils.isBlank(address)){
            return VerifyResult.builder().result(false).build();
        }
        return VerifyResult.builder().result(true).contentIds(List.of(address)).build();
    }

    private VerifyResult verifyOkx(AccessToken token, TaskConfigDTO configDTO){
        String address = okxTaskService.check(token.getAddress());
        if(StringUtils.isBlank(address)){
            return VerifyResult.builder().result(false).build();
        }
        return VerifyResult.builder().result(true).contentIds(List.of(address)).build();
    }
}
