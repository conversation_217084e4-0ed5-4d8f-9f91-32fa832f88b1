package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.param.ActivityBatchParam;
import com.kikitrade.activity.facade.award.ActivityBatchDTO;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.common.CronUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.flow.job.BatchJobFactory;
import com.kikitrade.activity.service.importing.source.CsvSourceService;
import com.kikitrade.activity.service.importing.source.domain.ImportSourceDTO;
import com.kikitrade.activity.service.job.ActivityRewardJob;
import com.kikitrade.activity.service.job.ElasticJobService;
import com.kikitrade.activity.service.model.RewardRule;
import com.kikitrade.framework.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityBatchNewServiceImpl implements ActivityBatchNewService {

    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private ActivityBatchBuilder activityBatchBuilder;
    @Autowired
    private KactivityProperties kactivityProperties;
    @Resource
    @Lazy
    private ElasticJobService elasticJobService;
    @Resource
    @Lazy
    private ActivityRewardJob activityRewardJob;
    @Resource
    @Lazy
    private List<CsvSourceService> csvSourceService;
    @Resource
    @Lazy
    private ActivityEntityService activityEntityService;
    @Resource
    private RedisService redisService;
//    @Autowired
//    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private BatchJobFactory batchJobFactory;

    @Override
    public ActivityBatch findByBatchId(String batchId) {
        if (batchId == null) {
            return null;
        }
        return activityBatchBuilder.queryById(batchId);
    }

    /**
     * 根据批次id查询
     *
     * @param batchId
     * @return
     */
    @Override
    public ActivityBatch findByBatchIdFromCache(String batchId) {
        if (batchId == null) {
            return null;
        }
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_BATCH_VALUE_KEY.getPrefix(), batchId);
        String batchCache = redisService.get(key);
        if (StringUtils.isNotBlank(batchCache)) {
            return JSON.parseObject(batchCache, ActivityBatch.class);
        }
        ActivityBatch activityBatch = activityBatchBuilder.queryById(batchId);
        redisService.save(key, JSON.toJSONString(activityBatch));
        return activityBatch;
    }

    @Override
    public Result<ActivityBatch> saveOrUpdate(ActivityBatchDTO activityBatchDto) {

        boolean check = existCheck(activityBatchDto.getId(), activityBatchDto.getBatchName());
        if (check) {
            return Result.of(Result.ResultCode.PARAM_INVALID, "批次名称重复");
        }
        if(activityBatchDto.getBatchName().contains("/")){
            return Result.of(Result.ResultCode.PARAM_INVALID, "批次名称不能包含 / ");
        }
        ActivityEntity activityEntity = activityEntityService.findById(activityBatchDto.getActivityId());
        try {
            ActivityBatch activityBatch = dtoToEntity(activityBatchDto,activityEntity);
            log.info("batch saveOrUpdate:{}", JSON.toJSONString(activityBatch));
            if (StringUtils.isBlank(activityBatch.getBatchId())) {
                activityBatch.setBatchId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(activityBatchBuilder.getTableName())));
                return this.save(activityBatchDto, activityEntity, activityBatch);
            } else {
                return update(activityBatchDto, activityBatch);
            }
        } catch (Exception ex) {
            log.error("activitybatch save error", ex);
            return Result.of(Result.ResultCode.SYSTEM_ERROR, ex.getMessage());
        }
    }

    @Compensation(cancelMethod = "cancelSave")
    public Result<ActivityBatch> save(ActivityBatchDTO activityBatchDto, ActivityEntity activityEntity, ActivityBatch activityBatch) throws Exception {
        Result<String> result = this.save(activityBatch, activityEntity);
        if(!result.isSuccess()){
            return Result.of(Result.ResultCode.SYSTEM_ERROR, "");
        }
        if(StringUtils.isNotBlank(activityBatchDto.getSourceOssUrl())){
            activityBatch.setSourceOssUrl(activityBatchDto.getSourceOssUrl());
            Result<String> uploadCsv = uploadCsv(activityBatch);
            if(!uploadCsv.isSuccess()){
                throw new Exception(uploadCsv.getMessage());
            }
            updateBatch(activityBatch);
        }
        ActivityBatch batch = activityBatchBuilder.queryById(activityBatch.getBatchId());
        return Result.of(Result.ResultCode.SUCCESS, "",batch);
    }

    public Result<ActivityBatch> cancelSave(ActivityBatchDTO activityBatchDto, ActivityEntity activityEntity, ActivityBatch activityBatch) throws Exception{
        activityBatchBuilder.deleteById(activityBatch);
        return Result.of(Result.ResultCode.SYSTEM_ERROR,"");
    }

    private Result<ActivityBatch> update(ActivityBatchDTO activityBatchDto, ActivityBatch activityBatch){
        ActivityBatch batch = activityBatchBuilder.queryById(activityBatch.getBatchId());
        if (batch == null) {
            return Result.of(Result.ResultCode.PARAM_INVALID, "修改失败，批次不存在");
        }
        if (!ActivityConstant.BatchRewardStatusEnum.NOT_IMPORTED.isEquals(batch.getRewardStatus())) {
            return Result.of(Result.ResultCode.PARAM_INVALID, "修改失败，批次不是待导入状态");
        }
        if(StringUtils.isNotBlank(batch.getSourceOssUrl()) && !batch.getSourceOssUrl().equals(activityBatch.getSourceOssUrl())){
            return Result.of(Result.ResultCode.PARAM_INVALID, "修改失败，名单不能多次上传");
        }
        updateBatch(activityBatch);
        if(StringUtils.isNotBlank(activityBatchDto.getSourceOssUrl())){
            activityBatch.setSourceOssUrl(activityBatchDto.getSourceOssUrl());
            Result<String> uploadCsv = uploadCsv(activityBatch);
            if(!uploadCsv.isSuccess()){
                activityBatch.setStatus(false);
                return Result.of(Result.ResultCode.SYSTEM_ERROR, uploadCsv.getMessage() + ", 请重新上传");
            }
            updateBatch(activityBatch);
        }
        activityBatch.setSourceOssUrl(activityBatchDto.getSourceOssUrl());
        updateBatch(activityBatch);
        batch = activityBatchBuilder.queryById(activityBatch.getBatchId());
        return Result.of(Result.ResultCode.SUCCESS, "",batch);
    }

    /**
     * 创建批次
     *
     * @param activityBatch
     * @return
     */
    @Override
    public Result<String> save(ActivityBatch activityBatch, ActivityEntity activityEntity) {
        activityBatch.setSaasId(kactivityProperties.getSaasId());
        activityBatch.setToken(seqGeneraterService.next(CustomerSeqRuleBuilder.RULE_BATCH_TOKEN));
        activityBatch.setStatus(Boolean.TRUE);
        activityBatch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.NOT_IMPORTED.name());
        activityBatch.setCreated(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        activityBatch.setShardCount(kactivityProperties.getRewardShard());
        activityBatch.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        activityBatch.setActivityType(activityEntity.getType());
        activityBatch.setBatchId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(activityBatchBuilder.getTableName())));
        boolean result = activityBatchBuilder.insert(activityBatch);
        return result ? new Result(true, "success", activityBatch.getBatchId()) : new Result<>(false, "save fail");
    }

    @Override
    public Result<String> deleteBatch(String batchId) {
        Result result = new Result();
        ActivityBatch batch = activityBatchBuilder.queryById(batchId);
        if (!ActivityConstant.BatchRewardStatusEnum.NOT_IMPORTED.isEquals(batch.getRewardStatus())) {
            result.setSuccess(false);
            result.setMessage("修改失败，已存在发奖名单，不能修改");
            return result;
        }
        ActivityBatch activityBatch = new ActivityBatch();
        activityBatch.setActivityId(batch.getActivityId());
        activityBatch.setBatchId(batch.getBatchId());
        activityBatch.setStatus(false);
        updateBatch(activityBatch);
        return result;
    }

    /**
     * @param activityBatchParam
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public PageResult findForPage(ActivityBatchParam activityBatchParam, int pageNo, int pageSize) {
        return activityBatchBuilder.queryForList(activityBatchParam, pageSize * (pageNo - 1), pageSize);
    }

    @Override
    public Result<String> audit(String batchId, ActivityConstant.AuditTypeEnum auditTypeEnum) {

        ActivityBatch batch = activityBatchBuilder.queryById(batchId);
        //已经审核过，无法在审核
        if (ActivityConstant.BatchRewardStatusEnum.valueOf(batch.getRewardStatus()).getCode() >= ActivityConstant.BatchRewardStatusEnum.REJECT.getCode()) {
            return new Result<>(false, String.format(Result.Constant.STATUS_NOTALLOW_AUDIT,
                    ActivityConstant.BatchRewardStatusEnum.valueOf(batch.getRewardStatus()).getDesc(), auditTypeEnum.getDesc()));
        }
        if (ActivityConstant.AuditTypeEnum.APPROVE == auditTypeEnum) {
            //通过
            return approve(batch);
        } else {
            //驳回
            return reject(batch);
        }
    }

    private Result<String> approve(ActivityBatch batch) {
        batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.APPROVE.name());
        updateBatch(batch);
        //创建发奖job
        Map<String, String> param = new HashMap<String, String>() {{
            put("activityId", batch.getActivityId());
            put("batchId", batch.getBatchId());
        }};
        if(BooleanUtils.isTrue(batch.getScheduled())
                && TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)).compareTo(TimeUtil.parse(batch.getScheduledTime())) < 0){
            elasticJobService.createJob(activityRewardJob, elasticJobService.getJobNameForReward(batch.getBatchId()), CronUtil.getCronForMinute(1), batch.getShardCount(), param);
        }else{
            batchJobFactory.runManualRewardJob(batch.getActivityId(), batch.getBatchId());
        }
        //异步调用发奖，创建任务做补充
        return new Result<>();
    }

    private Result<String> reject(ActivityBatch status) {
        status.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.REJECT.name());
        updateBatch(status);
        return new Result<>();
    }


    @Override
    public Boolean updateBatchToken(String batchId, String token) {
        ActivityBatch activityBatch = activityBatchBuilder.queryById(batchId);
        activityBatch.setToken(token);
        updateBatch(activityBatch);
        return true;
    }

    @Override
    public int count(String activityId, List<String> statusList) {
        return activityBatchBuilder.countByStatus(activityId, statusList);
    }

    @Override
    public void updateBatchStatus(String batchId, String rewardStatus) {
        ActivityBatch activityBatch = activityBatchBuilder.queryById(batchId);
        activityBatch.setRewardStatus(rewardStatus);
        updateBatch(activityBatch);
    }

    @Override
    public Boolean updateBatch(ActivityBatch activityBatch) {
        try{
            activityBatch.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
            String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_BATCH_VALUE_KEY.getPrefix(),activityBatch.getBatchId());
            redisService.del(key);
            boolean update = activityBatchBuilder.update(activityBatch);
            redisService.del(key);
            return update;
        }catch (Exception ex){
            log.error("update batch error:{}", activityBatch, ex);
            throw ex;
        }
    }

    /**
     * 活动下绑定的批次数量
     * @param activityId
     * @return
     */
    @Override
    public int countByActivity(String activityId) {
        return activityBatchBuilder.countByActivityId(activityId);
    }

    /**
     * 查询某个活动下最后一个批次
     *
     * @param activityId
     * @return
     */
    @Override
    public ActivityBatch findLastBatchByActivity(String activityId) {
        return activityBatchBuilder.queryLastByActivityId(activityId);
    }

    private ActivityBatch dtoToEntity(ActivityBatchDTO activityBatchDTO, ActivityEntity activityEntity) {
        ActivityBatch activityBatch = new ActivityBatch();
        activityBatch.setBatchId(activityBatchDTO.getId());
        activityBatch.setName(activityBatchDTO.getBatchName());
        activityBatch.setActivityId(activityBatchDTO.getActivityId());
        activityBatch.setActivityName(activityBatchDTO.getActivityName());
        activityBatch.setRewardType(activityBatchDTO.getRewardType());
        activityBatch.setAmount(activityBatchDTO.getAmount());
        activityBatch.setCurrency(activityBatchDTO.getCurrency());
        activityBatch.setRemark(activityBatchDTO.getRemark());
        activityBatch.setAmended(activityBatchDTO.getAmended());
        activityBatch.setScheduled(activityBatchDTO.getScheduled());
        activityBatch.setScheduledTime(activityBatchDTO.getScheduledTime());
        activityBatch.setStatus(true);
        if(CollectionUtils.isNotEmpty(activityBatchDTO.getRewardRuleList())){
            List<RewardRule> rules = new ArrayList();
            if(ActivityConstant.ActivityTypeEnum.INVITE.name().equals(activityEntity.getType())){
                Map<String, List<RewardRule>> mapRules = activityBatchDTO.getRewardRuleList().stream().map(rewardRule -> new RewardRule().toEntity(rewardRule)).collect(Collectors.groupingBy(RewardRule::getSide));
                for(Map.Entry<String, List<RewardRule>> entry : mapRules.entrySet()){
                    isOverlapped(entry.getValue());
                    rules.addAll(entry.getValue());
                }
            }else{
                rules = activityBatchDTO.getRewardRuleList().stream().map(rewardRule -> new RewardRule().toEntity(rewardRule)).collect(Collectors.toList());
                isOverlapped(rules);
            }
            activityBatch.setRewardConfig(JSON.toJSONString(rules));
        }
        return activityBatch;
    }

    private boolean existCheck(String batchId, String name) {
        ActivityBatchParam param = ActivityBatchParam.builder()
                .batchName(name)
                .build();
        PageResult pageResult = activityBatchBuilder.queryForList(param, 0, 1);
        if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getRows())) {
            ActivityBatch o = (ActivityBatch) pageResult.getRows().get(0);
            return !batchId.equals(o.getBatchId());
        }
        return false;
    }

    private Result<String> uploadCsv(ActivityBatch activityBatch){
        ImportSourceDTO dto = new ImportSourceDTO();
        dto.setBatchId(activityBatch.getBatchId());
        ActivityEntity activityEntity = activityEntityService.findById(activityBatch.getActivityId());
        Result<String> csvResult = csvSourceService.stream().filter(service -> service.support(activityEntity.getType())).findFirst().get().importSource(activityBatch.getSourceOssUrl(), dto);
        if(!csvResult.isSuccess()){
            return new Result<>(false, csvResult.getMessage());
        }
        return new Result<>();
    }

    private void isOverlapped(List<RewardRule> rules){
        Map<String, List<RewardRule>> ruleMap = new HashMap<>();
        for(RewardRule rewardRule : rules){
            if(ruleMap.containsKey(rewardRule.getVipLevel())){
                ruleMap.get(rewardRule.getVipLevel()).add(rewardRule);
            }else{
                ruleMap.put(rewardRule.getVipLevel(), new ArrayList<>(Arrays.asList(rewardRule)));
            }
        }
        for(Map.Entry<String, List<RewardRule>> map : ruleMap.entrySet()){
            List<RewardRule> rs = map.getValue();
            rs.sort(Comparator.comparing(RewardRule::getMin));
            log.info("rules:{}",rules);
            for(int i = 0; i < rs.size(); i++){
                if(rs.get(i).getMin() == null){
                    throw new IllegalArgumentException("Batch creation rules cannot null");
                }
                if(rs.get(i).getMax() != null && rs.get(i).getMax() < rs.get(i).getMin()){
                    throw new IllegalArgumentException("scope max must be greater scope min");
                }
                if(i > 0 && rs.get(i).getMin().equals(rs.get(i - 1).getMin())){
                    throw new IllegalArgumentException("Batch creation rules cannot overlapped");
                }
                if(i > 0 && rs.get(i-1).getMax() != null && rs.get(i).getMin() < rs.get(i-1).getMax()){
                    throw new IllegalArgumentException("Batch creation rules cannot overlapped");
                }
            }
        }
    }
}
