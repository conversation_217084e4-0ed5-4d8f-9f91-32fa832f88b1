package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.api.model.request.reward.ExchangeTicketsRequest;
import com.kikitrade.activity.api.model.response.reward.ExchangeTicketsResponse;
import com.kikitrade.asset.model.constant.AssetType;

/**
 * 抽奖券管理服务接口
 * 支持两阶段抽奖系统的抽奖券兑换、消费、查询等功能
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface LotteryTicketService {
    
    /**
     * 资产兑换抽奖券
     * 
     * @param request 兑换请求
     * @return 兑换结果
     */
    ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request);
    
    /**
     * 查询用户抽奖券余额
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param ticketType 抽奖券类型
     * @return 抽奖券余额
     */
    Integer getUserTicketBalance(String userId, String saasId, String ticketType);
    
    /**
     * 消费抽奖券（用于抽奖）
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param ticketType 抽奖券类型
     * @param count 消费数量
     * @return 是否成功
     */
    boolean consumeTickets(String userId, String saasId, String ticketType, Integer count);
    
    /**
     * 退还抽奖券（抽奖失败时）
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param ticketType 抽奖券类型
     * @param count 退还数量
     * @return 是否成功
     */
    boolean refundTickets(String userId, String saasId, String ticketType, Integer count);
    
    /**
     * 获取抽奖券兑换汇率
     * 
     * @param assetType 资产类型
     * @param ticketType 抽奖券类型
     * @return 兑换汇率（资产数量:抽奖券数量）
     */
    String getExchangeRate(String assetType, String ticketType);
    
    /**
     * 验证用户资产是否足够兑换
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param assetType 资产类型
     * @param assetAmount 需要的资产数量
     * @return 是否足够
     */
    boolean validateUserAsset(String userId, String saasId, AssetType assetType, Long assetAmount);
}
