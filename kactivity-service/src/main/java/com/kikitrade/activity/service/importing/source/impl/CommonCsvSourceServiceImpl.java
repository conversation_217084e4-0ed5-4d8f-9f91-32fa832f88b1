package com.kikitrade.activity.service.importing.source.impl;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.AssertUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Map;

@Service
public class CommonCsvSourceServiceImpl extends AbstractCsvSourceService {

    public static final String[] CSV_HEAD = new String[]{"Customer ID", "范围（Scope）", "VIP LEVEL"};

    @Override
    public boolean support(String activityType) {
        return Arrays.asList(ActivityConstant.ActivityTypeEnum.HIERARCHY.name(),
                ActivityConstant.ActivityTypeEnum.INVITE.name(),
                ActivityConstant.ActivityTypeEnum.NORMAL.name()).contains(activityType);
    }

    @Override
    public String[] header() {
        return CSV_HEAD;
    }

    @Override
    public ActivityBatchRewardRoster parseOssObject(ActivityEntity activityEntity, ActivityBatch activityBatch, String[] ossObject, Map<String, Integer> map) {
        ActivityBatchRewardRoster roster = new ActivityBatchRewardRoster();

        AssertUtil.isNotBlank(ossObject[0], "customerId cannot be empty");
        AssertUtil.isNotBlank(ossObject[1], "scope cannot be empty");
        if(NumberUtils.isDigits(ossObject[0].trim())){
            roster.setCustomerId(ossObject[0].trim());
        }else{
            roster.setCustomerId(ossObject[0].trim().substring(1));
        }
        roster.setScope(ossObject[1].trim());
        if(StringUtils.isNotBlank(ossObject[2])){
            roster.setVipLevel(ossObject[2].trim());
        }

        if (map.containsKey(roster.getCustomerId())) {
            roster.setSeq(getSeq(activityEntity.getType()).getPrefix() + map.get(roster.getCustomerId()) + 1);
            map.put(roster.getCustomerId(), map.get(roster.getCustomerId()) + 1);
        } else {
            roster.setSeq(getSeq(activityEntity.getType()).getInit());
            map.put(roster.getCustomerId(), 1);
        }
        roster.setBatchId(activityBatch.getBatchId());
        roster.setStatus(ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name());
        roster.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        roster.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        return roster;
    }
}
