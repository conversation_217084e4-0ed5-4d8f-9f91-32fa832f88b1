package com.kikitrade.activity.service.business.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.mysql.model.ActivityDispatchLog;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.service.business.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class ActivitySchedulerServiceImpl implements ActivitySchedulerService {
    @Resource
    private ActivityService activityService;
    @Resource
    private ActivityCommonService activityCommonService;
    @Resource
    private ActivityDispatchLogService activityDispatchLogService;
    @Resource
    private NoticeService noticeService;

    @Override
    public boolean statusCheck() {
        boolean flag = true;
        JsonResult result = new JsonResult();
        log.info("ActivitySchedulerTasks statusCheck invoke start!");

        try {
            //Query activity
            result = activityService.findForStatsUpdate();
        } catch (Exception e) {
            log.error("ActivitySchedulerTasks statusCheck process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(e.getMessage());
        }
        log.info("ActivitySchedulerTasks statusCheck  list result....{}", JSON.toJSONString(result));
        if (result.getSuccess()) {
            try {
                //活动状态 0-新建 1-已发布 2-进行中 3-暂停 4-已结束 5-失效（删除）
                List<Activity> activityList = (List<Activity>) result.getObj();
                for (Activity activity : activityList) {
                    log.info("ActivitySchedulerTasks statusCheck currentTimeMillis:{} activity....{}", System.currentTimeMillis(), JSON.toJSONString(activity));
                    JsonResult result1 = new JsonResult();
                    Date startTime = activity.getStart_time();
                    Date endTime = activity.getEnd_time();

                    if (System.currentTimeMillis() >= startTime.getTime() && System.currentTimeMillis() <= endTime.getTime()) {
                        if (activity.getStatus() == ActivityConstant.Status.PUBLISHED.getCode()) {
                            result1 = activityService.updateStatus(activity.getId(), ActivityConstant.Status.PROCESSING.getCode());
                            log.info("ActivitySchedulerTasks statusCheck update activity id {} , status {}, execute flag {}", activity.getId(), ActivityConstant.Status.PROCESSING.getCode(), result1.getSuccess());
                        }
                    }

                    if (System.currentTimeMillis() > endTime.getTime()) {
                        if (activity.getStatus() == ActivityConstant.Status.PUBLISHED.getCode() || activity.getStatus() == ActivityConstant.Status.PROCESSING.getCode()) {
                            result1 = activityService.updateStatus(activity.getId(), ActivityConstant.Status.END.getCode());
                            log.info("ActivitySchedulerTasks statusCheck update activity id {} , status {}, execute flag {}", activity.getId(), ActivityConstant.Status.END.getCode(), result1.getSuccess());
                        }
                    }

                    if (result1.getSuccess()) {
                        result1 = activityService.findDetailById(activity.getId());
                        if (result1.getSuccess() && result1.getObj() != null) {
                            activity = (Activity) result1.getObj();
                            activityCommonService.redisSave(activity);
                            log.info("ActivitySchedulerTasks statusCheck update activity id {} , status {}, execute flag {}", activity.getId(), ActivityConstant.Status.PROCESSING.getCode(), result1.getSuccess());
                        }
                    }
                }
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            } catch (Exception e) {
                flag = false;
                log.error("activity statusCheck process failed", e);
            }
        } else {
            if (!result.getCode().equals(ActivityExceptionType.NO_DATA_FOUND.getCode())) {
                flag = false;
            }
        }

        log.info("ActivitySchedulerTasks statusCheck invoke end and execute flag is {}!", flag);
        return flag;
    }

    @Override
    public boolean failCheck() {
        boolean flag = true;
        log.info("ActivitySchedulerTasks failCheck invoke start!");
        //dispatch check
        try {
            List<ActivityDispatchLog> activityDispatchLogList = activityDispatchLogService.findAll();
            if (activityDispatchLogList != null && activityDispatchLogList.size() > 0) {
                for (ActivityDispatchLog activityDispatchLog : activityDispatchLogList) {
                    Date createTime = activityDispatchLog.getCreated();
                    if (System.currentTimeMillis() - createTime.getTime() >= 1000 * 60 * 60 * 2) {
                        noticeService.dispatchFailNotice(JSONObject.toJSONString(activityDispatchLog));
                    }
                }
            }

        } catch (Exception e) {
            flag = false;
            log.error("activity dispatch log list check process failed", e);
        }

        log.info("ActivitySchedulerTasks failCheck invoke end and execute flag is {}!", flag);
        return flag;
    }


}
