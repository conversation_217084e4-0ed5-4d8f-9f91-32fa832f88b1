package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.tablestore.model.ActivityJoinItem;
import org.apache.commons.lang3.tuple.Pair;

public interface ActivityJoinItemService {

    /**
     * 添加参加活动记录
     * @param userName
     * @param businessCode
     * @param businessType
     * @return
     */
    boolean insert(String userName, String businessCode, String businessType);

    /**
     * 完成活动
     * @param userName
     * @param businessCode
     * @return
     */
    boolean done(String userName, String businessCode);

    String getActivityCode();

    Pair<Boolean, String> isContainsActivityCode(String businessCode);

    String reGenerateCode(String businessCode);

    ActivityJoinItem queryByUserName(String userName);
}
