package com.kikitrade.activity.service.rpc.osp;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.config.OspProperties;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.TreeMap;

import static com.kikitrade.activity.service.reward.impl.RewardBadgeTccServiceImpl.generateSignature;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/5 17:18
 */
@Data
@Slf4j
public class OspCallBackRequest {

    private static final String APP_KEY = "app_key";

    private OspProperties ospProperties;

    private String cid;
    private String saasId;
    private String taskId;
    private String taskType;
    private String taskCode;
    private String extra;

    public OspCallBackRequest() {
    }

    public OspCallBackRequest(OspProperties ospProperties) {
        this.ospProperties = ospProperties;
    }

    public OspCallBackRequest build(String saasId, String cid, String taskId, String taskType, String extra, String taskCode) {
        this.saasId = saasId;
        this.cid = cid;
        this.taskId = taskId;
        this.taskType = taskType;
        this.extra = extra;
        this.taskCode = taskCode;
        return this;
    }


    public Boolean execute() {
        try {
            return ospVerify(this.cid, this.saasId, this.taskId, this.taskType, this.extra, this.taskCode);
        } catch (Exception e) {
            log.error("[OspCallBackRequest] execute exception, cid:{}, error:", cid, e);
            return Boolean.FALSE;
        }
    }

    private Boolean ospVerify(String cid, String saasId, String taskId, String taskType, String extra, String taskCode) {
        try {
            SaasConfig config = SaasConfigLoader.getConfig(saasId);
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("Content-Type", "application/json")
                    .add(APP_KEY, "quests");
            MediaType mediaType = MediaType.parse("application/json");
            TreeMap<String, Object> param = new TreeMap<>();
            String ospNftCallback = config.getOspNftCallback();
            String url;

            if (ActivityConstant.OspNftCallbackTypeEnum.OWNER_NFT_CALLBACK.name().equals(ospNftCallback)) {
                headerBuilder.add("OS-App-Id", config.getOspAppId())
                        .add("OS-Api-Key", config.getOspAppKey());
                param.put("task_code", taskCode);
                param.put("owner_id", cid);
                url = config.getApiHost() + "/v2/s2s/task/verify";
            } else {
                param.put("customer_id", cid);
                param.put("task_id", taskId);
                param.put("extra", extra);
                param.put("task_type", taskType);
                param.put("signature", generateSignature(param, config.getAppKey()));
                url = config.getApiHost() + "/v2/tasks/verify";
            }

            Request request = new Request.Builder()
                    .url(url)
                    .method(HttpMethod.POST.name(), RequestBody.create(mediaType, JSON.toJSONString(param)))
                    .headers(headerBuilder.build())
                    .build();

            log.info("[ospVerify] request = {}, param = {}", request, JSON.toJSONString(param));
            try (Response response = HttpPoolUtil.getHttpClient().newCall(request).execute()) {
                log.info("[ospVerify] response = {}", response);
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    log.info("[OspCallBackRequest] ospVerify responseBody:{}", responseBody);
                    if (ActivityConstant.OspNftCallbackTypeEnum.OWNER_NFT_CALLBACK.name().equals(ospNftCallback)) {
                        return "0".equals(JSON.parseObject(responseBody).getString("code"));
                    } else {
                        return StringUtils.isNotBlank(responseBody) && Boolean.parseBoolean(responseBody);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[osp] ospVerify Exception:{}, error:", this.cid, e);
        }
        return false;
    }
}
