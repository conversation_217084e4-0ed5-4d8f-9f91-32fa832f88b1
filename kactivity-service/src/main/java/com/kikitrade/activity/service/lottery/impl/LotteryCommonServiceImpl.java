package com.kikitrade.activity.service.lottery.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCumulateItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LotteryConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCumulateItem;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.dal.tablestore.model.LotteryAward;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.ActivityCumulateConfig;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.lottery.LotteryCommonService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;

import static com.kikitrade.activity.api.exception.ActivityExceptionType.LOTTERY_PRODUCT_LIMIT;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/21 14:20
 */
@Service
@Slf4j
public class LotteryCommonServiceImpl implements LotteryCommonService {

    @Resource
    private LotteryConfigBuilder lotteryConfigBuilder;
    @Resource
    private ActivityLotteryItemBuilder activityLotteryItemBuilder;
    @Resource
    private LotteryServiceRoute lotteryServiceRoute;
    @Resource
    private ActivityCumulateItemBuilder activityCumulateItemBuilder;

    @Override
    public LotteryResponse lottery(String customerId, String code, String saasId) throws Exception {
        //抽奖配置
        LotteryConfig lotteryConfig = lotteryConfigBuilder.findByCode(code);
        //检测抽奖次数
        if(isAboveLotteryCountLimit(customerId, lotteryConfig)){
            //超过抽奖次数上限了
            throw new ActivityException(LOTTERY_PRODUCT_LIMIT);
        }
        //抽取奖品
        LotteryAward lotteryAward = lotteryServiceRoute.getService(lotteryConfig.getType()).draw(customerId, lotteryConfig);
        //保存抽奖记录
        ActivityLotteryItem lotteryItem = lotteryServiceRoute.getService(lotteryConfig.getType()).saveLotteryItem(customerId, code, saasId, lotteryAward, lotteryConfig);
        //其他后续操作
        return lotteryServiceRoute.getService(lotteryConfig.getType()).postAction(lotteryItem);
    }

    @Override
    public List<ActivityLotteryItem> lotteryItem(String customerId, String code, String saasId) {
        return null;
    }

    private Boolean isAboveLotteryCountLimit(String customerId, LotteryConfig lotteryConfig){
        long count = lotteryCount(customerId, lotteryConfig);
        return count >= lotteryConfig.getLimitTimes();
    }

    @Override
    public long lotteryCount(String customerId, String code, String saasId) {
        LotteryConfig config = lotteryConfigBuilder.findByCode(code);
        return lotteryCount(customerId, config);
    }

    @Override
    public boolean isAboveLotteryCountLimit(String customerId, String code, String saasId) {
        LotteryConfig config = lotteryConfigBuilder.findByCode(code);
        long count = lotteryCount(customerId, config);
        return count >= config.getLimitTimes();
    }

    /**
     * 历史全部中奖次数
     *
     * @param customerId
     * @param code
     * @param saasId
     * @return
     */
    @Override
    public long lotteryCountCumulateAll(String customerId, String code, String saasId) {
        return activityLotteryItemBuilder.cumulateCountByCustomer(customerId, code);

    }

    public String getLimitStartTime(LotteryConfig lotteryConfig){
        return switch (lotteryConfig.getLimitUnit()){
            case "weekly" -> TimeUtil.getWeekStart(new Date(), TimeFormat.YYYYMMDD_000000_PATTERN);
            default -> TimeUtil.getUtcTime(new Date(), TimeFormat.YYYYMMDD_000000_PATTERN);
        };
    }

    private long lotteryCount(String customerId, LotteryConfig lotteryConfig){
        String startTime = switch (lotteryConfig.getLimitUnit()){
            case "weekly" -> TimeUtil.getWeekStart(new Date(), TimeFormat.YYYYMMDD_000000_PATTERN);
            default -> TimeUtil.getUtcTime(new Date(), TimeFormat.YYYYMMDD_000000_PATTERN);
        };
        return activityLotteryItemBuilder.countByCustomer(customerId, lotteryConfig.getCode(), TimeUtil.parse(startTime).toInstant().getEpochSecond(), OffsetDateTime.now().toEpochSecond());
    }


    @Override
    public ActivityCumulateItem getActivityCumulate(String customerId, String code, String saasId, long count) {
        log.info("LotteryCommonServiceImpl getActivityCumulate customerId:{}, code:{}, saasId:{}", customerId, code,
                saasId);
        LotteryConfig config = lotteryConfigBuilder.findByCode(code);

        if (BooleanUtils.isFalse(config.getIsCumulate()) || StringUtils.isBlank(config.getCumulateInfo())) {
            return null;
        }

        List<ActivityCumulateConfig> activityCumulateConfigs = JSON.parseArray(config.getCumulateInfo(),
                ActivityCumulateConfig.class);
        ActivityCumulateConfig cumulateSatisfied = activityCumulateConfigs.stream()
                .filter(item -> item.getCumulateTotal() == count).findAny().orElse(null);
        log.info("LotteryCommonServiceImpl getActivityCumulate cumulateCount:{}, customerId:{}, cumulateSatisfied:{}",
                count, customerId, JSON.toJSONString(cumulateSatisfied));
        if (cumulateSatisfied != null) {
            ActivityCumulateItem activityCumulateItem = new ActivityCumulateItem();
            activityCumulateItem.setId(IdUtil.objectId());
            activityCumulateItem.setSaasId(saasId);
            activityCumulateItem.setBusinessId(config.getId());
            activityCumulateItem.setCustomerId(customerId);
            activityCumulateItem.setStatus(ActivityConstant.LotteryStatus.APPENDING.name());
            activityCumulateItem.setCreated(System.currentTimeMillis());
            activityCumulateItem.setCode(code);
            activityCumulateItem.setRewardCurrency(cumulateSatisfied.getRewardCurrency());
            activityCumulateItem.setRewardAmount(new BigDecimal(cumulateSatisfied.getRewardAmount()));
            activityCumulateItemBuilder.insert(activityCumulateItem);
            return activityCumulateItem;
        }
        return null;
    }
}
