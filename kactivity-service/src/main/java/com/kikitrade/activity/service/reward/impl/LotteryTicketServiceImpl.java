package com.kikitrade.activity.service.reward.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kikitrade.activity.api.model.request.reward.ExchangeTicketsRequest;
import com.kikitrade.activity.api.model.response.reward.ExchangeTicketsResponse;
import com.kikitrade.activity.dal.tablestore.builder.PrizePoolBuilder;
import com.kikitrade.activity.dal.tablestore.builder.UserLotteryProfileBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizePool;
import com.kikitrade.activity.dal.tablestore.model.UserLotteryProfile;
import com.kikitrade.activity.service.reward.LotteryTicketService;
import com.kikitrade.activity.service.reward.preference.UserPreferenceService;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 抽奖券管理服务实现
 * 基于UserLotteryProfile存储抽奖券信息，支持两阶段抽奖系统
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class LotteryTicketServiceImpl implements LotteryTicketService {
    
    @Resource
    private UserLotteryProfileBuilder userLotteryProfileBuilder;

    @Resource
    private UserPreferenceService userPreferenceService;

    @Resource
    private PrizePoolBuilder prizePoolBuilder;

    @DubboReference
    private RemoteAssetService remoteAssetService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 抽奖券类型常量
    private static final String TICKET_TYPE_NORMAL = "NORMAL";
    private static final String TICKET_TYPE_PREMIUM = "PREMIUM";
    private static final String TICKET_TYPE_SPECIAL = "SPECIAL";
    
    // 资产类型常量
    private static final String ASSET_TYPE_POINTS = "POINTS";
    private static final String ASSET_TYPE_COINS = "COINS";
    private static final String ASSET_TYPE_DIAMONDS = "DIAMONDS";
    private static final String ASSET_TYPE_SHELL = "SHELL"; // 兼容
    
    @Override
    public ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request) {
        log.info("用户兑换抽奖券: userId={}, assetType={}, assetAmount={}, ticketType={}, ticketCount={}", 
                request.getUserId(), request.getAssetType(), request.getAssetAmount(), 
                request.getTicketType(), request.getTicketCount());
        
        try {
            // 1. 参数验证
            if (!validateExchangeRequest(request)) {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("INVALID_PARAMS")
                        .message("请求参数无效")
                        .build();
            }
            
            // 2. 兼容性处理
            AssetType assetType = StringUtils.hasText(request.getAssetType()) ? AssetType.valueOf(request.getAssetType()) : AssetType.POINT;
            Long assetAmount = request.getAssetAmount() != null ? request.getAssetAmount() : 
                              (request.getAmount() != null ? request.getAmount().longValue() : 0L);
            String ticketType = StringUtils.hasText(request.getTicketType()) ? request.getTicketType() : TICKET_TYPE_NORMAL;
            Integer ticketCount = request.getTicketCount() != null ? request.getTicketCount() : 1;
            
            // 3. 验证用户资产
            if (!validateUserAsset(request.getUserId(), request.getSaasId(), assetType, assetAmount)) {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("INSUFFICIENT_ASSET")
                        .message("资产不足")
                        .build();
            }
            
            // 4. 计算兑换汇率和实际消耗
            Integer actualTicketCount = calculateTicketCount(request.getPrizePoolCode(), request.getSaasId(), assetType.name(), assetAmount);
            if (actualTicketCount == null || actualTicketCount <= 0) {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("INVALID_EXCHANGE_RULE")
                        .message("无效的兑换规则或资产类型")
                        .build();
            }

            // 5. 执行兑换操作
            boolean success = performExchange(request.getUserId(), request.getSaasId(),
                                            assetType, actualAssetConsumed, ticketType, actualTicketCount);
            
            if (success) {
                // 6. 查询剩余资产

                return ExchangeTicketsResponse.builder()
                        .success(true)
                        .ticketsObtained(actualTicketCount) // 兼容字段
                        .ticketType(ticketType)
                        .actualTicketCount(actualTicketCount)
                        .assetType(assetType.name())
                        .exchangeOrderId(generateOrderId())
                        .message("兑换成功")
                        .build();
            } else {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("EXCHANGE_FAILED")
                        .message("兑换失败")
                        .build();
            }
            
        } catch (Exception e) {
            log.error("兑换抽奖券异常: userId={}", request.getUserId(), e);
            return ExchangeTicketsResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }
    
    @Override
    public Integer getUserTicketBalance(String userId, String saasId, String ticketType) {
        log.debug("查询用户抽奖券余额: userId={}, ticketType={}", userId, ticketType);
        
        try {
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            String balanceStr = userPreferenceService.getUserPreference(userId, saasId, preferenceKey);
            return balanceStr != null ? Integer.parseInt(balanceStr) : 0;
        } catch (Exception e) {
            log.error("查询抽奖券余额异常: userId={}, ticketType={}", userId, ticketType, e);
            return 0;
        }
    }
    
    @Override
    public boolean consumeTickets(String userId, String saasId, String ticketType, Integer count) {
        log.info("消费抽奖券: userId={}, ticketType={}, count={}", userId, ticketType, count);
        
        try {
            Integer currentBalance = getUserTicketBalance(userId, saasId, ticketType);
            if (currentBalance < count) {
                log.warn("抽奖券余额不足: userId={}, current={}, required={}", userId, currentBalance, count);
                return false;
            }
            
            Integer newBalance = currentBalance - count;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("消费抽奖券异常: userId={}, ticketType={}, count={}", userId, ticketType, count, e);
            return false;
        }
    }
    
    @Override
    public boolean refundTickets(String userId, String saasId, String ticketType, Integer count) {
        log.info("退还抽奖券: userId={}, ticketType={}, count={}", userId, ticketType, count);
        
        try {
            Integer currentBalance = getUserTicketBalance(userId, saasId, ticketType);
            Integer newBalance = currentBalance + count;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("退还抽奖券异常: userId={}, ticketType={}, count={}", userId, ticketType, count, e);
            return false;
        }
    }
    
    @Override
    public String getExchangeRate(String assetType) {
        // 兼容旧接口，返回默认汇率
        return "100:1";
    }
    
    @Override
    public boolean validateUserAsset(String userId, String saasId, AssetType assetType, Long assetAmount) {
        try {
            remoteAssetService.asset(saasId, userId, assetType, AssetCategory.NORMAL);
            return userAssetBalance >= assetAmount;
        } catch (Exception e) {
            log.error("验证用户资产异常: userId={}, assetType={}", userId, assetType, e);
            return false;
        }
    }
    
    /**
     * 验证兑换请求参数
     */
    private boolean validateExchangeRequest(ExchangeTicketsRequest request) {
        if (request == null || !StringUtils.hasText(request.getUserId()) || !StringUtils.hasText(request.getSaasId())) {
            return false;
        }
        
        // 兼容性检查
        boolean hasAssetAmount = request.getAssetAmount() != null && request.getAssetAmount() > 0;
        boolean hasAmount = request.getAmount() != null && request.getAmount() > 0;
        
        return hasAssetAmount || hasAmount;
    }
    
    /**
     * 执行兑换操作
     */
    private boolean performExchange(String userId, String saasId, AssetType assetType, Long assetAmount,
                                  String ticketType, Integer ticketCount) {
        try {
            //TODO 1. 扣除用户资产

            //TODO 2. 增加抽奖券
            Integer currentTickets = getUserTicketBalance(userId, saasId, ticketType);
            Integer newTickets = currentTickets + ticketCount;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            boolean ticketsAdded = userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newTickets.toString());

            if (!ticketsAdded) {
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("执行兑换操作异常: userId={}", userId, e);
            return false;
        }
    }
    
    /**
     * 从奖池配置中计算抽奖券数量
     */
    private Integer calculateTicketCount(String prizePoolCode, String saasId, String assetType, Long assetAmount) {
        try {
            // 1. 获取奖池配置
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(prizePoolCode, saasId);
            if (prizePool == null) {
                log.warn("奖池不存在: prizePoolCode={}, saasId={}", prizePoolCode, saasId);
                return null;
            }

            // 2. 解析兑换规则JSON
            List<ExchangeRule> exchangeRules = parseExchangeRules(prizePool.getExchangeRules());
            if (exchangeRules == null || exchangeRules.isEmpty()) {
                log.warn("奖池兑换规则为空: prizePoolCode={}", prizePoolCode);
                return null;
            }

            // 3. 查找匹配的资产类型
            for (ExchangeRule rule : exchangeRules) {
                if (assetType.equals(rule.getAssetType())) {
                    // 计算可兑换的抽奖券数量：资产数量 / 单张抽奖券成本
                    return (int) (assetAmount / rule.getCost());
                }
            }

            log.warn("未找到匹配的兑换规则: assetType={}, prizePoolCode={}", assetType, prizePoolCode);
            return null;

        } catch (Exception e) {
            log.error("计算抽奖券数量异常: prizePoolCode={}, assetType={}, assetAmount={}",
                     prizePoolCode, assetType, assetAmount, e);
            return null;
        }
    }

    /**
     * 计算实际消耗的资产数量
     */
    private Long calculateActualAssetConsumption(String prizePoolCode, String saasId, String assetType, Integer ticketCount) {
        try {
            // 1. 获取奖池配置
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(prizePoolCode, saasId);
            if (prizePool == null) {
                return 0L;
            }

            // 2. 解析兑换规则JSON
            List<ExchangeRule> exchangeRules = parseExchangeRules(prizePool.getExchangeRules());
            if (exchangeRules == null || exchangeRules.isEmpty()) {
                return 0L;
            }

            // 3. 查找匹配的资产类型
            for (ExchangeRule rule : exchangeRules) {
                if (assetType.equals(rule.getAssetType())) {
                    // 计算实际消耗：抽奖券数量 * 单张抽奖券成本
                    return (long) (ticketCount * rule.getCost());
                }
            }

            return 0L;

        } catch (Exception e) {
            log.error("计算实际资产消耗异常: prizePoolCode={}, assetType={}, ticketCount={}",
                     prizePoolCode, assetType, ticketCount, e);
            return 0L;
        }
    }

    /**
     * 从奖池获取兑换汇率字符串
     */
    private String getExchangeRateFromPool(String prizePoolCode, String saasId, String assetType) {
        try {
            // 1. 获取奖池配置
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(prizePoolCode, saasId);
            if (prizePool == null) {
                return "100:1"; // 默认汇率
            }

            // 2. 解析兑换规则JSON
            List<ExchangeRule> exchangeRules = parseExchangeRules(prizePool.getExchangeRules());
            if (exchangeRules == null || exchangeRules.isEmpty()) {
                return "100:1"; // 默认汇率
            }

            // 3. 查找匹配的资产类型
            for (ExchangeRule rule : exchangeRules) {
                if (assetType.equals(rule.getAssetType())) {
                    return rule.getCost() + ":1"; // 返回格式：成本:1张抽奖券
                }
            }

            return "100:1"; // 默认汇率

        } catch (Exception e) {
            log.error("获取兑换汇率异常: prizePoolCode={}, assetType={}", prizePoolCode, assetType, e);
            return "100:1"; // 默认汇率
        }
    }
    
    /**
     * 获取用户资产余额
     */
    private Long getUserAssetBalance(String userId, String saasId, String assetType) {
        try {
            String preferenceKey = "ASSET_BALANCE_" + assetType;
            String balanceStr = userPreferenceService.getUserPreference(userId, saasId, preferenceKey);
            return balanceStr != null ? Long.parseLong(balanceStr) : 10000L; // 默认给用户10000资产用于测试
        } catch (Exception e) {
            log.error("获取用户资产余额异常: userId={}, assetType={}", userId, assetType, e);
            return 0L;
        }
    }
    
    /**
     * 扣除用户资产
     */
    private boolean deductUserAsset(String userId, String saasId, String assetType, Long amount) {
        try {
            Long currentBalance = getUserAssetBalance(userId, saasId, assetType);
            if (currentBalance < amount) {
                return false;
            }
            
            Long newBalance = currentBalance - amount;
            String preferenceKey = "ASSET_BALANCE_" + assetType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("扣除用户资产异常: userId={}, assetType={}, amount={}", userId, assetType, amount, e);
            return false;
        }
    }
    
    /**
     * 退还用户资产
     */
    private boolean refundUserAsset(String userId, String saasId, String assetType, Long amount) {
        try {
            Long currentBalance = getUserAssetBalance(userId, saasId, assetType);
            Long newBalance = currentBalance + amount;
            String preferenceKey = "ASSET_BALANCE_" + assetType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("退还用户资产异常: userId={}, assetType={}, amount={}", userId, assetType, amount, e);
            return false;
        }
    }
    

    
    /**
     * 解析兑换规则JSON
     */
    private List<ExchangeRule> parseExchangeRules(String exchangeRulesJson) {
        if (!StringUtils.hasText(exchangeRulesJson)) {
            return null;
        }

        try {
            return objectMapper.readValue(exchangeRulesJson, new TypeReference<List<ExchangeRule>>() {});
        } catch (Exception e) {
            log.error("解析兑换规则JSON失败: json={}", exchangeRulesJson, e);
            return null;
        }
    }

    /**
     * 生成兑换订单ID
     */
    private String generateOrderId() {
        return "EX_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 兑换规则内部类
     */
    private static class ExchangeRule {
        private String assetType;
        private Integer cost;

        public String getAssetType() {
            return assetType;
        }

        public void setAssetType(String assetType) {
            this.assetType = assetType;
        }

        public Integer getCost() {
            return cost;
        }

        public void setCost(Integer cost) {
            this.cost = cost;
        }
    }
}
