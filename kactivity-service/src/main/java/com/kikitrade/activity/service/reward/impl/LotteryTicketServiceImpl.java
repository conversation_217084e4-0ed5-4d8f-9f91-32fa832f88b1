package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.api.model.request.reward.ExchangeTicketsRequest;
import com.kikitrade.activity.api.model.response.reward.ExchangeTicketsResponse;
import com.kikitrade.activity.dal.tablestore.builder.UserLotteryProfileBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserLotteryProfile;
import com.kikitrade.activity.service.reward.LotteryTicketService;
import com.kikitrade.activity.service.reward.preference.UserPreferenceService;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 抽奖券管理服务实现
 * 基于UserLotteryProfile存储抽奖券信息，支持两阶段抽奖系统
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class LotteryTicketServiceImpl implements LotteryTicketService {
    
    @Resource
    private UserLotteryProfileBuilder userLotteryProfileBuilder;
    
    @Resource
    private UserPreferenceService userPreferenceService;

    @DubboReference
    private RemoteAssetService remoteAssetService;
    
    // 抽奖券类型常量
    private static final String TICKET_TYPE_NORMAL = "NORMAL";
    private static final String TICKET_TYPE_PREMIUM = "PREMIUM";
    private static final String TICKET_TYPE_SPECIAL = "SPECIAL";
    
    // 资产类型常量
    private static final String ASSET_TYPE_POINTS = "POINTS";
    private static final String ASSET_TYPE_COINS = "COINS";
    private static final String ASSET_TYPE_DIAMONDS = "DIAMONDS";
    private static final String ASSET_TYPE_SHELL = "SHELL"; // 兼容
    
    @Override
    public ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request) {
        log.info("用户兑换抽奖券: userId={}, assetType={}, assetAmount={}, ticketType={}, ticketCount={}", 
                request.getUserId(), request.getAssetType(), request.getAssetAmount(), 
                request.getTicketType(), request.getTicketCount());
        
        try {
            // 1. 参数验证
            if (!validateExchangeRequest(request)) {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("INVALID_PARAMS")
                        .message("请求参数无效")
                        .build();
            }
            
            // 2. 兼容性处理
            AssetType assetType = StringUtils.hasText(request.getAssetType()) ? AssetType.valueOf(request.getAssetType()) : AssetType.POINT;
            Long assetAmount = request.getAssetAmount() != null ? request.getAssetAmount() : 
                              (request.getAmount() != null ? request.getAmount().longValue() : 0L);
            String ticketType = StringUtils.hasText(request.getTicketType()) ? request.getTicketType() : TICKET_TYPE_NORMAL;
            Integer ticketCount = request.getTicketCount() != null ? request.getTicketCount() : 1;
            
            // 3. 验证用户资产
            if (!validateUserAsset(request.getUserId(), request.getSaasId(), assetType, assetAmount)) {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("INSUFFICIENT_ASSET")
                        .message("资产不足")
                        .build();
            }
            
            // 4. 计算兑换汇率和实际消耗
            String exchangeRate = getExchangeRate(assetType, ticketType);
            Long actualAssetConsumed = calculateAssetConsumption(assetAmount, ticketCount, exchangeRate);
            
            // 5. 执行兑换操作
            boolean success = performExchange(request.getUserId(), request.getSaasId(), 
                                            assetType, actualAssetConsumed, ticketType, ticketCount);
            
            if (success) {
                // 6. 查询剩余资产
                Long remainingAsset = getUserAssetBalance(request.getUserId(), request.getSaasId(), assetType);
                
                return ExchangeTicketsResponse.builder()
                        .success(true)
                        .ticketsObtained(ticketCount) // 兼容字段
                        .ticketType(ticketType)
                        .actualTicketCount(ticketCount)
                        .assetType(assetType)
                        .assetConsumed(actualAssetConsumed)
                        .remainingAsset(remainingAsset)
                        .exchangeRate(exchangeRate)
                        .exchangeOrderId(generateOrderId())
                        .message("兑换成功")
                        .build();
            } else {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("EXCHANGE_FAILED")
                        .message("兑换失败")
                        .build();
            }
            
        } catch (Exception e) {
            log.error("兑换抽奖券异常: userId={}", request.getUserId(), e);
            return ExchangeTicketsResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }
    
    @Override
    public Integer getUserTicketBalance(String userId, String saasId, String ticketType) {
        log.debug("查询用户抽奖券余额: userId={}, ticketType={}", userId, ticketType);
        
        try {
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            String balanceStr = userPreferenceService.getUserPreference(userId, saasId, preferenceKey);
            return balanceStr != null ? Integer.parseInt(balanceStr) : 0;
        } catch (Exception e) {
            log.error("查询抽奖券余额异常: userId={}, ticketType={}", userId, ticketType, e);
            return 0;
        }
    }
    
    @Override
    public boolean consumeTickets(String userId, String saasId, String ticketType, Integer count) {
        log.info("消费抽奖券: userId={}, ticketType={}, count={}", userId, ticketType, count);
        
        try {
            Integer currentBalance = getUserTicketBalance(userId, saasId, ticketType);
            if (currentBalance < count) {
                log.warn("抽奖券余额不足: userId={}, current={}, required={}", userId, currentBalance, count);
                return false;
            }
            
            Integer newBalance = currentBalance - count;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("消费抽奖券异常: userId={}, ticketType={}, count={}", userId, ticketType, count, e);
            return false;
        }
    }
    
    @Override
    public boolean refundTickets(String userId, String saasId, String ticketType, Integer count) {
        log.info("退还抽奖券: userId={}, ticketType={}, count={}", userId, ticketType, count);
        
        try {
            Integer currentBalance = getUserTicketBalance(userId, saasId, ticketType);
            Integer newBalance = currentBalance + count;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("退还抽奖券异常: userId={}, ticketType={}, count={}", userId, ticketType, count, e);
            return false;
        }
    }
    
    @Override
    public String getExchangeRate(String assetType, String ticketType) {
        // 示例汇率配置，实际项目中应该从配置中心或数据库获取
        Map<String, Map<String, String>> rateConfig = getExchangeRateConfig();
        
        Map<String, String> assetRates = rateConfig.get(assetType);
        if (assetRates != null) {
            return assetRates.getOrDefault(ticketType, "100:1"); // 默认100资产兑换1抽奖券
        }
        
        return "100:1";
    }
    
    @Override
    public boolean validateUserAsset(String userId, String saasId, AssetType assetType, Long assetAmount) {
        try {
            remoteAssetService.asset(saasId, userId, assetType, AssetCategory.NORMAL);
            return userAssetBalance >= assetAmount;
        } catch (Exception e) {
            log.error("验证用户资产异常: userId={}, assetType={}", userId, assetType, e);
            return false;
        }
    }
    
    /**
     * 验证兑换请求参数
     */
    private boolean validateExchangeRequest(ExchangeTicketsRequest request) {
        if (request == null || !StringUtils.hasText(request.getUserId()) || !StringUtils.hasText(request.getSaasId())) {
            return false;
        }
        
        // 兼容性检查
        boolean hasAssetAmount = request.getAssetAmount() != null && request.getAssetAmount() > 0;
        boolean hasAmount = request.getAmount() != null && request.getAmount() > 0;
        
        return hasAssetAmount || hasAmount;
    }
    
    /**
     * 执行兑换操作
     */
    private boolean performExchange(String userId, String saasId, String assetType, Long assetAmount, 
                                  String ticketType, Integer ticketCount) {
        try {
            // 1. 扣除用户资产
            boolean assetDeducted = deductUserAsset(userId, saasId, assetType, assetAmount);
            if (!assetDeducted) {
                return false;
            }
            
            // 2. 增加抽奖券
            Integer currentTickets = getUserTicketBalance(userId, saasId, ticketType);
            Integer newTickets = currentTickets + ticketCount;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            boolean ticketsAdded = userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newTickets.toString());
            
            if (!ticketsAdded) {
                // 回滚资产扣除
                refundUserAsset(userId, saasId, assetType, assetAmount);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("执行兑换操作异常: userId={}", userId, e);
            return false;
        }
    }
    
    /**
     * 计算资产消耗
     */
    private Long calculateAssetConsumption(Long requestedAmount, Integer ticketCount, String exchangeRate) {
        // 解析汇率，格式如 "100:1"
        String[] rateParts = exchangeRate.split(":");
        if (rateParts.length == 2) {
            try {
                Long assetPerTicket = Long.parseLong(rateParts[0]);
                return assetPerTicket * ticketCount;
            } catch (NumberFormatException e) {
                log.warn("解析兑换汇率失败: {}", exchangeRate);
            }
        }
        
        return requestedAmount; // 降级方案
    }
    
    /**
     * 获取用户资产余额
     */
    private Long getUserAssetBalance(String userId, String saasId, String assetType) {
        try {
            String preferenceKey = "ASSET_BALANCE_" + assetType;
            String balanceStr = userPreferenceService.getUserPreference(userId, saasId, preferenceKey);
            return balanceStr != null ? Long.parseLong(balanceStr) : 10000L; // 默认给用户10000资产用于测试
        } catch (Exception e) {
            log.error("获取用户资产余额异常: userId={}, assetType={}", userId, assetType, e);
            return 0L;
        }
    }
    
    /**
     * 扣除用户资产
     */
    private boolean deductUserAsset(String userId, String saasId, String assetType, Long amount) {
        try {
            Long currentBalance = getUserAssetBalance(userId, saasId, assetType);
            if (currentBalance < amount) {
                return false;
            }
            
            Long newBalance = currentBalance - amount;
            String preferenceKey = "ASSET_BALANCE_" + assetType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("扣除用户资产异常: userId={}, assetType={}, amount={}", userId, assetType, amount, e);
            return false;
        }
    }
    
    /**
     * 退还用户资产
     */
    private boolean refundUserAsset(String userId, String saasId, String assetType, Long amount) {
        try {
            Long currentBalance = getUserAssetBalance(userId, saasId, assetType);
            Long newBalance = currentBalance + amount;
            String preferenceKey = "ASSET_BALANCE_" + assetType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("退还用户资产异常: userId={}, assetType={}, amount={}", userId, assetType, amount, e);
            return false;
        }
    }
    
    /**
     * 获取兑换汇率配置
     */
    private Map<String, Map<String, String>> getExchangeRateConfig() {
        Map<String, Map<String, String>> config = new HashMap<>();
        
        // 积分兑换汇率
        Map<String, String> pointsRates = new HashMap<>();
        pointsRates.put(TICKET_TYPE_NORMAL, "100:1");
        pointsRates.put(TICKET_TYPE_PREMIUM, "200:1");
        pointsRates.put(TICKET_TYPE_SPECIAL, "500:1");
        config.put(ASSET_TYPE_POINTS, pointsRates);
        
        // 金币兑换汇率
        Map<String, String> coinsRates = new HashMap<>();
        coinsRates.put(TICKET_TYPE_NORMAL, "50:1");
        coinsRates.put(TICKET_TYPE_PREMIUM, "100:1");
        coinsRates.put(TICKET_TYPE_SPECIAL, "250:1");
        config.put(ASSET_TYPE_COINS, coinsRates);
        
        // 贝壳币兑换汇率（兼容）
        Map<String, String> shellRates = new HashMap<>();
        shellRates.put(TICKET_TYPE_NORMAL, "10:1");
        shellRates.put(TICKET_TYPE_PREMIUM, "20:1");
        shellRates.put(TICKET_TYPE_SPECIAL, "50:1");
        config.put(ASSET_TYPE_SHELL, shellRates);
        
        return config;
    }
    
    /**
     * 生成兑换订单ID
     */
    private String generateOrderId() {
        return "EX_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
