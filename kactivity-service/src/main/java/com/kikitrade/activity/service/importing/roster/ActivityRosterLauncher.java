package com.kikitrade.activity.service.importing.roster;

import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.CsvService;
import com.kikitrade.activity.service.importing.ActivityLauncher;
import com.kikitrade.activity.service.importing.roster.domain.ActivityBatchRewardRosterItem;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.job.ElasticJobService;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ActivityRosterLauncher extends ActivityLauncher<ActivityBatchRewardRosterItem> {

    @Resource
    private RewardImportingService rewardImportingService;
    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @Resource
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @Resource
    private CsvService<ActivityCustomReward> rewardCsvService;
    @Resource
    private ElasticJobService elasticJobService;
    @Resource
    private ActivityBatchNewService activityBatchNewService;

    @Override
    protected boolean before(LauncherParameter launcherParameter) {
        activityBatchNewService.updateBatchStatus(launcherParameter.getBatch().getBatchId(), ActivityConstant.BatchRewardStatusEnum.IMPORTING.name());
        return true;
    }

    @Override
    protected ActivityBatchRewardRosterItem reader(LauncherParameter launcherParameter) {
        ActivityBatchRewardRosterItem item = new ActivityBatchRewardRosterItem();
        RangeResult<ActivityBatchRewardRoster> page
                = activityBatchRewardRosterStoreBuilder.findForPage(launcherParameter.getBatch().getBatchId(),
                ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name(), 200, launcherParameter.getNextToken());
        if(page != null && CollectionUtils.isNotEmpty(page.list)){
            item.setActivityBatchRewardRosterList(page.list);
        }
        if(page == null || page.nextToken == null){
            item.finish();
            return item;
        }
        launcherParameter.setNextToken(page.nextToken);
        return item;
    }

    @Override
    protected void exec(ActivityBatchRewardRosterItem item, LauncherParameter launcherParameter) {
        if(CollectionUtils.isEmpty(item.getActivityBatchRewardRosterList())){
            return;
        }
        List<ActivityCustomReward> customRewards = item.getActivityBatchRewardRosterList().stream().map(record -> {
            try{
                return covertReward(record);
            }catch (Exception ex){
                log.error("ActivityRosterLauncher",ex);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        rewardImportingService.intoReward(customRewards);
    }

    @Override
    protected void after(LauncherParameter launcherParameter) {
        activityBatchStatusStoreBuilder.updateStatusAndNum(launcherParameter.getBatch().getBatchId(), ActivityConstant.ImportStatusEnum.IMPORT_SUCCESS.name(), null);
        activityBatchNewService.updateBatchStatus(launcherParameter.getBatch().getBatchId(), ActivityConstant.BatchRewardStatusEnum.UNAUDITED.name());
        elasticJobService.removeJob(elasticJobService.getJobNameForImport(launcherParameter.getBatch().getBatchId()));
        uploadOss(launcherParameter.getBatch());
    }

    private void uploadOss(ActivityBatch batch){
        ActivityRewardPageParam pageParam = new ActivityRewardPageParam();
        pageParam.setBatchId(batch.getBatchId());
        pageParam.setPageNo(0);
        pageParam.setActivityType(batch.getActivityType());
        rewardCsvService.write(String.format("%s-%s.%s",batch.getName(), batch.getBatchId(), "csv"), pageParam);
    }

    private ActivityCustomReward covertReward(ActivityBatchRewardRoster roster){
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setBatchId(roster.getBatchId());
        reward.setCustomerId(roster.getCustomerId());
        reward.setSeq(roster.getSeq());

        reward.setUserName(roster.getUserName());
        reward.setCreated(roster.getCreated());
        reward.setStatus(roster.getStatus());
        reward.setPhone(roster.getPhone());
        reward.setEmail(roster.getEmail());
        reward.setAmount(roster.getAmount());
        reward.setCurrency(roster.getCurrency());
        reward.setScope(roster.getScope());
        reward.setRewardType(roster.getRewardType());
        return reward;
    }
}
