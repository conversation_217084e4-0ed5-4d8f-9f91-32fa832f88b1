package com.kikitrade.activity.service.importing.roster.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.importing.roster.RewardImportingProcess;
import com.kikitrade.activity.service.importing.roster.RewardImportingService;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.kcustomer.common.constants.CustomerReferralConstants;
import com.kikitrade.market.client.TickerClient;
import com.kikitrade.market.common.model.TickerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RewardImportingServiceImpl implements RewardImportingService {

    LoadingCache<String, ActivityBatch> batchCache = null;
    LoadingCache<String, ActivityEntity> activityCache = null;
    Cache<String, Boolean> rewardCache = null;

    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityEntityService activityEntityService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @Autowired
    private List<RewardImportingProcess> processes;
    @Resource
    private RedisService redisService;
    @Resource
    private CustomerService customerService;
    @Resource
    private TickerClient tickerClient;
    @Resource
    private KactivityProperties kactivityProperties;

    @PostConstruct
    public void init(){
        batchCache = CacheBuilder.newBuilder().
                maximumSize(1024 * 1024 * 1024).//1M
                expireAfterAccess(1, TimeUnit.MINUTES)
                .build(new CacheLoader<String, ActivityBatch>() {
                    @Override
                    public ActivityBatch load(String s) throws Exception {
                        return activityBatchNewService.findByBatchIdFromCache(s);
                    }
                });
        activityCache= CacheBuilder.newBuilder().
                maximumSize(500 * 1024 * 1024).//500k
                expireAfterAccess(1, TimeUnit.MINUTES)
                .build(new CacheLoader<String, ActivityEntity>() {
                    @Override
                    public ActivityEntity load(String s) throws Exception {
                        return activityEntityService.findByIdFromCache(s);
                    }
                });
        rewardCache = CacheBuilder.newBuilder().
                maximumSize(500 * 1024 * 1024).//500k
                expireAfterAccess(5, TimeUnit.MINUTES)
                .build();
    }

    @Override
    public void intoReward(List<ActivityCustomReward> rewardList) {
        AtomicLong currentNum = new AtomicLong(0);

        //邀请活动共类型，补充邀请人
        List<ActivityCustomReward> activityCustomRewards = supplementRewardItem(rewardList);
        if(CollectionUtils.isNotEmpty(activityCustomRewards)){
            rewardList.addAll(activityCustomRewards);
        }

        //批量查询用户
        List<String> customIds = rewardList.parallelStream().map(ActivityCustomReward::getCustomerId).collect(Collectors.toList());
        List<CustomerCacheDTO> customerCacheDTOS = customerService.getByIds(customIds);
        Map<String, CustomerCacheDTO> customerCacheDTOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(customerCacheDTOS)){
            customerCacheDTOMap = customerCacheDTOS.stream().collect(Collectors.toMap(CustomerCacheDTO::getId, Function.identity() , (k1, k2) -> k2));
        }

        for(ActivityCustomReward reward : rewardList){
            try{
                ActivityBatch activityBatch = batchCache.getUnchecked(reward.getBatchId());
                ActivityEntity activityEntity = activityCache.getUnchecked(activityBatch.getActivityId());
                CustomerCacheDTO customerDO = customerCacheDTOMap.get(reward.getCustomerId());
                for(RewardImportingProcess process : processes){
                    process.process(activityEntity, activityBatch, reward, customerDO);
                }
                Boolean ifPresent = rewardCache.getIfPresent(reward.getBusinessId());
                if(BooleanUtils.isNotTrue(ifPresent)){
                    rewardCache.put(reward.getBusinessId(), Boolean.TRUE);
                    reward.setCost(BigDecimal.ZERO);
                    if(StringUtils.isNoneBlank(reward.getAmount()) && "Token".equalsIgnoreCase(reward.getRewardType())){
                        if(kactivityProperties.getRewardStatisticsCurrency().equals(reward.getCurrency())){
                            reward.setCost(new BigDecimal(reward.getAmount()));
                        }else{
                            TickerDTO tickerDTO = tickerClient.get(String.format("%s_%s", reward.getCurrency(), kactivityProperties.getRewardStatisticsCurrency()));
                            reward.setCost(new BigDecimal(reward.getAmount()).multiply(tickerDTO.getPrice()));
                        }
                    }
                    reward.setActivityName(activityEntity.getName());
                    reward.setBusinessType(ActivityConstant.RewardBusinessType.get(activityEntity.getType(), activityEntity.getSubType()).name());
                    boolean success = activityCustomRewardStoreBuilder.insert(reward);
                    //Inviter邀请人不再原始表，不更新原始表
                    if(success && !ActivityConstant.SideEnum.INVITER.name().equals(reward.getSide())){
                        ActivityBatchRewardRoster activityBatchRewardRoster = new ActivityBatchRewardRoster();
                        activityBatchRewardRoster.setBatchId(reward.getBatchId());
                        activityBatchRewardRoster.setCustomerId(reward.getCustomerId());
                        activityBatchRewardRoster.setSeq(reward.getSeq());
                        activityBatchRewardRoster.setStatus(ActivityConstant.ImportStatusEnum.IMPORT_SUCCESS.name());
                        activityBatchRewardRosterStoreBuilder.updateStatus(activityBatchRewardRoster);
                        //计数已导入数量
                        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_IMPORT_NUM_KEY.getPrefix(), reward.getBatchId());
                        currentNum.getAndSet(redisService.hIncrease(key, "current", 1L));
                        redisService.expire(key, 4 * 60 * 60);
                    }
                }
            }catch (Exception ex){
                log.error("插入数据异常：{}", rewardList, ex);
            }
        }
    }


    private List<ActivityCustomReward> supplementRewardItem(List<ActivityCustomReward> inviteeList){

        List<ActivityCustomReward> inviterList = new ArrayList<>();
        //被邀请人
        for(ActivityCustomReward invitee : inviteeList){

            ActivityBatch activityBatch = batchCache.getUnchecked(invitee.getBatchId());
            ActivityEntity activityEntity = activityCache.getUnchecked(activityBatch.getActivityId());

            if(!ActivityConstant.ActivityTypeEnum.INVITE.name().equalsIgnoreCase(activityEntity.getType())){
                continue;
            }
            //被邀请人
            String nickName = customerService.getMiscByCustomerId(invitee.getCustomerId()).getNickName();
            invitee.setNickName(nickName);
            invitee.setSide(ActivityConstant.SideEnum.INVITEE.name());

            String referrerId = customerService.getInviteInfo(invitee.getCustomerId());
            if(StringUtils.isBlank(referrerId) || CustomerReferralConstants.EMPTY_ID.equals(referrerId)){
                continue;
            }
            invitee.setReferId(referrerId);
            
            String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_REWARD_SEQ_KEY.getPrefix(), activityBatch.getBatchId());
            Long seq = redisService.hIncrease(key, referrerId, 1L);
            redisService.expire(key, 8 * 60 * 60);
            ActivityCustomReward inviter = new ActivityCustomReward();
            inviter.setBatchId(invitee.getBatchId());
            inviter.setCustomerId(referrerId);
            inviter.setSeq(ActivityConstant.SeqPrefix.INVITEE_SEQ.getPrefix() + seq);
            //邀请人
            inviter.setSide(ActivityConstant.SideEnum.INVITER.name());
            //被邀请人昵称
            inviter.setNickName(nickName);
            inviter.setScope(invitee.getScope());
            inviter.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            inviter.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            inviterList.add(inviter);
        }

        List<String> customerRequest = new ArrayList<>();
        List<CustomerCacheDTO> list = new ArrayList<>();
        for(int i =0; i < inviterList.size(); i++){
            customerRequest.add(inviterList.get(i).getCustomerId());
            if(i % 20 == 0 && i > 0){
                list.addAll(customerService.getByIds(customerRequest));
                customerRequest = new ArrayList<>();
            }
        }
        if(customerRequest.size() > 0){
            list.addAll(customerService.getByIds(customerRequest));
        }

        Set<String> collect = list.stream().map(CustomerCacheDTO::getId).collect(Collectors.toSet());

        List<ActivityCustomReward> result = new ArrayList<>();
        for(int i =0; i < inviterList.size(); i++){
            if(collect.contains(inviterList.get(i).getCustomerId())){
                result.add(inviterList.get(i));
            }
        }

        return result;
    }
}
