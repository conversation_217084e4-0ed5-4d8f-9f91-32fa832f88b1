package com.kikitrade.activity.service.business;

import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityEventMassage;
import com.kikitrade.activity.dal.mysql.model.SchedLog;

import java.util.Date;

public interface ActivityBatchService {

    void jobDispatch(Integer activity_id, Integer execute_type, Integer status, Integer limit, Date deadline, String notifyUrl);

    void manualReward(Integer activity_id, Integer execute_type, Integer status, Date deadline, Integer limit, String notifyUrl);

    void timedReward(String buisnessIdList);

    void customerFeeExtract(SchedLog schedLog, String tran_date, int limit, ActivityConstant.RuleEvent event);

    void customerDiscountExtract(SchedLog schedLog, String discountDate, int limit, ActivityConstant.RuleEvent event);

    void customerInterestExtract(SchedLog schedLog, String transDate, int limit, ActivityConstant.RuleEvent event);

    void dataPrepare(ActivityEventMassage activityEventMassage);


}
