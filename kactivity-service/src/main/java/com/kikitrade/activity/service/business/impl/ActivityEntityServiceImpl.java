package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.dao.ActivityEntityDao;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.facade.award.ActivityDTO;
import com.kikitrade.activity.facade.award.ActivityTypeEnum;
import com.kikitrade.activity.facade.award.BatchFrequency;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.autoreward.domain.Condition;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.common.AssertUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.model.RewardRule;
import com.kikitrade.activity.service.task.ActivityTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityEntityServiceImpl implements ActivityEntityService {

    @Resource
    private ActivityEntityDao activityEntityDao;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private RedisService redisService;
    @Resource
    @Lazy
    private ActivityTaskService activityTaskService;

    @Override
    public Result<String> save(ActivityDTO activityDTO, ActivityConstant.ActivitySourceEnum source, String activityId, String batchId) {
        valid(activityDTO);
        ActivityEntity activityEntity;
        if(StringUtils.isNotBlank(activityDTO.getTaskId())){
            ActivityTaskConfig task = activityTaskService.findTaskById(activityDTO.getTaskId());
            activityEntity = activityDtoToEntity(activityDTO, source, task.getEndTime());
        } else {
            activityEntity = activityDtoToEntity(activityDTO, source, StringUtils.isNotBlank(activityDTO.getEndTime()) ? TimeUtil.parse(activityDTO.getEndTime()) : null);
        }
        int result = activityEntityDao.insert(activityEntity);
        activityId = activityEntity.getId();
        return result > 0 ? new Result<>(true, "success", activityId) : new Result<>(false, "save fail");
    }

    public Result<String> updateCheckType(ActivityDTO activityDTO){
        valid(activityDTO);
        ActivityEntity oldActivityEntity = activityEntityDao.selectByPrimaryKey(activityDTO.getId());
        if (!StringUtils.equals(activityDTO.getType().name(), oldActivityEntity.getType())) {
            if(activityBatchNewService.countByActivity(activityDTO.getId()) > 0){
                return new Result<>(false, "update fail, there are batches under the activity");
            }
        }
        ActivityEntity activityEntity;
        if(StringUtils.isNotBlank(activityDTO.getTaskId())){
            ActivityTaskConfig task = activityTaskService.findTaskById(activityDTO.getTaskId());
            activityEntity = activityDtoToEntity(activityDTO, ActivityConstant.ActivitySourceEnum.OPERATE, task.getEndTime());
        } else {
            activityEntity = activityDtoToEntity(activityDTO, ActivityConstant.ActivitySourceEnum.OPERATE, StringUtils.isNotBlank(activityDTO.getEndTime()) ? TimeUtil.parse(activityDTO.getEndTime()) : null);
        }
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_VALUE_KEY.getPrefix(), activityDTO.getId());
        redisService.del(key);
        int result = activityEntityDao.updateByPrimaryKey(activityEntity);
        redisService.del(key);
        return result > 0 ? new Result<>(true, "success", activityEntity.getId()) : new Result<>(false, "update fail");
    }

    /**
     * 修改活动
     *
     * @param activityEntity
     * @return
     */
    @Override
    public Result<String> update(ActivityEntity activityEntity) {
        if(activityEntity.getId() == null){
            return new Result<>(false, "id must be not empty");
        }
        activityEntity.setModified(TimeUtil.parse(TimeUtil.getUtcTime(new Date())));
        int result = activityEntityDao.updateByPrimaryKeySelective(activityEntity);
        return result > 0 ? new Result<>(true, "update success") : new Result<>(false, "update file");
    }

    /**
     * 修改活动
     *
     * @param activityEntity
     * @return
     */
    @Override
    public Result<String> updateNextCreateTime(ActivityEntity activityEntity) {
        if(activityEntity.getId() == null){
            return new Result<>(false, "id must be not empty");
        }
        activityEntity.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        int result = activityEntityDao.updateNextCreateTime(activityEntity);
        return result > 0 ? new Result<>(true, "update success") : new Result<>(false, "update file");
    }

    @Override
    public List<ActivityEntity> findByAutoCreateBatch(Date date) {
        return activityEntityDao.findByAutoCreateBatch(date);
    }

    @Override
    public ActivityEntity findById(String activityId) {
        return activityEntityDao.selectByPrimaryKey(activityId);
    }

    @Override
    public ActivityEntity findByIdFromCache(String activityId) {
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_VALUE_KEY.getPrefix(),activityId);
        String activityCache = redisService.get(key);
        if(StringUtils.isNotBlank(activityCache)){
            if("-1".equals(activityCache)){
                return null;
            }
            return JSON.parseObject(activityCache, ActivityEntity.class);
        }
        ActivityEntity activityEntity = findById(activityId);
        if(activityEntity != null){
            redisService.save(key, JSON.toJSONString(activityEntity));
            return activityEntity;
        }
        redisService.save(key, "-1");
        return null;
    }

    private void valid(ActivityDTO activityDTO){
        try{
            AssertUtil.isNotBlank(activityDTO.getActivityName(), "Activity Name cannot be empty");
            AssertUtil.isNotBlank(activityDTO.getStartTime(), "Starting Time cannot be empty");
            AssertUtil.isNotNull(activityDTO.getType(), "Activity Type cannot be null");
            AssertUtil.isNotNull(activityDTO.getStatus(), "Status cannot be null");
            if (activityDTO.getType() == ActivityTypeEnum.INVITE) {
                AssertUtil.isNotNull(activityDTO.getSubType(), "SubType cannot be null when ");
            }
            if(activityDTO.getAutoCreateBatch()){
                AssertUtil.isNotNull(activityDTO.getBatchFrequency(), "batch frequency cannot be null if auto create batch is true");
            }
        }catch (IllegalArgumentException ex){
            log.error("activityDto check fail:{}", JSON.toJSONString(activityDTO), ex);
            throw ex;
        }
    }

    private ActivityEntity activityDtoToEntity(ActivityDTO dto, ActivityConstant.ActivitySourceEnum source, Date endTime) {
        log.info("activityDtoToEntity dto :{},{}", dto, endTime);
        ActivityEntity build = new ActivityEntity();
        ActivityEntity entity;
        if (StringUtils.isBlank(dto.getId()) || (entity = this.findById(dto.getId())) == null) {
            build.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            build.setSaasId(kactivityProperties.getSaasId());
            build.setName(dto.getActivityName());
            build.setSource(source == null ? ActivityConstant.ActivitySourceEnum.OPERATE.name() : source.name());
            if (dto.getAutoCreateBatch()) {
                build.setCycle(dto.getBatchFrequency().name());
                if(StringUtils.isNotBlank(dto.getTaskId())){
                    ActivityTaskConfig task = activityTaskService.findTaskById(dto.getTaskId());
                    build.setNextCreateTime(getNextCreateTime(dto.getStatus().name(), task.getStatus(), TimeUtil.parse(dto.getStartTime()), dto.getBatchFrequency().name(), endTime, null));
                }else{
                    build.setNextCreateTime(getNextCreateTime(dto.getStatus().name(), null, TimeUtil.parse(dto.getStartTime()), dto.getBatchFrequency().name(), endTime, null));
                }
            }
        }else{
            build.setCreated(entity.getCreated());
            build.setSaasId(entity.getSaasId());
            build.setName(entity.getName());
            build.setSource(entity.getSource());
            build.setNextCreateTime(entity.getNextCreateTime());
            build.setCycle(entity.getCycle());
            if (dto.getAutoCreateBatch() && !dto.getBatchFrequency().name().equals(entity.getCycle())) {
                build.setCycle(dto.getBatchFrequency().name());
                if(StringUtils.isNotBlank(dto.getTaskId())){
                    ActivityTaskConfig task = activityTaskService.findTaskById(dto.getTaskId());
                    build.setNextCreateTime(getNextCreateTime(dto.getStatus().name(), task.getStatus(), TimeUtil.parse(dto.getStartTime()), dto.getBatchFrequency().name(), endTime, null));
                }else{
                    build.setNextCreateTime(getNextCreateTime(dto.getStatus().name(), null, TimeUtil.parse(dto.getStartTime()), dto.getBatchFrequency().name(), endTime, null));
                }
            }
        }
        build.setId(StringUtils.isBlank(dto.getId()) ? null : dto.getId());
        build.setType(dto.getType().name());
        build.setSubType(dto.getSubType().name());
        build.setStartTime(TimeUtil.parse(TimeUtil.getUtcTime(TimeUtil.parse(dto.getStartTime()), TimeUtil.YYYYMMDDHHMMSS)));
        build.setEndTime(endTime);
        build.setRemark(dto.getRemark());
        build.setStatus(dto.getStatus().getNumber());
        build.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        build.setAutoCreateBatch(dto.getAutoCreateBatch());
        build.setAutoApprove(Boolean.FALSE);
        if(dto.getAutoCreateBatch()){
            build.setAutoApprove(dto.getAutoApprove());
        }
        build.setArea(dto.getActivityArea());
        build.setTaskId(dto.getTaskId());
        //支持周期性创建后，修改cycle
        if (CollectionUtils.isNotEmpty(dto.getRewardRuleList())) {
            List<RewardRule> rules = new ArrayList<>();
            if (ActivityConstant.ActivityTypeEnum.INVITE.name().equals(dto.getType().name())) {
                Map<String, List<RewardRule>> mapRules = dto.getRewardRuleList().stream().map(rewardRule -> new RewardRule().toEntity(rewardRule)).collect(Collectors.groupingBy(RewardRule::getSide));
                for (Map.Entry<String, List<RewardRule>> entry : mapRules.entrySet()) {
                    isOverlapped(entry.getValue(), dto);
                    rules.addAll(entry.getValue());
                }
            } else {
                rules = dto.getRewardRuleList().stream().map(rewardRule -> new RewardRule().toEntity(rewardRule)).collect(Collectors.toList());
                isOverlapped(rules, dto);
            }
            build.setRewardConfig(JSON.toJSONString(rules));
        }
        if(CollectionUtils.isNotEmpty(dto.getConditionRuleList())){
            build.setConditions(JSON.toJSONString(dto.getConditionRuleList().stream().map(conditionRule -> Condition.of(conditionRule)).collect(Collectors.toList())));
        }
        build.setTemplateCode(dto.getConditionCode());
        return build;
    }

    @Override
    public Date getNextCreateTime(String status, Integer taskStatus, Date date, String cycle, Date endTime, String activityId){
        if(!ActivityConstant.ActivityStatusEnum.ACTIVE.name().equalsIgnoreCase(status)
                && (activityId == null || redisService.hHasKey(RedisKeyConst.ACTIVITY_CAL_LAST_KEY.getPrefix(), activityId))){
            return null;
        }
        if(taskStatus != null && ActivityConstant.ActivityTaskStatus.ACTIVE.getStatus() != taskStatus
            && (activityId == null || redisService.hHasKey(RedisKeyConst.ACTIVITY_CAL_LAST_KEY.getPrefix(), activityId))){
            return null;
        }
        if (StringUtils.isBlank(cycle)) {
            return null;
        }
        BatchFrequency frequency = BatchFrequency.valueOf(cycle);
        if (frequency == null) {
            return null;
        }
        if(date.compareTo(new Date()) < 0){
            date = new Date();
        }
        date = TimeUtil.parse(TimeUtil.getUtcTime(date,TimeUtil.YYYYMMDD_000000));
        switch (frequency){
            case EVERY_DAY:
                //每天11点执行
                return TimeUtil.addHour(TimeUtil.addDay(date, 1), 3);
            case EVERY_MONTH:
                //每天11点执行
                String monthStart = TimeUtil.getMonthStart(date, TimeFormat.YYYYMMDD_000000_PATTERN);
                return TimeUtil.addHour(TimeUtil.addMonth(TimeUtil.parse(monthStart), 1), 3);
            case FINISH:
                //forever
                if (endTime == null) {
                    return null;
                }
                return TimeUtil.addHour(TimeUtil.addDay(TimeUtil.parse(TimeUtil.getUtcTime(endTime, TimeUtil.YYYYMMDD_000000)), 1), 3);
            case EVERY_WEEK:
                // 每周一11点执行
                String weekStart = TimeUtil.getWeekStart(date, TimeFormat.YYYYMMDD_000000_PATTERN);
                return TimeUtil.addHour(TimeUtil.addWeek(TimeUtil.parse(weekStart), 1), 3);
        }
        return null;
    }

    private void isOverlapped(List<RewardRule> ruleList, ActivityDTO dto) {
        Map<String, List<RewardRule>> mapRules = ruleList.stream().collect(Collectors.groupingBy(r -> Optional.ofNullable(r.getVipLevel()).orElse("")));
        for(Map.Entry<String, List<RewardRule>> entry : mapRules.entrySet()){
            List<RewardRule> rules = entry.getValue();
            rules.sort(Comparator.comparing(RewardRule::getMin));
            for (int i = 0; i < rules.size(); i++) {
                if (rules.get(i).getMin() == null) {
                    throw new IllegalArgumentException("Batch creation rules cannot null");
                }
                if (rules.get(i).getMax() != null && rules.get(i).getMax() < rules.get(i).getMin()) {
                    throw new IllegalArgumentException("scope max must be greater scope min");
                }
                if (i > 0 && rules.get(i).getMin().equals(rules.get(i - 1).getMin())) {
                    throw new IllegalArgumentException("Batch creation rules cannot overlapped");
                }
                if (i > 0 && rules.get(i - 1).getMax() != null && rules.get(i).getMin() < rules.get(i - 1).getMax()) {
                    throw new IllegalArgumentException("Batch creation rules cannot overlapped");
                }
                if (rules.get(i).getAwardType().equalsIgnoreCase(ActivityConstant.AwardTypeEnum.TOKEN.name())
                        && dto.getAutoApprove()) {
                    throw new IllegalArgumentException("reward token must be not auto approve");
                }
            }
        }
    }
}
