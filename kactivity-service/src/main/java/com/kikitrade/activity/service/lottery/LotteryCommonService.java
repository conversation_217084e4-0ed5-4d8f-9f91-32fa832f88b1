package com.kikitrade.activity.service.lottery;

import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.dal.tablestore.model.ActivityCumulateItem;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;

import java.util.List;

public interface LotteryCommonService {

    /**
     * 抽奖
     * @param customerId
     * @param code
     * @param saasId
     * @return
     * @throws Exception
     */
    LotteryResponse lottery(String customerId, String code, String saasId) throws Exception;

    /**
     * 抽奖记录
     * @param customerId
     * @param code
     * @param saasId
     * @return
     */
    List<ActivityLotteryItem> lotteryItem(String customerId, String code, String saasId);

    /**
     * 奖池周期内中奖次数
     * @param customerId
     * @param code
     * @param saasId
     * @return
     */
    long lotteryCount(String customerId, String code, String saasId);

    /**
     * 奖池周期抽奖次数是否超过上限
     * @param customerId
     * @param code
     * @param saasId
     * @return
     */
    boolean isAboveLotteryCountLimit(String customerId, String code, String saasId);

    /**
     * 历史全部中奖次数
     * @param customerId
     * @param code
     * @param saasId
     * @return
     */
    long lotteryCountCumulateAll(String customerId, String code, String saasId);


    /**
     * 获取当前限制抽奖次数的周期开始时间
     * @param lotteryConfig /
     * @return
     */
    String getLimitStartTime(LotteryConfig lotteryConfig);

    /**
     * 获取当前次数抽奖可获取的累积奖励
     * @return
     */
    ActivityCumulateItem getActivityCumulate(String customerId, String code, String saasId, long count);
}
