package com.kikitrade.activity.service.rpc.osp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.service.common.config.OspProperties;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/5 17:18
 */
@Data
@Slf4j
public class OspConnectTGRequest {

    private OspProperties ospProperties;

    private String cid;
    private String saasId;

    public OspConnectTGRequest() {
    }

    public OspConnectTGRequest(OspProperties ospProperties) {
        this.ospProperties = ospProperties;
    }

    public OspConnectTGRequest build(String saasId, String cid) {
        this.saasId = saasId;
        this.cid = cid;
        return this;
    }


    public String execute() {
        try {
            String tgId = getTGId(this.saasId, this.cid);
            if (StringUtils.isNotBlank(tgId)) {
                return tgId;
            }
            return null;
        } catch (Exception e) {
            log.error("[OspConnectTGRequest] execute exception, cid:{}, error:", cid, e);
            return null;
        }
    }

    private String getTGId(String saasId,String ownerId) {
        try {
            SaasConfig config = SaasConfigLoader.getConfig(saasId);
            HttpUrl.Builder urlBuilder = HttpUrl.parse(config.getApiHost() + "/v2/s2s/owners/"+ownerId+"/binds/TELEGRAM").newBuilder();
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("os-app-id", config.getOspAppId())
                    .add("os-Api-Key", config.getOspAppKey());
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                log.info("[osp] getTGId response:{}", jsonObject);
                return Optional.ofNullable(jsonObject.getJSONObject("data"))
                        .map(o -> o.getString("platform_id")).orElse((null));
            }
            log.info("[osp] getTGId failed. cid:{}, response:{}", this.cid, response);
            return null;
        } catch (Exception e) {
            log.error("[osp] getTGId Exception:{}, error:", this.cid, e);
            return null;
        }
    }
}
