package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.dal.tablestore.model.UserClaimEntitlement;

import java.util.List;

/**
 * 统一领奖服务接口
 * 按照技术规格书要求实现统一的领奖能力
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface UnifiedClaimService {
    
    /**
     * 创建领奖凭证
     * 
     * @param request 创建凭证请求
     * @return 创建结果
     */
    EntitlementCreateResult createEntitlement(CreateEntitlementRequest request);
    
    /**
     * 统一领奖接口
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param claimId 凭证ID
     * @return 领奖结果
     */
    UnifiedClaimResult claimReward(String userId, String saasId, String claimId);
    
    /**
     * 获取用户可领取的奖励列表
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 可领取的奖励列表
     */
    List<ClaimableReward> getClaimableRewards(String userId, String saasId);
    
    /**
     * 批量创建凭证（用于进度宝箱等自动生成场景）
     * 
     * @param requests 创建凭证请求列表
     * @return 创建结果列表
     */
    List<EntitlementCreateResult> batchCreateEntitlements(List<CreateEntitlementRequest> requests);
    
    /**
     * 清理过期凭证
     * 
     * @param saasId SaaS ID
     * @return 清理的凭证数量
     */
    int cleanupExpiredEntitlements(String saasId);
    
    /**
     * 创建凭证请求
     */
    class CreateEntitlementRequest {
        private String userId;
        private String saasId;
        private String rewardType;
        private String rewardSourceId;
        private String rewardName;
        private String rewardIcon;
        private String rewardDescription;
        private String sourceChannel;
        private String sourceTransactionId;
        private Long expireTime;
        
        // Constructors
        public CreateEntitlementRequest() {}
        
        public CreateEntitlementRequest(String userId, String saasId, String rewardType, 
                                      String rewardSourceId, String sourceChannel) {
            this.userId = userId;
            this.saasId = saasId;
            this.rewardType = rewardType;
            this.rewardSourceId = rewardSourceId;
            this.sourceChannel = sourceChannel;
        }
        
        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getSaasId() { return saasId; }
        public void setSaasId(String saasId) { this.saasId = saasId; }
        
        public String getRewardType() { return rewardType; }
        public void setRewardType(String rewardType) { this.rewardType = rewardType; }
        
        public String getRewardSourceId() { return rewardSourceId; }
        public void setRewardSourceId(String rewardSourceId) { this.rewardSourceId = rewardSourceId; }
        
        public String getRewardName() { return rewardName; }
        public void setRewardName(String rewardName) { this.rewardName = rewardName; }
        
        public String getRewardIcon() { return rewardIcon; }
        public void setRewardIcon(String rewardIcon) { this.rewardIcon = rewardIcon; }
        
        public String getRewardDescription() { return rewardDescription; }
        public void setRewardDescription(String rewardDescription) { this.rewardDescription = rewardDescription; }
        
        public String getSourceChannel() { return sourceChannel; }
        public void setSourceChannel(String sourceChannel) { this.sourceChannel = sourceChannel; }
        
        public String getSourceTransactionId() { return sourceTransactionId; }
        public void setSourceTransactionId(String sourceTransactionId) { this.sourceTransactionId = sourceTransactionId; }
        
        public Long getExpireTime() { return expireTime; }
        public void setExpireTime(Long expireTime) { this.expireTime = expireTime; }
    }
    
    /**
     * 凭证创建结果
     */
    class EntitlementCreateResult {
        private boolean success;
        private String claimId;
        private String errorCode;
        private String message;
        
        public EntitlementCreateResult(boolean success, String claimId, String message) {
            this.success = success;
            this.claimId = claimId;
            this.message = message;
        }
        
        public EntitlementCreateResult(boolean success, String errorCode, String message) {
            this.success = success;
            this.errorCode = errorCode;
            this.message = message;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getClaimId() { return claimId; }
        public void setClaimId(String claimId) { this.claimId = claimId; }
        
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
    
    /**
     * 统一领奖结果
     */
    class UnifiedClaimResult {
        private boolean success;
        private String errorCode;
        private String message;
        private List<ClaimedReward> rewards;
        
        public UnifiedClaimResult(boolean success, String message, List<ClaimedReward> rewards) {
            this.success = success;
            this.message = message;
            this.rewards = rewards;
        }
        
        public UnifiedClaimResult(boolean success, String errorCode, String message) {
            this.success = success;
            this.errorCode = errorCode;
            this.message = message;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public List<ClaimedReward> getRewards() { return rewards; }
        public void setRewards(List<ClaimedReward> rewards) { this.rewards = rewards; }
    }
    
    /**
     * 可领取的奖励信息
     */
    class ClaimableReward {
        private String claimId;
        private String rewardType;
        private String rewardName;
        private String rewardIcon;
        private String rewardDescription;
        private Long createTime;
        private Long expireTime;
        
        // Getters and Setters
        public String getClaimId() { return claimId; }
        public void setClaimId(String claimId) { this.claimId = claimId; }
        
        public String getRewardType() { return rewardType; }
        public void setRewardType(String rewardType) { this.rewardType = rewardType; }
        
        public String getRewardName() { return rewardName; }
        public void setRewardName(String rewardName) { this.rewardName = rewardName; }
        
        public String getRewardIcon() { return rewardIcon; }
        public void setRewardIcon(String rewardIcon) { this.rewardIcon = rewardIcon; }
        
        public String getRewardDescription() { return rewardDescription; }
        public void setRewardDescription(String rewardDescription) { this.rewardDescription = rewardDescription; }
        
        public Long getCreateTime() { return createTime; }
        public void setCreateTime(Long createTime) { this.createTime = createTime; }
        
        public Long getExpireTime() { return expireTime; }
        public void setExpireTime(Long expireTime) { this.expireTime = expireTime; }
    }
    
    /**
     * 已领取的奖励信息
     */
    class ClaimedReward {
        private String itemId;
        private String itemName;
        private Integer quantity;
        private String itemType;
        private String description;
        
        public ClaimedReward(String itemId, String itemName, Integer quantity) {
            this.itemId = itemId;
            this.itemName = itemName;
            this.quantity = quantity;
        }
        
        // Getters and Setters
        public String getItemId() { return itemId; }
        public void setItemId(String itemId) { this.itemId = itemId; }
        
        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }
        
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
        
        public String getItemType() { return itemType; }
        public void setItemType(String itemType) { this.itemType = itemType; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
}
