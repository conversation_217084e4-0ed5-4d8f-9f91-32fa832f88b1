package com.kikitrade.activity.service.business;

import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.model.exception.ActivityException;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/9 14:06
 */
public interface ClaimItemService {

    Boolean isContinue(String businessType);

    Boolean allowClaim(String businessType, String customerId, String address, List<String> addresses);

    ClaimResponse claim(String businessType, String address, String customerId, String code, String saasId, List<String> addresses) throws ActivityException;
}
