package com.kikitrade.activity.service.rpc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 社交平台用户信息
 * 统一不同平台的用户信息结构
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialUserInfo implements Serializable {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 邮箱地址（主要用于Google平台）
     */
    private String email;

    /**
     * 头像URL
     */
    private String profileImageUrl;

    /**
     * 创建时间
     */
    private Long createdAt;

    private String name;
}
