package com.kikitrade.activity.service.business;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.facade.award.ActivityDTO;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;

import java.util.Date;
import java.util.List;

public interface ActivityEntityService {

    /**
     * 保存活动
     *
     * @param activityDTO
     * @return
     */
    Result<String> save(ActivityDTO activityDTO, ActivityConstant.ActivitySourceEnum source, String activityId, String batchId);


    /**
     * 修改活动
     *
     * @param activityDTO
     * @return
     */
    Result<String> updateCheckType(ActivityDTO activityDTO);

    /**
     * 修改活动
     *
     * @param activityEntity
     * @return
     */
    Result<String> update(ActivityEntity activityEntity);

    /**
     * 修改活动
     *
     * @param activityEntity
     * @return
     */
    Result<String> updateNextCreateTime(ActivityEntity activityEntity);

    /**
     * 查询全部活动
     *
     * @return
     */
    List<ActivityEntity> findByAutoCreateBatch(Date date);

    /**
     * 查询单个活动
     *
     * @param activityId
     * @return
     */
    ActivityEntity findById(String activityId);

    ActivityEntity findByIdFromCache(String activityId);

    /**
     * 计算下次创建批次时间
     *
     * @param date
     * @param cycle
     * @param endTime
     * @return
     */
    Date getNextCreateTime(String status, Integer taskStatus ,Date date, String cycle, Date endTime, String activityId);
}
