package com.kikitrade.activity.service.rpc;

import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.rpc.discord.JoinChannelRequest;
import com.kikitrade.activity.service.rpc.game.GameRequest;
import com.kikitrade.activity.service.rpc.osp.OspBlackListRequest;
import com.kikitrade.activity.service.rpc.osp.OspCallBackRequest;
import com.kikitrade.activity.service.rpc.osp.OspConnectTGRequest;
import com.kikitrade.activity.service.rpc.osp.OspConnectWalletRequest;
import com.kikitrade.activity.service.rpc.telepulse.TelePulseRequest;
import com.kikitrade.activity.service.rpc.mugen.MugenCustomerAddressRequest;
import com.kikitrade.activity.service.rpc.osp.*;
import com.kikitrade.activity.service.rpc.tg.TGBotRequest;
import com.kikitrade.activity.service.rpc.twitter.*;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 15:24
 */
@Slf4j
@Component
public class ThreePlatformApi {

    private Twitter twitter = new Twitter();
    private Discord discord = new Discord();
    private Google google = new Google();
    private Line line = new Line();
    private Facebook facebook = new Facebook();

    private Game game = new Game();

    private Mugen mugen = new Mugen();
    private Osp osp = new Osp();
    private TG tg = new TG();
    private TelePulse telePulse = new TelePulse();

    public Twitter twitter(){
        return twitter;
    }

    public Discord discord(){
        return discord;
    }

    public Game game(){
        return game;
    }

    public Mugen mugen(){
        return mugen;
    }

    public Osp osp(){
        return osp;
    }

    public TG tg(){return tg;}

    public TelePulse telePulse(){return telePulse;}

    public Google google(){
        return google;
    }

    public Line line(){
        return line;
    }

    public Facebook facebook(){
        return facebook;
    }

    @Resource
    private ThreePlatformProperties threePlatformProperties;
    @Resource
    private OkHttpClient okHttpClient;

    public class Twitter{

        /**
         * 查询当前用户
         * @param accessToken
         * @return
         */
        public CurrentUserRequest getCurrentUserRequest(AccessToken accessToken){
            return new CurrentUserRequest(threePlatformProperties.getTwitter(), accessToken);
        }


        /**
         * 根据用户 id 查询
         * @return
         */
        public UserRequest getUserRequest(){
            return new UserRequest(threePlatformProperties.getTwitter());
        }

        /**
         * 帖子点赞行为
         * @param accessToken
         * @return
         */
        public LikedTweetRequest getLikedTweetRequest(AccessToken accessToken){
            return new LikedTweetRequest(threePlatformProperties.getTwitter(), accessToken);
        }

        /**
         * 帖子转发行为
         * @param accessToken
         * @return
         */
        public RetweetRequest getReTweetRequest(AccessToken accessToken){
            return new RetweetRequest(threePlatformProperties.getTwitter(), accessToken);
        }

        /**
         * 用户关注行为
         * @param accessToken
         * @return
         */
        public FollowingRequest getFollowingRequest(AccessToken accessToken){
            return new FollowingRequest(threePlatformProperties.getTwitter(), accessToken);
        }

        /**
         * 帖子评论行为
         * @param accessToken
         * @return
         */
        public CommentPostRequest getCommentRequest(AccessToken accessToken){
            return new CommentPostRequest(threePlatformProperties.getTwitter(), accessToken);
        }

        /**
         * 帖子回复行为
         * @param accessToken
         * @return
         */
        public ReplyPostRequest getReplyPostRequest(AccessToken accessToken){
            return new ReplyPostRequest(threePlatformProperties.getTwitter(), accessToken);
        }

        /**
         * 用户发帖行为
         * @param accessToken
         * @return
         */
        public PostSubjectRequest getPostSubjectRequest(AccessToken accessToken){
            return new PostSubjectRequest(threePlatformProperties.getTwitter(), accessToken);
        }

        /**
         * @return
         */
        public KolPostRequest getKolPostRequest(){
            return new KolPostRequest(threePlatformProperties.getTwitter());
        }

        /**
         * 用户最新发帖行为
         * @param accessToken
         * @return
         */
        public CurrentUserPostRequest getCurrentUserPostRequest(AccessToken accessToken) {
            return new CurrentUserPostRequest(threePlatformProperties.getTwitter(), accessToken);
        }
    }

    public class Discord{

        /**
         * 查询当前用户
         * @param accessToken
         * @return
         */
        public com.kikitrade.activity.service.rpc.discord.CurrentUserRequest getCurrentUserRequest(AccessToken accessToken){
            return new com.kikitrade.activity.service.rpc.discord.CurrentUserRequest(threePlatformProperties.getDiscord(), accessToken.getAccessToken());
        }

        /**
         * 加入 discord
         * @param accessToken
         * @return
         */
        public JoinChannelRequest getJoinChannelRequest(AccessToken accessToken){
            return new JoinChannelRequest(threePlatformProperties.getDiscord(), accessToken);
        }
    }

    public class Game{
        public GameRequest getGameRequest(AccessToken accessToken){
            return new GameRequest(threePlatformProperties.getGame(), accessToken);
        }
    }

    public class Mugen{
        /**
         * 查询 mugen 用户钱包
         * @param accessToken jwt token
         * @return
         */
        public MugenCustomerAddressRequest getMugenCustomerAddressRequest(AccessToken accessToken){
            return new MugenCustomerAddressRequest(threePlatformProperties.getMugen(), accessToken);
        }
    }

    public class Osp{
        /**
         * 根据钱包地址查询 osp 创建 profile id
         * @param
         * @return
         */
        public OspProfileRequest getOspProfileRequest(){
            return new OspProfileRequest(threePlatformProperties.getOsp());
        }

        public OspConnectTGRequest getOspConnectTGRequest(){
            return new OspConnectTGRequest(threePlatformProperties.getOsp());
        }

        public OspConnectWalletRequest getOspConnectWalletRequest(){
            return new OspConnectWalletRequest(threePlatformProperties.getOsp());
        }

        public OspCallBackRequest getOspCallBackRequest(){
            return new OspCallBackRequest(threePlatformProperties.getOsp());
        }

        public OspCreateDappRequest getOspCreateDappRequest(){
            return new OspCreateDappRequest(threePlatformProperties.getOsp());
        }

        public OspBlackListRequest getOspBlackListRequest() {
            return new OspBlackListRequest(threePlatformProperties.getOsp());
        }
    }

    public class TG{
        public TGBotRequest getTGBotRequest(AccessToken accessToken){
            return new TGBotRequest(threePlatformProperties.getTg().getChat().get(accessToken.getSaasId()), accessToken.getTgId());
        }
    }

    public class TelePulse {
        public TelePulseRequest getTelePulseRequest(){
            return new TelePulseRequest(threePlatformProperties.getTelePulse());
        }
    }

    public class Google {
        public com.kikitrade.activity.service.rpc.google.CurrentUserRequest getCurrentUserRequest(AccessToken accessToken){
            return new com.kikitrade.activity.service.rpc.google.CurrentUserRequest(threePlatformProperties.getGoogle(), accessToken.getAccessToken());
        }
    }

    public class Line {
        public com.kikitrade.activity.service.rpc.line.CurrentUserRequest getCurrentUserRequest(AccessToken accessToken){
            return new com.kikitrade.activity.service.rpc.line.CurrentUserRequest(threePlatformProperties.getLine(), accessToken.getAccessToken());
        }
    }

    public class Facebook {
        public com.kikitrade.activity.service.rpc.facebook.CurrentUserRequest getCurrentUserRequest(AccessToken accessToken){
            return new com.kikitrade.activity.service.rpc.facebook.CurrentUserRequest(threePlatformProperties.getFacebook(), accessToken.getAccessToken());
        }
    }
}
