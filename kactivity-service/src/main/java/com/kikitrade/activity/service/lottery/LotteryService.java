package com.kikitrade.activity.service.lottery;

import com.kikitrade.activity.api.model.LotteryItem;
import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.dal.mysql.model.ActivityContents;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.tablestore.model.ActivityCumulateItem;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.dal.tablestore.model.LotteryAward;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.model.StoreInfo;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.quota.api.model.response.RpcResponse;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface LotteryService {

    /**
     * 抽奖
     * @param customerId /
     * @param lotteryConfig /
     * @return
     */
    LotteryAward draw(String customerId, LotteryConfig lotteryConfig) throws Exception;

    /**
     * 保存抽奖记录
     * @param customerId
     * @param code
     * @param saasId
     * @param lotteryAward
     */
    ActivityLotteryItem saveLotteryItem(String customerId, String code, String saasId, LotteryAward lotteryAward, LotteryConfig lotteryConfig);

    /**
     * 中奖后续操作
     * @return
     */
    LotteryResponse postAction(ActivityLotteryItem lotteryItem);
}
