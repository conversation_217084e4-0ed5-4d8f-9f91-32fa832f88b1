package com.kikitrade.activity.service.rpc;

import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.twitter.clientlib.TwitterCredentialsBearer;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Base64;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccessToken implements Serializable {

    protected String saasId;
    protected String accessToken;
    protected String refreshToken;
    protected String userId;
    protected String currentUserName;
    protected String vipLevel;
    protected String address;
    protected String twitterHandleName;
    protected String twitterId;

    protected Long tgId;

    public AccessToken(AccessToken accessToken){
        this.accessToken = accessToken.getAccessToken();
        this.saasId = accessToken.getSaasId();
        this.currentUserName = accessToken.getCurrentUserName();
        this.refreshToken = accessToken.getRefreshToken();
        this.vipLevel = accessToken.getVipLevel();
        this.userId = accessToken.getUserId();
        this.address = accessToken.getAddress();
        this.tgId = accessToken.getTgId();
    }

    public TwitterApi getApi(String accessToken, String refreshToken, TwitterProperties twitterProperties) {
        return new TwitterApi(new TwitterCredentialsOAuth2(
                twitterProperties.getClientId().get(saasId), twitterProperties.getClientSecret().get(saasId),
                accessToken, refreshToken,
                refreshToken != null
        ));
    }

    public TwitterApi getApi(TwitterProperties twitterProperties) {
        return new TwitterApi(new TwitterCredentialsBearer(twitterProperties.getBearer().get(saasId)));
    }
}
