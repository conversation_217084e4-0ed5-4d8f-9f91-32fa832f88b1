package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityRewardInfoDao;
import com.kikitrade.activity.service.business.ActivityRewardInfoService;
import com.kikitrade.activity.dal.mysql.model.ActivityRewardInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityRewardInfoServiceImpl implements ActivityRewardInfoService {


    @Resource
    private ActivityRewardInfoDao activityRewardInfoDao;

    @Override
    public List<ActivityRewardInfo> findByActivityId(Integer activity_id) {
        List<ActivityRewardInfo> activityRewardInfoList = null;
        try {
            activityRewardInfoList = activityRewardInfoDao.findByActivityId(activity_id);
        } catch (Exception e) {
            log.error("ActivityRewardInfoServiceImpl findAll process failed.", e);
        }
        return activityRewardInfoList;
    }


}
