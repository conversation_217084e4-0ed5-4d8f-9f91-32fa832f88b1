package com.kikitrade.activity.service.importing.award.impl;

import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.service.importing.award.RewardEntityService;
import org.springframework.stereotype.Service;

@Service
public class RewardEntityServiceImpl implements RewardEntityService {
    /**
     * 发奖
     *
     * @param activityCustomReward
     * @return
     */
    @Override
    public Result<String> reward(ActivityCustomReward activityCustomReward) {
        //判断活动类型
        return null;
    }
}
