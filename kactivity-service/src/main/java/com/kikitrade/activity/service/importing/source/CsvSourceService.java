package com.kikitrade.activity.service.importing.source;

import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.service.importing.source.domain.ImportSourceDTO;

public interface CsvSourceService {

    /**
     * 导入文件到roster表
     * @param fileUrl
     * @param dto
     * @return
     */
    Result<String> importSource(String fileUrl, ImportSourceDTO dto);

    boolean support(String activityType);
}
