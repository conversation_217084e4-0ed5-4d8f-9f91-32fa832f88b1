package com.kikitrade.activity.service.question;

import com.kikitrade.activity.dal.tablestore.model.CustomerQuestionSets;

public interface QuestionSetsService {

    boolean insert(CustomerQuestionSets customerQuestionSets);

    Long incrementAvailableSets(CustomerQuestionSets customerQuestionSets, int inc);

    Long incrementUsedSets(CustomerQuestionSets customerQuestionSets);

    boolean update(CustomerQuestionSets customerQuestionSets);

    CustomerQuestionSets findByUid(String saasId, String customerId);

    CustomerQuestionSets findByUser(String saasId, String customerId);

}
