package com.kikitrade.activity.service.autoreward.domain;

import com.kikitrade.activity.facade.award.ConditionRule;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-11-30 20:43
 */
@Data
public class Condition {
    private String name;
    private String alias;
    private String filter;
    private Object value;

    public static Condition of(ConditionRule rule){
        Condition condition = new Condition();
        condition.setName(rule.getName());
        condition.setAlias(rule.getAlias());
        condition.setFilter(rule.getFilter());
        condition.setValue(rule.getValue());
        return condition;
    }
}
