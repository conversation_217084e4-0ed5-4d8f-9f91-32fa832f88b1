package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityCustomerCurrentInterestDao;
import com.kikitrade.activity.service.business.ActivityCustomerInterestService;
import com.kikitrade.activity.dal.mysql.model.ActivityCustomerCurrentInterest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityCustomerInterestServiceImpl implements ActivityCustomerInterestService {


    @Resource
    private ActivityCustomerCurrentInterestDao activityCustomerCurrentInterestDao;


    @Override
    public List<ActivityCustomerCurrentInterest> findAll(String transDate, Integer offset, Integer limit) {
        List<ActivityCustomerCurrentInterest> activityCustomerCurrentInterestList = null;
        try {
            activityCustomerCurrentInterestList = activityCustomerCurrentInterestDao.findAll(transDate, offset, limit);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl findAll process failed.", e);
        }
        return activityCustomerCurrentInterestList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(String transDate, String customer_id, String currency, Integer status) {
        int count = 0;
        try {
            count = activityCustomerCurrentInterestDao.updateStatus(transDate, customer_id, currency, status);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl updateStatus process failed.", e);
            throw e;
        }
        return count;
    }

    @Override
    public Long countByStatus(String transDate, Integer status) {
        try {
            return activityCustomerCurrentInterestDao.countByStatus(transDate, status);
        } catch (Exception e) {
            log.error("activitycustomerfeeserviceimpl countByTypeAndTye process failed.transDate{} status:{}", transDate, status, e);
            return -1L;
        }
    }


}
