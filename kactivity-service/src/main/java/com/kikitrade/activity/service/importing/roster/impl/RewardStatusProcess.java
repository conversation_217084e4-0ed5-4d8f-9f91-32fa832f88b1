package com.kikitrade.activity.service.importing.roster.impl;

import com.kikitrade.accounting.api.model.InternalAccount;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.kcustomer.common.constants.CustomerConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@Order(2)
public class RewardStatusProcess extends AbstractRewardProcess {

    @Override
    boolean support(ActivityEntity activityEntity, ActivityBatch activityBatch, CustomerCacheDTO customerDO) {
        return true;
    }

    @Override
    void doProcess(ActivityEntity activityEntity, ActivityBatch activityBatch, ActivityCustomReward activityCustomReward, CustomerCacheDTO customerDO) {
        Map<Integer, CustomerConstants.Status> statusMap = new HashMap<>();
        for(CustomerConstants.Status status : CustomerConstants.Status.values()){
            statusMap.put(status.getCode(), status);
        }
        try{
            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.NOT_AWARD.name());
            if(customerDO == null){
                activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                activityCustomReward.setMessage("customerId not found");
            }else if(customerDO.getStatus() != CustomerConstants.Status.ACTIVATED.getCode()){
                activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                activityCustomReward.setMessage(String.format("customer already %s", statusMap.get(customerDO.getStatus())));
            }else if(InternalAccount.fromId(activityCustomReward.getCustomerId()) != null){
                activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                activityCustomReward.setMessage("from/to customer cannot be the same");
            }else if(StringUtils.isNotBlank(activityEntity.getArea())){
                boolean match = Arrays.stream(activityEntity.getArea().split("、")).anyMatch(area -> area.equalsIgnoreCase(customerDO.getCountry()));
                if(!match && StringUtils.isNotBlank(customerDO.getCountry())){
                    activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                    activityCustomReward.setMessage("country not matching");
                }
            }
            if(customerDO != null){
                activityCustomReward.setEmail(customerDO.getEmail());
                activityCustomReward.setPhone(customerDO.getPhone());
                activityCustomReward.setUserName(customerDO.getUserName());
                activityCustomReward.setUserType(CustomerConstants.Type.fromCode(customerDO.getType()).name());
                if(StringUtils.isBlank(activityCustomReward.getNickName())){
                    activityCustomReward.setNickName(customerDO.getNickName());
                }else{
                    String[] nickNameArr = activityCustomReward.getNickName().split(",");
                    if(nickNameArr.length > 1){
                        activityCustomReward.setNickName(String.format("%s,%s ...", nickNameArr[0], nickNameArr[1]));
                    }else{
                        activityCustomReward.setNickName(nickNameArr[0]);
                    }
                }
            }
        }catch (Exception ex){
            log.error("customerService error",ex);
        }
    }
}
