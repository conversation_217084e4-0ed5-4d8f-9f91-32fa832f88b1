package com.kikitrade.activity.service.business.impl;


import com.kikitrade.activity.dal.mysql.dao.SchedLogDao;
import com.kikitrade.activity.service.business.SchedLogService;
import com.kikitrade.activity.dal.mysql.model.SchedLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Slf4j
@Service
public class SchedLogServiceImpl implements SchedLogService {

    @Resource
    private SchedLogDao schedLogDao;

    @Override
    public SchedLog findByBatch(String batch_pt, String job_nm) {

        try {
            return schedLogDao.findByBatch(batch_pt, job_nm);
        } catch (Exception e) {
            log.error("schedlogserviceimpl findByBatch process failed.", e);
        }
        return null;
    }

    @Override
    public SchedLog findByLatestBatch(String job_nm) {

        try {
            return schedLogDao.findByLatestBatch(job_nm);
        } catch (Exception e) {
            log.error("schedlogserviceimpl findByLatestBatch process failed.", e);
        }
        return null;
    }


}
