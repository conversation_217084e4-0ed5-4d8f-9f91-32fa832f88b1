package com.kikitrade.activity.service.importing.roster.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Order(4)
@Slf4j
public class RewardBusinessIdProcess extends AbstractRewardProcess {

    @Override
    boolean support(ActivityEntity activityEntity, ActivityBatch activityBatch, CustomerCacheDTO customerDO) {
        return true;
    }

    @Override
    void doProcess(ActivityEntity activityEntity, ActivityBatch activityBatch, ActivityCustomReward activityCustomReward, CustomerCacheDTO customerDO) {
        log.info("RewardBusinessIdProcess:{}", JSON.toJSONString(activityCustomReward));
        activityCustomReward.setActivityId(activityEntity.getId());
        activityCustomReward.setBusinessId(String.format("%s-%s-%s",activityCustomReward.getBatchId(), activityCustomReward.getCustomerId(), activityCustomReward.getSeq()));
        activityCustomReward.setActivityName(activityEntity.getName());
        activityCustomReward.setBusinessType("reward");
        if(ActivityConstant.ActivityTypeEnum.value(activityEntity.getType()) == ActivityConstant.ActivityTypeEnum.INVITE){
            activityCustomReward.setBusinessType(ActivityConstant.RewardBusinessType.get(activityEntity.getType(), activityEntity.getSubType()).name());
        }
        if(StringUtils.isBlank(activityCustomReward.getSeq())){
            switch (ActivityConstant.ActivityTypeEnum.value(activityEntity.getType())){
                case INVITE:
                    activityCustomReward.setSeq(getSeq(ActivityConstant.SeqPrefix.INVITE_SEQ, activityBatch.getBatchId()));
                    break;
                case CUSTOMIZE:
                    activityCustomReward.setSeq(getSeq(ActivityConstant.SeqPrefix.CUSTOMIZE_SEQ, activityBatch.getBatchId()));
                    break;
                case HIERARCHY:
                    activityCustomReward.setSeq(getSeq(ActivityConstant.SeqPrefix.HIERARCHY_SEQ, activityBatch.getBatchId()));
                    break;
                case NORMAL:
                    activityCustomReward.setSeq(getSeq(ActivityConstant.SeqPrefix.NORMAL_SEQ, activityBatch.getBatchId()));
                    break;
            }
        }
    }
}
