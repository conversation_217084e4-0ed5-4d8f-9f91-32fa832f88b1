package com.kikitrade.activity.service.importing;

import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.importing.roster.domain.LauncherResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public abstract class ActivityLauncher<T extends LauncherResult> {

    public void run(LauncherParameter launcherParameter) throws Exception {
        boolean before = before(launcherParameter);
        if(!before){
            //前置校验不通过
            after(launcherParameter);
            return;
        }
        T item = null;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        do{
            try{
                log.error("ActivityLauncher{},{}", launcherParameter, stopWatch.getTime());
                if(stopWatch.getTime() > 3 * 60 * 1000){
                    log.error("ActivityLauncher stop{},{}", launcherParameter, stopWatch.getTime());
                    break;
                }
                item = reader(launcherParameter);
                exec(item, launcherParameter);
            }catch (Exception ex){
                log.error("activity launcher run exception", ex);
                throw ex;
            }
        }while (!item.isFinish());
        if(item != null && item.isFinish()){
            after(launcherParameter);
        }
    }

    protected abstract boolean before(LauncherParameter launcherParameter);

    protected abstract T reader(LauncherParameter launcherParameter);

    protected abstract void exec(T t, LauncherParameter launcherParameter) throws Exception;

    protected abstract void after(LauncherParameter launcherParameter);
}
