package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.EmailTemplateType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.kcustomer.api.constants.CommConstants;
import com.kikitrade.kcustomer.api.constants.FirebaseTemplateType;
import com.kikitrade.kcustomer.api.model.CustomerExtraDTO;
import com.kikitrade.kcustomer.api.model.DingTalkMessageDTO;
import com.kikitrade.kcustomer.api.model.EmailNotificationDTO;
import com.kikitrade.kcustomer.api.model.FirebaseMessageDTO;
import com.kikitrade.kcustomer.api.model.PushMessageDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerExtraService;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class NoticeServiceImpl implements NoticeService {
    @DubboReference
    private RemoteCustomerExtraService remoteCustomerExtraService;
    @DubboReference
    private RemoteNotificationService remoteNotificationService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private RedisService redisService;

    @Override
    public void dispatchFailNotice(String msg) {

        log.error("NoticeServiceImpl reward dispatch fail,place check! {}", msg);
    }

    @Override
    public void doActionsFailNotice(String business_id, Integer activity_id) {
        log.error("NoticeServiceImpl doActions fail, business_id [{}], activity id [{}]", business_id, activity_id);
    }

    @Override
    public void doActionsPrepareFailNotice(String business_id, Integer activity_id) {
        log.error("NoticeServiceImpl doActionsPrepare fail, business_id [{}], activity id [{}] ", business_id, activity_id);
    }

    @Override
    @Async
    public void notice(String saasId, String customerId, EmailTemplateType emailTemplate, RewardRequest request, ActivityConstant.FirebaseTemplateCode notificationEnum) {
        if(!kactivityProperties.isNoticeSwitch()){
            return;
        }
        if(emailTemplate!=null){//邮件推送
            emailMsgSend(saasId,customerId,emailTemplate,request);
        }

        if(notificationEnum!=null){//firebase推送
            JSONObject activityRewardPush =new JSONObject(true);
            activityRewardPush.put("nickName", request.getNickName());
            activityRewardPush.put("activityName", request.getActivityName());
            activityRewardPush.put("amount", request.getAmount().toPlainString());
            activityRewardPush.put("currency", request.getCurrency());
            activityRewardPush.put("customerId", request.getCustomerId());
            activityRewardPush.put("orderNum", request.getRewardId());
            //activityRewardPush.put("titleKey", notificationEnum.getTitleKey());

            pushMsgSend(activityRewardPush,notificationEnum);
        }
    }

    public void emailMsgSend(String saasId, String customerId, EmailTemplateType emailTemplate, RewardRequest request) {
        try {
            CustomerExtraDTO customerExtraDTO = remoteCustomerExtraService.getById(saasId, customerId, true);
            if (!customerExtraDTO.getIsEmailCertified() || StringUtils.isBlank(customerExtraDTO.getEmail())) {
                return;
            }
            Map<String,Object> map=buildEmailData(customerExtraDTO,request);
            EmailNotificationDTO emialMessageDTO = new EmailNotificationDTO();
            emialMessageDTO.setCustomerId(customerId);
            emialMessageDTO.setTemplateId(emailTemplate.name());
            emialMessageDTO.setParameterMap(map);
            emialMessageDTO.setSaasId(saasId);

            log.info("emailMsgSend send ...{}", JSONObject.toJSONString(emialMessageDTO));
            boolean success = remoteNotificationService.send(emialMessageDTO);
            log.info("emailMsgSend process success[{}].", success);

        }catch (Exception e){
            log.error("emailMsgSend process error.", e);
        }
    }
    Map buildEmailData(CustomerExtraDTO customerExtraDTO,RewardRequest request){
        Map<String,Object> map=new HashMap();
        map.put("name",customerExtraDTO.getUserName());
        map.put("customerId",customerExtraDTO.getUserName());
        map.put("reward",request.getAmount().toPlainString()+" "+request.getCurrency());
        return map;
    }

    private List<String> buildBodyArgs(JSONObject object, ActivityConstant.FirebaseTemplateCode notificationEnum){

        String nickName = object.getString("nickName");
        String activityName = object.getString("activityName");
        String amount = object.getString("amount");
        String currency = object.getString("currency");

        switch (notificationEnum){
            case activity_invite_reward:
                return Lists.newArrayList(nickName, activityName, amount, currency);
            case activity_reward:
                return Lists.newArrayList(activityName, amount, currency);
            default:
                return Lists.newArrayList(amount, currency);
        }
    }

    private List<String> buildTitleArgs(ActivityConstant.FirebaseTemplateCode notificationEnum){
        switch (notificationEnum){
            case activity_invite_reward:
                return Lists.newArrayList("nickName", "activityName", "amount", "currency");
            case activity_reward:
                return Lists.newArrayList("activityName", "amount", "currency");
            default:
                return Lists.newArrayList("amount", "currency");
        }
    }

    private void pushMsgSend(JSONObject object, ActivityConstant.FirebaseTemplateCode notificationEnum){
        try{
            PushMessageDTO pushMessageDTO = new PushMessageDTO();
            pushMessageDTO.setMsgId(object.getString("orderNum"));
            pushMessageDTO.setCustomerId(object.getString("customerId"));
            pushMessageDTO.setExtParams(object);
            pushMessageDTO.setTitleKey(notificationEnum.getTitleKey());
            pushMessageDTO.setBodyKey(notificationEnum.getBodyKey());
            pushMessageDTO.setTitleArgs(buildTitleArgs(notificationEnum));
            pushMessageDTO.setBodyArgs(buildBodyArgs(object, notificationEnum));

            log.info("pushMsgSend send ...{}", JSONObject.toJSONString(pushMessageDTO));
            boolean success = remoteNotificationService.send(pushMessageDTO);
            log.info("pushMsgSend process success[{}].", success);
        }catch (Exception ex){
            log.error("pushMsgSend process error.", ex);
        }
    }

    @Override
    public void noticeDingTalk(String msgId, String url, String msg) {
        try{
            if(redisService.hasKey(RedisKeyConst.ACTIVITY_REWARD_NOTICE.getKey(msgId))){
                return;
            }
            boolean success = redisService.setIfAbsent(RedisKeyConst.ACTIVITY_REWARD_NOTICE.getKey(msgId), "1", TimeUnit.DAYS.toSeconds(1));
            if(success){
                log.info("noticeDingTalk:{},{}", url, msg);
                DingTalkMessageDTO messageDTO = new DingTalkMessageDTO();
                messageDTO.setNotifyUrl(url);
                messageDTO.setMessage(msg);
                remoteNotificationService.send(messageDTO);
            }
        }catch (Exception ex){
            log.error("noticeDingTalk error", ex);
        }
    }
}