package com.kikitrade.activity.service.importing.roster.impl;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.importing.roster.RewardImportingProcess;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;

import jakarta.annotation.Resource;

public abstract class AbstractRewardProcess implements RewardImportingProcess {

    @Resource
    private RewardSeq rewardSeq;

    @Override
    public void process(ActivityEntity activityEntity, ActivityBatch activityBatch, ActivityCustomReward activityCustomReward, CustomerCacheDTO customerDO) {
        boolean support = support(activityEntity, activityBatch, customerDO);
        if(support){
            doProcess(activityEntity, activityBatch, activityCustomReward, customerDO);
        }
    }

    abstract boolean support(ActivityEntity activityEntity, ActivityBatch activityBatch, CustomerCacheDTO customerDO);

    abstract void doProcess(ActivityEntity activityEntity, ActivityBatch activityBatch, ActivityCustomReward activityCustomReward, CustomerCacheDTO customerDO);

    protected String getSeq(ActivityConstant.SeqPrefix type, String batchId){
        return rewardSeq.of(type, batchId).getValue();
    }
}
