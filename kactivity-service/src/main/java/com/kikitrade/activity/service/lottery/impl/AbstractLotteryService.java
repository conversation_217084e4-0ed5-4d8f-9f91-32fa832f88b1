package com.kikitrade.activity.service.lottery.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCumulateItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCumulateItem;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.dal.tablestore.model.LotteryAward;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.ActivityCumulateConfig;
import com.kikitrade.activity.service.lottery.LotteryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Slf4j
public abstract class AbstractLotteryService implements LotteryService {

    @Resource
    private ActivityLotteryItemBuilder activityLotteryItemBuilder;

    @Resource
    private ActivityCumulateItemBuilder activityCumulateItemBuilder;

    @Override
    public ActivityLotteryItem saveLotteryItem(String customerId, String code, String saasId, LotteryAward lotteryAward, LotteryConfig lotteryConfig) {
        ActivityLotteryItem lotteryItem = new ActivityLotteryItem();
        lotteryItem.setId(IdUtil.objectId());
        lotteryItem.setCustomerId(customerId);
        lotteryItem.setCode(code);
        lotteryItem.setPoolId(lotteryConfig.getId());
        lotteryItem.setDrewTime(OffsetDateTime.now().toEpochSecond());
        lotteryItem.setRewardName(lotteryAward.getName());
        lotteryItem.setAmount(new BigDecimal(lotteryAward.getAmount()));
        lotteryItem.setCurrency(lotteryAward.getCurrency());
        lotteryItem.setStatus(ActivityConstant.LotteryStatus.APPENDING.name());
        lotteryItem.setCreated(System.currentTimeMillis());
        lotteryItem.setSaasId(saasId);
        lotteryItem.setCumulate(BooleanUtils.isTrue(lotteryConfig.getIsCumulate()));

        activityLotteryItemBuilder.insert(lotteryItem);
        return lotteryItem;
    }
}
