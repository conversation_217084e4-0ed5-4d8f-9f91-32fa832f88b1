package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityRosterImportService;
import com.kikitrade.activity.service.importing.roster.ActivityRosterLauncher;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.job.ElasticJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
@Slf4j
public class ActivityRosterImportServiceImpl implements ActivityRosterImportService {
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ElasticJobService elasticJobService;
    @Resource
    @Lazy
    private ActivityRosterLauncher activityRosterLauncher;
    @Resource
    private RedisService redisService;

    public void run(String batchId, String activityId){
        execute(batchId, activityId);
    }

    @Override
    public void execute(String batchId, String activityId) {
        ActivityBatch batch = activityBatchNewService.findByBatchId(batchId);
        if (batch == null) {
            elasticJobService.removeJob(elasticJobService.getJobNameForImport(batchId));
            return;
        }
        if(ActivityConstant.BatchRewardStatusEnum.IMPORTING.isEquals(batch.getRewardStatus()) && StringUtils.isNotBlank(batch.getModified())
            && TimeUtil.addDay(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)), -1).compareTo(TimeUtil.parse(batch.getModified())) >= 0){
            elasticJobService.removeJob(elasticJobService.getJobNameForImport(batchId));
            activityBatchNewService.updateBatchStatus(batchId, ActivityConstant.BatchRewardStatusEnum.IMPORT_FAILED.name());
            return;
        }
        String lock = null;
        try {
            lock = redisService.lock("IMPORT:LOCK:" + batchId, 180);
            if(StringUtils.isBlank(lock)){
                log.info("批次：");
                return;
            }
            LauncherParameter launcherDomain = new LauncherParameter();
            launcherDomain.setBatch(batch);
            activityRosterLauncher.run(launcherDomain);
        } catch (Exception ex) {
            log.error("导入失败", ex);
            activityBatchNewService.updateBatchStatus(batchId, ActivityConstant.BatchRewardStatusEnum.IMPORT_FAILED.name());
        }finally {
            if(StringUtils.isNotBlank(lock)){
                log.info(elasticJobService.getJobNameForImport(batchId) + "_finish");
                redisService.unLock(lock);
            }
        }
    }
}
