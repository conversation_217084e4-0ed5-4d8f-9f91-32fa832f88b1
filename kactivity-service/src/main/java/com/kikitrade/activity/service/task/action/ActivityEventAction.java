package com.kikitrade.activity.service.task.action;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.ActivityTaskStatisService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.TaskFilter;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class ActivityEventAction {

    @Resource
    private ActivityTaskService activityTaskService;
    @Resource
    private TaskConfigService taskConfigService;
    @Autowired
    private KiKiMonitor kiKiMonitor;
    @Resource
    private ActivityTaskStatisService activityTaskStatisService;

    public ActivityResponse<List<Award>> action(ActivityEventMessage activityEventMessage) throws Exception {
        String saasId = String.valueOf(activityEventMessage.getBody().get("saasId"));
        String eventCode = activityEventMessage.getEventCode();
        String customerId = activityEventMessage.getCustomerId();
        log.info("user_track [task] start [{}]", eventCode);

        if (saasId != null && eventCode != null) {
            kiKiMonitor.monitor(BusinessMonitorConstant.EVENT, new String[]{"saasId", saasId, "code", eventCode, "stage", "listener"});
        }

        List<TaskConfigDTO> taskConfigs = getTaskConfigs(activityEventMessage, saasId);
        log.info("[task] eventAction taskConfig: customerId={}, taskConfigs={}", customerId, JSON.toJSONString(taskConfigs));

        if (CollectionUtils.isEmpty(taskConfigs)) {
            if (saasId != null && eventCode != null) {
                kiKiMonitor.monitor(BusinessMonitorConstant.EVENT, new String[]{"saasId", saasId, "code", eventCode, "stage", "expired"});
            }
            return ActivityResponse.<List<Award>>builder().code(ActivityResponseCode.TASK_EXPIRED).build();
        }

        ActivityResponse<List<Award>> award = null;
        for (TaskConfigDTO config : taskConfigs) {
            log.info("user_track [task] [{},{}]", eventCode, config.getTaskId());
            TaskCodeConfig taskCodeConfig = TaskCodeConfig.getValue(config.getCode());
            ActivityConstant.VipLevelEnum vipLevel = getVipLevel(activityEventMessage);

            if (!isValidVipLevel(config, vipLevel)) {
                continue;
            }

            if (taskCodeConfig == null) {
                log.warn("[task] eventAction, event_not_found: customerId={}, eventCode={}", customerId, eventCode);
                return ActivityResponse.<List<Award>>builder().code(ActivityResponseCode.TASK_EVENT_NOT_FOUND).build();
            }

            if (!isValidCheckReward(config, activityEventMessage)) {
                continue;
            }

            try {
                award = processTask(config, activityEventMessage, taskCodeConfig);
            } catch (Exception ex) {
                log.error("[task] eventAction error: activityEventMessage={}, exception={}", activityEventMessage, ex);
            }
        }
        log.info("user_track [task] end [{}]", eventCode);

        return ActivityResponse.<List<Award>>builder().code(ActivityResponseCode.SUCCESS).obj(award != null ? award.getObj() : null).build();
    }

    private List<TaskConfigDTO> getTaskConfigs(ActivityEventMessage activityEventMessage, String saasId) {
        List<TaskConfigDTO> taskConfigs = new ArrayList<>();
        Object taskId = activityEventMessage.getBody().get("taskId");

        if (taskId != null) {
            TaskConfigDTO configDTO = taskConfigService.findByTaskIdAndWhiteFlag(String.valueOf(taskId), activityEventMessage.getCustomerId(), ActivityTaskConstant.TaskConfigScope.FILTER);
            if (configDTO != null) {
                taskConfigs.add(configDTO);
            }
        } else {
            List<TaskConfigDTO> configDTOS = taskConfigService.findByTaskCodeAndWhiteFlag(saasId, activityEventMessage.getEventCode(), activityEventMessage.getCustomerId(), ActivityTaskConstant.TaskConfigScope.FILTER);
            if (CollectionUtils.isNotEmpty(configDTOS)) {
                taskConfigs.addAll(configDTOS);
            }
        }

        return taskConfigs;
    }

    private ActivityConstant.VipLevelEnum getVipLevel(ActivityEventMessage activityEventMessage) {
        return activityEventMessage.getBody().get("vipLevel") == null ? ActivityConstant.VipLevelEnum.NORMAL
                : ActivityConstant.VipLevelEnum.valueOf(String.valueOf(activityEventMessage.getBody().get("vipLevel")));
    }

    private boolean isValidVipLevel(TaskConfigDTO config, ActivityConstant.VipLevelEnum vipLevel) {
        if (config.getVipLevel() != null) {
            int configVipLevel = Integer.parseInt(config.getVipLevel().replace("+", ""));
            if (config.getVipLevel().startsWith("+")) {
                return configVipLevel <= vipLevel.getLevel();
            } else {
                return configVipLevel == vipLevel.getLevel();
            }
        }
        return true;
    }

    private boolean isValidCheckReward(TaskConfigDTO config, ActivityEventMessage activityEventMessage) {
        String checkReward = config.getCheckReward();
        if (!Objects.isNull(checkReward)) {
            EvaluationContext context = new StandardEvaluationContext();
            context.setVariable("map", activityEventMessage.getBody());
            Boolean value = new SpelExpressionParser().parseExpression(checkReward).getValue(context, Boolean.class);
            log.info("[task] eventAction, checkReward: checkReward={}, value={}", checkReward, value);
            return value;
        }
        return true;
    }

    private ActivityResponse<List<Award>> processTask(TaskConfigDTO config, ActivityEventMessage activityEventMessage, TaskCodeConfig eventCode) throws Exception {
        if (StringUtils.isBlank(config.getType()) || ActivityTaskConstant.TaskType.NORMAL.name().equalsIgnoreCase(config.getType())) {
            ActivityEventMessage activityEventMessageA = BeanUtil.copyProperties(activityEventMessage, ActivityEventMessage::new);
            return fillTask(config, activityEventMessageA, eventCode);
        } else if (ActivityTaskConstant.TaskType.STATIS.name().equalsIgnoreCase(config.getType())) {
            ActivityEventMessage activityEventMessageB = BeanUtil.copyProperties(activityEventMessage, ActivityEventMessage::new);
            Long progress = incrementProgress(config, activityEventMessageB, eventCode);
            if (progress % Long.parseLong(config.getAttr().get("accumulate")) == 0) {
                activityTaskService.doTask(activityEventMessage, config, eventCode);
                log.info("[task] eventAction success: customerId={}, eventCode={}", activityEventMessage.getCustomerId(), activityEventMessage.getEventCode());
            }
        }
        return null;
    }

    /**
     * 完成任务
     * @param config
     * @param activityEventMessage
     * @param eventCode
     * @return
     * @throws Exception
     */
    private ActivityResponse<List<Award>> fillTask(TaskConfigDTO config, ActivityEventMessage activityEventMessage, TaskCodeConfig eventCode) throws Exception {
        if("liked".equalsIgnoreCase(config.getCode()) || "followed".equalsIgnoreCase(config.getCode())){
            if(StringUtils.isBlank(activityEventMessage.getSourceCustomerId())){
                log.warn("[task] eventAction, source_customer is null,{},{}", activityEventMessage.getCustomerId(), config.getCode());
                ActivityResponse.ActivityResponseBuilder<List<Award>> builder = ActivityResponse.builder();
                return builder.code(ActivityResponseCode.INVALID_PARAMETER).build();
            }
            activityEventMessage.setTargetId(activityEventMessage.getTargetId() + "=" + activityEventMessage.getSourceCustomerId());
        }


        //获取前置校验
        List<TaskFilter> taskFilterList = config.getFilters();
        for(TaskFilter taskFilter : taskFilterList){
            taskFilter.filter(activityEventMessage, config);
        }
        //做任务
        List<Award> award = activityTaskService.doTask(activityEventMessage, config, eventCode);
        log.info("[task] eventAction success,{},{}", activityEventMessage.getCustomerId(), activityEventMessage.getEventCode());
        ActivityResponse.ActivityResponseBuilder<List<Award>> builder = ActivityResponse.builder();
        return builder.code(ActivityResponseCode.SUCCESS).obj(award).build();
    }

    private Long incrementProgress(TaskConfigDTO config, ActivityEventMessage activityEventMessage, TaskCodeConfig eventCode) throws Exception {
        return activityTaskStatisService.incrementProgress(config, activityEventMessage, eventCode);
    }
}
