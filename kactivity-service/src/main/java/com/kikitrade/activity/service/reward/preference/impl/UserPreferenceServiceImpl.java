package com.kikitrade.activity.service.reward.preference.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kikitrade.activity.dal.tablestore.builder.UserLotteryProfileBuilder;
import com.kikitrade.activity.dal.tablestore.model.UserLotteryProfile;
import com.kikitrade.activity.service.reward.preference.UserPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户偏好管理服务实现
 * 基于UserLotteryProfile存储用户偏好数据，使用JSON格式存储多种偏好类型
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class UserPreferenceServiceImpl implements UserPreferenceService {
    
    @Resource
    private UserLotteryProfileBuilder userLotteryProfileBuilder;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public boolean setUserPreference(String userId, String saasId, String preferenceType, String preferenceValue) {
        log.info("设置用户偏好: userId={}, saasId={}, type={}, value={}", 
                userId, saasId, preferenceType, preferenceValue);
        
        try {
            UserLotteryProfile profile = getOrCreateUserProfile(userId, saasId);
            
            // 解析现有偏好
            Map<String, String> preferences = parsePreferences(profile.getUserPreferences());
            
            // 设置新的偏好值
            if (preferenceValue != null) {
                preferences.put(preferenceType, preferenceValue);
            } else {
                preferences.remove(preferenceType);
            }
            
            // 序列化偏好数据
            profile.setUserPreferences(serializePreferences(preferences));
            profile.setUpdateTime(System.currentTimeMillis());
            
            // 保存到数据库
            boolean success;
            if (profile.getCreateTime() == null) {
                // 新建档案
                profile.setCreateTime(System.currentTimeMillis());
                success = userLotteryProfileBuilder.insert(profile);
            } else {
                // 更新档案
                success = userLotteryProfileBuilder.update(profile);
            }
            
            if (success) {
                log.info("用户偏好设置成功: userId={}, type={}", userId, preferenceType);
            } else {
                log.warn("用户偏好设置失败: userId={}, type={}", userId, preferenceType);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("设置用户偏好异常: userId={}, type={}, value={}", 
                    userId, preferenceType, preferenceValue, e);
            return false;
        }
    }
    
    @Override
    public String getUserPreference(String userId, String saasId, String preferenceType) {
        log.debug("获取用户偏好: userId={}, saasId={}, type={}", userId, saasId, preferenceType);
        
        try {
            UserLotteryProfile profile = userLotteryProfileBuilder.findByUserId(userId, saasId);
            if (profile == null) {
                log.debug("用户档案不存在: userId={}, saasId={}", userId, saasId);
                return null;
            }
            
            Map<String, String> preferences = parsePreferences(profile.getUserPreferences());
            String value = preferences.get(preferenceType);
            
            log.debug("获取用户偏好结果: userId={}, type={}, value={}", userId, preferenceType, value);
            return value;
            
        } catch (Exception e) {
            log.error("获取用户偏好异常: userId={}, type={}", userId, preferenceType, e);
            return null;
        }
    }
    
    @Override
    public Map<String, String> getAllUserPreferences(String userId, String saasId) {
        log.debug("获取用户所有偏好: userId={}, saasId={}", userId, saasId);
        
        try {
            UserLotteryProfile profile = userLotteryProfileBuilder.findByUserId(userId, saasId);
            if (profile == null) {
                log.debug("用户档案不存在: userId={}, saasId={}", userId, saasId);
                return Collections.emptyMap();
            }
            
            Map<String, String> preferences = parsePreferences(profile.getUserPreferences());
            log.debug("获取用户所有偏好结果: userId={}, count={}", userId, preferences.size());
            return preferences;
            
        } catch (Exception e) {
            log.error("获取用户所有偏好异常: userId={}", userId, e);
            return Collections.emptyMap();
        }
    }
    
    @Override
    public boolean removeUserPreference(String userId, String saasId, String preferenceType) {
        log.info("删除用户偏好: userId={}, saasId={}, type={}", userId, saasId, preferenceType);
        
        // 设置为null即为删除
        return setUserPreference(userId, saasId, preferenceType, null);
    }
    
    @Override
    public boolean clearAllUserPreferences(String userId, String saasId) {
        log.info("清空用户所有偏好: userId={}, saasId={}", userId, saasId);
        
        try {
            UserLotteryProfile profile = userLotteryProfileBuilder.findByUserId(userId, saasId);
            if (profile == null) {
                log.debug("用户档案不存在，无需清空: userId={}, saasId={}", userId, saasId);
                return true;
            }
            
            // 清空偏好数据
            profile.setUserPreferences(null);
            profile.setUpdateTime(System.currentTimeMillis());
            
            boolean success = userLotteryProfileBuilder.update(profile);
            if (success) {
                log.info("清空用户偏好成功: userId={}", userId);
            } else {
                log.warn("清空用户偏好失败: userId={}", userId);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("清空用户偏好异常: userId={}", userId, e);
            return false;
        }
    }
    
    /**
     * 获取或创建用户档案
     */
    private UserLotteryProfile getOrCreateUserProfile(String userId, String saasId) {
        UserLotteryProfile profile = userLotteryProfileBuilder.findByUserId(userId, saasId);
        if (profile == null) {
            profile = new UserLotteryProfile();
            profile.setId(generateProfileId(userId, saasId));
            profile.setUserId(userId);
            profile.setSaasId(saasId);
            profile.setTotalDrawCount(0);
            profile.setTotalWinCount(0);
            profile.setLastDrawTime(0L);
            log.debug("创建新的用户档案: userId={}, saasId={}", userId, saasId);
        }
        return profile;
    }
    
    /**
     * 生成用户档案ID
     */
    private String generateProfileId(String userId, String saasId) {
        return saasId + "_" + userId + "_" + System.currentTimeMillis();
    }
    
    /**
     * 解析偏好JSON字符串为Map
     */
    private Map<String, String> parsePreferences(String preferencesJson) {
        if (!StringUtils.hasText(preferencesJson)) {
            return new HashMap<>();
        }
        
        try {
            return objectMapper.readValue(preferencesJson, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            log.warn("解析用户偏好JSON失败，返回空Map: json={}", preferencesJson, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 序列化偏好Map为JSON字符串
     */
    private String serializePreferences(Map<String, String> preferences) {
        if (preferences == null || preferences.isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(preferences);
        } catch (Exception e) {
            log.error("序列化用户偏好失败: preferences={}", preferences, e);
            return null;
        }
    }
}
