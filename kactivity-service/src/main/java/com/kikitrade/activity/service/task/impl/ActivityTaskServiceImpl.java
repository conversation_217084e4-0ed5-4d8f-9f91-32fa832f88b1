package com.kikitrade.activity.service.task.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.openservices.tablestore.TableStoreException;
import com.google.common.collect.Lists;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.TaskSubmitDTO;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.dao.ActivityTaskDao;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.CouponConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.SaharaTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.dal.tablestore.model.CouponConfig;
import com.kikitrade.activity.dal.tablestore.model.SaharaTaskItem;
import com.kikitrade.activity.facade.award.ActivityDTO;
import com.kikitrade.activity.facade.award.ActivityStatusEnum;
import com.kikitrade.activity.facade.award.ActivityTypeEnum;
import com.kikitrade.activity.facade.award.RewardRule;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TaskUtil;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.auth.AuthService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.business.ThreePlatformFilter;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.VerifyResult;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.ActivityTaskTccService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.redis.lock.RedisDistributedLock;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.member.api.RemoteMemberLevelService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kikitrade.activity.model.Result.ResultCode.CHECK_FAILED;
import static com.kikitrade.activity.model.Result.ResultCode.PARAM_INVALID;
import static com.kikitrade.activity.model.constant.ActivityTaskConstant.TaskCodeEnum.create_dapp;
import static com.kikitrade.activity.model.constant.ActivityTaskConstant.TaskCodeEnum.osp_callback;

@Service
@Slf4j
public class ActivityTaskServiceImpl implements ActivityTaskService {

    private static final String TARGET_HANDLE = "targetHandle";

    @Resource
    private ActivityTaskDao activityTaskDao;
    @Resource
    private RedisService redisService;
    @Resource
    private ActivityEntityService activityEntityService;
    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;
    @DubboReference
    private RemoteMemberLevelService remoteMemberLevelService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private ActivityTaskTccService activityTaskTccService;
    @Resource
    private CustomerService customerService;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private AuthService authService;
    @Resource
    private ThreePlatformFilter threePlatformFilter;
    @Autowired(required = false)
    private EventClient eventClient;
    @Resource
    private UploadOssUtil uploadOssUtil;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource(name = "activityPoolTaskListExecutor")
    private ThreadPoolTaskExecutor activityPoolTaskListExecutor;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private ThreePlatformProperties threePlatformProperties;
    @Resource
    private CouponConfigBuilder couponConfigBuilder;
    @Autowired
    private TaskConfigServiceImpl taskConfigServiceImpl;
    @Resource
    private RedisDistributedLock redisDistributedLock;
    @Resource
    private SaharaTaskItemBuilder saharaTaskItemBuilder;

    private static final String TASK_CODE_PREFIX = "TASK_CODE_";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityTaskConfig insert(ActivityTaskConfig activityTaskConfig) {
        log.info("activityTaskConfig:{}", activityTaskConfig);
        ActivityDTO.Builder activityDTOBuild = ActivityDTO.newBuilder()
                .setActivityName(activityTaskConfig.getNameEn())
                .setType(ActivityTypeEnum.CUSTOMIZE)
                .setStartTime(TimeUtil.getUtcTime(activityTaskConfig.getStartTime(), TimeUtil.YYYYMMDDHHMMSS))
                .setRemark(activityTaskConfig.getDescEn())
                .setAutoCreateBatch(false)
                .setStatus(ActivityStatusEnum.ACTIVE)
                .addAllRewardRule(Collections.singletonList(RewardRule.newBuilder().setMin("1").setAwardAmount(activityTaskConfig.getAmount().toPlainString()).setAward(ActivityConstant.AwardTypeEnum.POINT.name()).build()))
                ;
        ActivityDTO.Builder builder = activityDTOBuild.setEndTime(activityTaskConfig.getEndTime() == null ?
                TimeUtil.getUtcTime(TimeUtil.addMonth(activityTaskConfig.getStartTime(), 1200),TimeUtil.YYYYMMDDHHMMSS) : TimeUtil.getUtcTime(activityTaskConfig.getEndTime(), TimeUtil.YYYYMMDDHHMMSS));
        Result<String> activityResult = activityEntityService.save(builder.build(),  ActivityConstant.ActivitySourceEnum.TASK ,null, null);
        activityTaskConfig.setActivityId(activityResult.getData());
        activityTaskConfig.setModified(new Date());
        activityTaskConfig.setCreated(new Date());
        activityTaskConfig.setSaasId(kactivityProperties.getSaasId());
        activityTaskDao.insert(activityTaskConfig);
        return activityTaskConfig;
    }

    @Override
    @Transactional
    public ActivityTaskConfig update(ActivityTaskConfig activityTaskConfig) {
        ActivityTaskConfig config = activityTaskDao.selectByPrimaryKey(activityTaskConfig.getId());
        mergeActivityTaskConfig(activityTaskConfig, config);
        int result = activityTaskDao.updateByPrimaryKey(config);
        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setId(config.getActivityId());
        activityEntity.setName(activityTaskConfig.getNameEn());
        activityEntity.setRemark(activityTaskConfig.getDescEn());
        activityEntityService.update(activityEntity);
        return config;
    }

    /**
     * @param request
     * @return
     */
    @Override
    public List<TaskListResponse> taskList(TaskListRequest request) {
        log.info("taskList request:{}", JSON.toJSONString(request));
        boolean taskWhiteFlag = StringUtils.isNotBlank(request.getCustomerId()) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), request.getCustomerId()) != null;
        ActivityConstant.VipLevelEnum vipLevelEnum = request.getVipLevel() == null ? ActivityConstant.VipLevelEnum.NORMAL : ActivityConstant.VipLevelEnum.valueOf(request.getVipLevel());
        List<TaskConfigDTO> configDTOS = taskConfigService.findTaskBySaasId(request.getSaasId(), request.getChannel(), request.getPosition(), request.getClientType(), taskWhiteFlag);
        List<TaskListResponse> responses = new ArrayList<>();
        if (CollectionUtils.isEmpty(configDTOS)) {
            return responses;
        }
        Map<String, List<String>> cycleMap = new HashMap<>();
        Map<String, List<String>> postCycleMap = new HashMap<>();
        for (TaskConfigDTO dto : configDTOS) {
            if (!isValidVipLevel(dto, vipLevelEnum)) {
                continue;
            }
            TaskListResponse response = createTaskListResponse(dto, request, vipLevelEnum);
            updateCycleMaps(cycleMap, postCycleMap, response);
            handleTwitterDomain(dto, response, request);
            handleAppLinks(dto, response);
            handleTaskStatus(dto, request, response, vipLevelEnum);
            responses.add(response);
        }
        buildTaskStatus(responses, cycleMap, request.getCustomerId());
        buildPostTaskStatus(responses, postCycleMap, request.getCustomerId());
        responses = filterCompleted(responses);
        return responses.stream().sorted(Comparator.comparingInt(TaskListResponse::getOrder)).collect(Collectors.toList());
    }

    private List<TaskListResponse> filterCompleted(List<TaskListResponse> responses){
        List<TaskListResponse> res = new  ArrayList<>();
        for(TaskListResponse task : responses){
            if(task.getHiddenTaskCompleted() && task.getStatus().equals(ActivityConstant.TaskCompleteStatus.DONE.getStatus())){
                continue;
            }
            res.add(task);
        }
        return res;
    }

    private boolean isValidVipLevel(TaskConfigDTO dto, ActivityConstant.VipLevelEnum vipLevelEnum) {
        if (!dto.getVipLevel().startsWith("+") && Integer.parseInt(dto.getVipLevel()) != vipLevelEnum.getLevel()) {
            return false;
        }
        return !dto.getVipLevel().startsWith("+") || Integer.parseInt(dto.getVipLevel()) <= vipLevelEnum.getLevel();
    }

    private TaskListResponse createTaskListResponse(TaskConfigDTO dto, TaskListRequest request, ActivityConstant.VipLevelEnum vipLevelEnum) {
        TaskListResponse response = BeanUtil.copyProperties(dto, TaskListResponse.class);
        if (dto.getImage() != null && !dto.getImage().isEmpty()) {
            Map<String, String> imageMap = new HashMap<>();
            dto.getImage().forEach((key, value) -> imageMap.put(key, uploadOssUtil.getLocation(value)));
            response.setImage(imageMap);
        }
        response.setTitle(parseTitle(dto.getTitle(), request.getChannel()));
        response.setDesc(parseDesc(dto.getDesc(), vipLevelEnum, request.getChannel()));
        response.setLimit(dto.getLimit(vipLevelEnum.name()));
        response.setCycle(TaskCycleDomain.getCurrencyCycle(dto, null));
        response.setCycleEnum(dto.getCycle());
        response.setEndTime(dto.getEndTime());
        String code = dto.getShowCode() == null ? dto.getCode() : dto.getShowCode();
        response.setIcon(uploadOssUtil.getLocation("quests/" + dto.getSaasId(), code + "." + SaasConfigLoader.getConfig(dto.getSaasId()).getIconSuffix()));
        response.setPostTaskId(dto.getPostTaskId());
        response.setPostTaskCode(dto.getPostTaskCode());
        response.setPostTaskDesc(dto.getPostTaskDesc());
        response.setPostTaskReward(dto.getPostTaskReward());
        if (dto.getReward() != null) {
            List<Award> awards = dto.getReward().entrySet().iterator().next().getValue();
            Award award = awards.stream().filter(r -> r.getVipLevel().equals(vipLevelEnum.name())).findFirst().get();
            response.setShowReward(NumberUtils.isDigits(award.getAmount()) ? new BigDecimal(award.getAmount()) : (award.getShowAmount() != null ? new BigDecimal(award.getShowAmount()) : BigDecimal.ZERO));
        }
        if (dto.getShowCode() != null) {
            response.setCode(dto.getShowCode());
        }
        if ("pc".equals(request.getChannel())) {
            response.setConnectUrl(dto.getConnectUrlPc());
        } else {
            response.setConnectUrl(dto.getConnectUrl());
        }
        response.setHiddenTaskCompleted(BooleanUtils.isTrue(dto.getHiddenTaskCompleted()));
        return response;
    }

    private void updateCycleMaps(Map<String, List<String>> cycleMap, Map<String, List<String>> postCycleMap, TaskListResponse response) {
        cycleMap.computeIfAbsent(response.getCycle(), k -> new ArrayList<>()).add(response.getTaskId());
        if (response.getPostTaskId() != null) {
            postCycleMap.computeIfAbsent(response.getCycle(), k -> new ArrayList<>()).add(response.getPostTaskId());
        }
    }

    private void handleTwitterDomain(TaskConfigDTO dto, TaskListResponse response, TaskListRequest request) {
        if ("twitter".equalsIgnoreCase(dto.getDomain()) || "x".equalsIgnoreCase(dto.getDomain())) {
            if (StringUtils.isBlank(response.getConnectUrl())) {
                if ("pc".equals(request.getChannel())) {
                    response.setConnectUrl(String.format("%s&client_id=%s&redirect_uri=%s", threePlatformProperties.getTwitter().getAuthScore().get(dto.getSaasId()), threePlatformProperties.getTwitter().getClientId().get(dto.getSaasId()), threePlatformProperties.getTwitter().getPcRedirectUri().get(dto.getSaasId())));
                } else {
                    response.setConnectUrl(String.format("%s&client_id=%s", threePlatformProperties.getTwitter().getAuthScore().get(dto.getSaasId()), threePlatformProperties.getTwitter().getClientId().get(dto.getSaasId())));
                }
            }
            if (dto.getAttr() != null && StringUtils.isNotBlank(response.getUrl())) {
                String text = dto.getAttr().get("twitter-random-text");
                String appendText = dto.getAttr().get("twitter-random-append-text");
                if (StringUtils.isNotBlank(text)) {
                    List<String> textList = JSON.parseArray(text, String.class);
                    response.setUrl(response.getUrl().substring(0, response.getUrl().indexOf("?text=") + 6) + textList.get(RandomUtil.randomInt(textList.size())));
                } else if (StringUtils.isNotBlank(appendText)) {
                    List<String> textList = JSON.parseArray(appendText, String.class);
                    response.setUrl(response.getUrl() + "%0A" + textList.get(RandomUtil.randomInt(textList.size())));
                }
            }
        }
    }

    private void handleAppLinks(TaskConfigDTO dto, TaskListResponse response) {
        Map<String, String> link = response.getLink();
        if (link != null) {
            for (Map.Entry<String, String> entry : link.entrySet()) {
                if (entry.getValue().startsWith("https://twitter.com/compose/post")) {
                    String text = dto.getAttr().get("twitter-random-text");
                    String appendText = dto.getAttr().get("twitter-random-append-text");
                    if (StringUtils.isNotBlank(text)) {
                        List<String> textList = JSON.parseArray(text, String.class);
                        link.put(entry.getKey(), entry.getValue().substring(0, entry.getValue().indexOf("?text=") + 6) + textList.get(RandomUtil.randomInt(textList.size())));
                    } else if (StringUtils.isNotBlank(appendText)) {
                        List<String> textList = JSON.parseArray(appendText, String.class);
                        link.put(entry.getKey(), entry.getValue() + "%0A" + textList.get(RandomUtil.randomInt(textList.size())));
                    }
                }
            }
        }
    }

    private void handleTaskStatus(TaskConfigDTO dto, TaskListRequest request, TaskListResponse response, ActivityConstant.VipLevelEnum vipLevelEnum) {
        if (dto.getGroupId() != null) {
            List<TaskConfigDTO> dtos = taskConfigService.findByGroupTaskId(dto.getGroupId(), false);
            if (CollectionUtils.isNotEmpty(dtos)) {
                Map<String, ActivityTaskItem> taskItemsMap = new HashMap<>();
                if (request.getCustomerId() != null) {
                    List<String> taskIds = dtos.stream().map(TaskConfigDTO::getTaskId).collect(Collectors.toList());
                    List<ActivityTaskItem> taskItems = activityTaskItemBuilder.findByCustomer(request.getCustomerId(), TaskCycleDomain.getCurrencyCycle(dtos.get(0), null), taskIds);
                    log.info("taskList customerId:{}, taskIds:{}, activityItems:{}", request.getCustomerId(), JSON.toJSONString(taskIds), JSON.toJSONString(taskItems));
                    if (CollectionUtils.isNotEmpty(taskItems)) {
                        taskItemsMap = taskItems.stream().collect(Collectors.toMap(ActivityTaskItem::getTaskId, Function.identity(), (v1, v2) -> v2));
                    }
                }
                List<TaskVO> subTasks = new ArrayList<>();
                for (TaskConfigDTO subConfig : dtos) {
                    TaskVO taskVO = createTaskVO(subConfig, taskItemsMap, vipLevelEnum, request);
                    subTasks.add(taskVO);
                }
                response.setSubTasks(subTasks);
            }
        }
        response.setTodayTaskStatus(0);
        if (dto.getCycle() == ActivityTaskConstant.TaskCycleEnum.once_daily) {
            String targetId = TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD);
            if (request.getCustomerId() != null) {
                ActivityTaskItem taskItem = activityTaskItemBuilder.findDetailByCustomer(request.getCustomerId(),
                    TaskCycleDomain.getCurrencyCycle(dto, null), dto.getTaskId(), targetId);
                if (taskItem != null) {
                    response.setTodayTaskStatus(1);
                }
            }
        }
        response.setStatus(0);
        response.setProgress(0);
    }

    private TaskVO createTaskVO(TaskConfigDTO subConfig, Map<String, ActivityTaskItem> taskItemsMap, ActivityConstant.VipLevelEnum vipLevelEnum, TaskListRequest request) {
        TaskVO taskVO = new TaskVO();
        taskVO.setStatus(0);
        taskVO.setLimit(subConfig.getLimit(vipLevelEnum.name()));
        ActivityTaskItem taskItem = taskItemsMap.get(subConfig.getTaskId());
        if (taskItem != null) {
            String businessId = taskItem.getBusinessId() + taskItem.getProgress() / subConfig.getRewardFrequency();
            ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(businessId);
            if (reward != null && ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())) {
                if (Arrays.asList("deek-tg", "dojo3-tg").contains(subConfig.getSaasId())) {
                    taskVO.setRewardStatus(1);
                    taskVO.setStatus(1);
                } else if (reward.getAmount() != null && new BigDecimal(reward.getAmount()).compareTo(BigDecimal.ZERO) > 0) {
                    taskVO.setRewardStatus(1);
                    taskVO.setStatus(1);
                }
            }
        }
        taskVO.setTitle(parseTitle(subConfig.getTitle(), request.getChannel()));
        taskVO.setDesc(parseDesc(subConfig.getDesc(), vipLevelEnum, request.getChannel()));
        taskVO.setTaskId(subConfig.getTaskId());
        return taskVO;
    }

    private void buildTaskStatus(List<TaskListResponse> tasks, Map<String, List<String>> cycleMap, String customerId){
        if(customerId == null){
            return;
        }
        List<ActivityTaskItem> taskItems = new ArrayList<>();
        for(Map.Entry<String, List<String>> entry : cycleMap.entrySet()){
            List<ActivityTaskItem> taskItem = activityTaskItemBuilder.findByCustomer(customerId, entry.getKey(), entry.getValue());
            if(CollectionUtils.isNotEmpty(taskItem)){
                taskItems.addAll(taskItem);
            }
        }
        Map<String, ActivityTaskItem> taskItemMap = taskItems.stream().collect(Collectors.toMap(t -> t.getTaskId(), Function.identity(), (t1, t2) ->
            t1.getCycle().compareTo(t2.getCycle()) > 0 ? t1 : t2));
        for(TaskListResponse task : tasks){
            ActivityTaskItem item = taskItemMap.get(task.getTaskId());
            if (Objects.nonNull(item)) {
                task.setProgress(item.getProgress());
                if (task.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series) {
                    task.setProgress(item.getProgress() % task.getRewardFrequency());
                }
            }
            task.setStatus(item != null && item.getProgress() >= task.getLimit() ? ActivityConstant.TaskCompleteStatus.DONE.getStatus() : ActivityConstant.TaskCompleteStatus.APPENDING.getStatus());
            if (Objects.nonNull(item) && Objects.nonNull(item.getCompleteTime())) {
                Date completeTime = TimeUtil.parse(item.getCompleteTime(), new SimpleDateFormat(TimeUtil.YYYYMMDDHHMMSS));
                task.setCompleteTime(Objects.nonNull(completeTime) ? completeTime.getTime() : 0L);
            }
        }
    }

    private void buildPostTaskStatus(List<TaskListResponse> tasks, Map<String, List<String>> cycleMap, String customerId){
        if(customerId == null){
            return;
        }
        List<ActivityTaskItem> taskItems = new ArrayList<>();
        for(Map.Entry<String, List<String>> entry : cycleMap.entrySet()){
            List<ActivityTaskItem> taskItem = activityTaskItemBuilder.findByCustomer(customerId, entry.getKey(), entry.getValue());
            if(CollectionUtils.isNotEmpty(taskItem)){
                taskItems.addAll(taskItem);
            }
        }
        Map<String, ActivityTaskItem> taskItemMap = taskItems.stream().collect(Collectors.toMap(t -> t.getTaskId(), Function.identity(), (t1, t2) ->
                t1.getCycle().compareTo(t2.getCycle()) > 0 ? t1 : t2));

        tasks.forEach(task -> {
            ActivityTaskItem item = taskItemMap.get(task.getPostTaskId());
            task.setPostTaskStatus(item != null && item.getProgress() >= task.getLimit() ? ActivityConstant.TaskCompleteStatus.DONE.getStatus() : ActivityConstant.TaskCompleteStatus.APPENDING.getStatus());
        });
    }

    private void buildTaskStatus(TaskListResponse response, TaskConfigDTO dto, String customerId, ActivityConstant.VipLevelEnum vipLevelEnum){
        ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(dto, null), dto.getTaskId());
        response.setProgress(taskItem != null ? taskItem.getProgress() : 0);
        response.setStatus(taskItem != null && taskItem.getProgress() >= dto.getLimit(vipLevelEnum.name()) ? ActivityConstant.TaskCompleteStatus.DONE.getStatus() : ActivityConstant.TaskCompleteStatus.APPENDING.getStatus());
    }

    @Override
    public TaskDetailResponse findByTaskId(String taskId, String customerId, ActivityConstant.VipLevelEnum vipLevelEnum, String channel) {
        boolean isGroup = TaskUtil.isGroupTask(taskId);
        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        if (Objects.isNull(taskConfigDTO)) {
            return null;
        }
        log.info("findByTaskId:{}", JSON.toJSONString(taskConfigDTO));
        boolean taskWhiteFlag = StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
        //4开头并且子任务不为空
        if (isGroup && !taskConfigDTO.getSubTask().isEmpty()) {
            TaskDetailResponse response = buildByTaskGroupId(taskConfigDTO, customerId, vipLevelEnum, channel,taskWhiteFlag);
            Map<String, String> image = new HashMap<>();
            if(MapUtils.isNotEmpty(response.getImage())){
                for(Map.Entry<String, String> entry : response.getImage().entrySet()){
                    image.put(entry.getKey(), uploadOssUtil.getLocation(entry.getValue()));
                }
            }
            response.setImage(image);
            return response;
        } else {
            //对于白名单用户做任务，修改任务的开始时间
            taskConfigDTO.setStartTime(fixTaskWhiteDate(taskConfigDTO,taskWhiteFlag));
            TaskDetailResponse response = buildByTaskId(taskConfigDTO, customerId);
            Map<String, String> image = new HashMap<>();
            if(MapUtils.isNotEmpty(response.getImage())){
                for(Map.Entry<String, String> entry : response.getImage().entrySet()){
                    image.put(entry.getKey(), uploadOssUtil.getLocation(entry.getValue()));
                }
            }
            response.setImage(image);
            return response;
        }
    }

    /**
     * 根据任务 code 查询任务
     *
     * @param taskCode
     * @param customerId
     * @return
     */
    @Override
    public TaskCodeDetailResponse findByTaskCode(String saasId, String taskCode, String customerId) {
        List<TaskConfigDTO> taskConfigDTOS = taskConfigService.findByTaskCodeAndWhiteFlag(saasId, taskCode, customerId);
        if(CollectionUtils.isEmpty(taskConfigDTOS)){
            return null;
        }
        log.info("findByTaskCode response:{}", taskConfigDTOS);
        TaskCodeDetailResponse response = new TaskCodeDetailResponse();
        TaskConfigDTO configDTO = taskConfigDTOS.get(0);
        String cycle = TaskCycleDomain.getCurrencyCycle(configDTO, null);
        TaskCompletedResult taskDetailResult = getTaskResult(customerId, cycle, configDTO.getTaskId());
        List<NodeVO> nodeList = new ArrayList<>(configDTO.getReward().size());
        Map<Integer, String> existAward = new HashMap<>();
        for(Map.Entry<String, List<Award>> award : configDTO.getReward().entrySet()){
            NodeVO nodeVO = new NodeVO();
            nodeVO.setIndex(Integer.valueOf(award.getKey()));
            nodeVO.setNodeLogo(1);
            existAward.put(Integer.valueOf(award.getKey()), "1");
            nodeList.add(nodeVO);
        }
        response.setTaskId(configDTO.getTaskId());
        response.setTitle(configDTO.getTitle());
        response.setDesc(configDTO.getDesc());
        response.setLabelName(configDTO.getLabelName());
        response.setLabelColor(configDTO.getLabelColor());
        if(MapUtils.isNotEmpty(configDTO.getImage())){
            response.setImage(new ArrayList<>(configDTO.getImage().values()));
        }
        if(taskDetailResult != null && taskDetailResult.getProgress() != null){
            response.setProcess(taskDetailResult.getProgress() % 7 == 0 ? 7 : taskDetailResult.getProgress() % 7);
        }
        response.setNodes(nodeList);
        ActivityTaskItem todayTask = activityTaskItemBuilder.findDetailByCustomer(customerId, cycle, configDTO.getTaskId(), TimeUtil.getCurrentUtcTime(TimeFormat.YYYYMMDD_PATTERN));
        response.setCheckIn(todayTask != null);
        return response;
    }

    private TaskDetailResponse buildByTaskGroupId(TaskConfigDTO taskConfigDTO, String customerId, ActivityConstant.VipLevelEnum vipLevelEnum, String channel, boolean taskWhiteFlag){
        //白名单用户做任务，返回任务详情时须修改任务开始时间
        taskConfigDTO.setStartTime(fixTaskWhiteDate(taskConfigDTO,taskWhiteFlag));
        TaskDetailResponse response = BeanUtil.copyProperties(taskConfigDTO, TaskDetailResponse.class);
        response.setPlatform(taskConfigDTO.getDomain());
        response.setDesc(parseDesc(taskConfigDTO.getDesc(), vipLevelEnum, channel));
        if(MapUtils.isNotEmpty(taskConfigDTO.getReward())){
            List<Award> awards = BeanUtil.copyToList(taskConfigDTO.getReward().get("0"), Award.class);
            response.setRewards(awards);
        }
        List<TaskConfigDTO> subTask = taskConfigDTO.getSubTask();
        List<TaskVO> taskVOS = new ArrayList<>();

        for(TaskConfigDTO taskConfig : subTask){
            TaskVO taskVO = new TaskVO();
            taskVO.setTaskId(taskConfig.getTaskId());
            taskVO.setTitle(parseTitle(taskConfig.getTitle(), channel));
            taskVO.setDesc(parseDesc(taskConfig.getDesc(), vipLevelEnum, channel));
            taskVO.setUrl(taskConfig.getUrl());
            taskVO.setSaasId(taskConfig.getSaasId());
            taskVO.setDomain(taskConfig.getDomain());
            taskVO.setStatus(0);
            taskVO.setCode(taskConfig.getShowCode() != null ? taskConfig.getShowCode() :  taskConfig.getCode());
            taskVO.setPlatform(taskConfig.getDomain());
            taskVO.setStartTime(fixTaskWhiteDate(taskConfig,taskWhiteFlag));
            taskVO.setEndTime(taskConfig.getEndTime());
            taskVO.setAttr(taskConfig.getAttr());
            if (MapUtils.isNotEmpty(taskConfig.getReward())) {
                List<Award> awards = BeanUtil.copyToList(taskConfig.getReward().get("0"), Award.class);
                taskVO.setRewards(awards);
            }
            if("pc".equals(channel)){
                taskVO.setConnectUrl(taskConfig.getConnectUrlPc());
            }else{
                taskVO.setConnectUrl(taskConfig.getConnectUrl());
            }
            if("twitter".equals(taskConfig.getDomain())){
                if("pc".equals(channel)){
                    taskVO.setConnectUrl(String.format("%s&client_id=%s&redirect_uri=%s", threePlatformProperties.getTwitter().getAuthScore().get(taskConfig.getSaasId()), threePlatformProperties.getTwitter().getClientId().get(taskConfig.getSaasId()), threePlatformProperties.getTwitter().getPcRedirectUri().get(taskConfig.getSaasId())));
                }else{
                    taskVO.setConnectUrl(String.format("%s&client_id=%s", threePlatformProperties.getTwitter().getAuthScore().get(taskConfig.getSaasId()), threePlatformProperties.getTwitter().getClientId().get(taskConfig.getSaasId())));
                }
                if(taskConfig.getAttr() != null && taskVO.getUrl() != null){
                    String text = taskConfig.getAttr().get("twitter-random-text");
                    String appendText = taskConfig.getAttr().get("twitter-random-append-text");
                    if(StringUtils.isNotBlank(text)){
                        taskVO.setUrl(taskVO.getUrl().substring(0, taskVO.getUrl().indexOf("?text=") + 6) + text);
                    } else if (StringUtils.isNotBlank(appendText)) {
                        taskVO.setUrl(taskVO.getUrl() + "%0A" + appendText);
                    }
                }
            }
            if(customerId != null){
                ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(taskConfig, null), taskConfig.getTaskId());
                taskVO.setStatus((taskItem != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus())) ? 1 : 0);
                taskVO.setRewardStatus(0);
                if(taskItem != null){
                    String businessId = taskItem.getBusinessId() + taskItem.getProgress()/taskConfig.getRewardFrequency();
                    ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(businessId);
                    if(reward != null && ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())){
                        taskVO.setRewardStatus(1);
                    }
                }
            }
            taskVOS.add(taskVO);
        }
        response.setRewardStatus(-1);
        boolean allMatch = taskVOS.stream().allMatch(taskVO -> taskVO.getStatus() == 1);
        if(allMatch){
            response.setRewardStatus(0);
            ActivityTaskItem taskItemAll = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
            if(taskItemAll != null){
                String seq = taskItemAll.getEvent() + ":" + taskItemAll.getBusinessId() + taskItemAll.getProgress()/taskConfigDTO.getRewardFrequency();
                ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByPrimaryId(TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), customerId, seq);
                if(reward != null && ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())){
                    response.setRewardStatus(1);
                }
            }
        }
        response.setStatus(taskVOS.stream().allMatch(t -> t.getStatus() == 1) ? 1 : 0);
        response.setSubTasks(taskVOS);
        response.setTotal(taskVOS.size());
        long count = taskVOS.stream().filter(taskVO -> taskVO.getStatus() == 1).count();
        response.setProcess(count > 0 ? (int)count : 0);
        return response;
    }

    private TaskDetailResponse buildByTaskId(TaskConfigDTO taskConfigDTO, String customerId){
        TaskDetailResponse response = BeanUtil.copyProperties(taskConfigDTO, TaskDetailResponse.class);
        if(MapUtils.isNotEmpty(taskConfigDTO.getReward())){
            List<Award> awards = new ArrayList<>();
            for (List<Award> awardList : taskConfigDTO.getReward().values()) {
                for (Award award : awardList) {
                    Award newAward = new Award();
                    BeanUtil.copyProperties(award, newAward);
                    awards.add(newAward);
                }
            }
            response.setRewards(awards);
        }
        response.setSubTasks(new ArrayList<>());
        if(customerId != null){
            ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
            if (taskItem != null) {
                response.setStatus(ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus()) ? 1 : 0);
            } else {
                response.setStatus(0);
            }
        }
        return response;
    }

    /**
     * 白名单人员做任务时，返回给前端的任务开始时间须提前
     * @param taskConfigDTO
     */
    private Long fixTaskWhiteDate(TaskConfigDTO taskConfigDTO, boolean taskWhiteFlag){
        if (taskWhiteFlag) {
            Long startTime = taskConfigDTO.getStartTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(startTime));
            calendar.add(Calendar.DAY_OF_MONTH, -kactivityProperties.getTaskWhiteDate());
            taskConfigDTO.setStartTime(calendar.getTime().getTime());
        }
        return taskConfigDTO.getStartTime();
    }

    /**
     * 做任务
     * @param activityEventMessage
     * @param taskConfig
     * @param codeConfig
     */
    @Override
    public List<Award> doTask(ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig) throws Exception {
        String distributeKey = RedisKeyConst.TASK_DISTRIBUTE_KEY.getKey("_" + activityEventMessage.getCustomerId() + "_" + taskConfig.getTaskId());
        String vipLevel = activityEventMessage.getBody().get("vipLevel") == null ? ActivityConstant.VipLevelEnum.NORMAL.name()
                : String.valueOf(activityEventMessage.getBody().get("vipLevel"));

        log.info("Starting doTask for customerId: {}, taskId: {}, vipLevel: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId(), vipLevel);

        ActivityTaskItem taskItem = getTaskItem(activityEventMessage, taskConfig, codeConfig, distributeKey);
        if (taskItem == null) {
            log.warn("Task item is null for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
            return null;
        }

        if (ActivityConstant.TaskStatusEnum.FAIL.name().equals(taskItem.getStatus())) {
            if (codeConfig.getInc() < 0) {
                log.warn("Task status is FAIL and codeConfig increment is negative for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
                return null;
            }
            taskItem = buildActivityTaskItem(activityEventMessage, taskConfig);
            activityTaskItemBuilder.update(taskItem);
            log.info("Updated task item for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
        }

        if (taskItem.getProgress() >= taskConfig.getLimit(vipLevel)) {
            completeTask(taskItem, activityEventMessage);
            log.info("Task completed for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
            return null;
        }

        log.info("Executing task for taskItem: {}, activityEventMessage: {}", taskItem, activityEventMessage);
        return executeTask(taskItem, activityEventMessage, taskConfig, codeConfig);
    }

    private ActivityTaskItem getTaskItem(ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig, String distributeKey) throws Exception {
        try {
            if (!redisDistributedLock.tryLock(distributeKey, Duration.ofSeconds(5))) {
                log.warn("Failed to acquire lock for distributeKey: {}", distributeKey);
                throw new ActivityException(ActivityResponseCode.TASK_RETRY);
            }

            log.info("Lock acquired for distributeKey: {}", distributeKey);

            ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(activityEventMessage.getCustomerId(), TaskCycleDomain.getCurrencyCycle(taskConfig, activityEventMessage.getEventTime()), taskConfig.getTaskId());
            if (taskItem == null && codeConfig.getInc() >= 0) {
                taskItem = buildActivityTaskItem(activityEventMessage, taskConfig);
                if (!activityTaskItemBuilder.insert(taskItem)) {
                    log.warn("Failed to insert task item for distributeKey: {}", distributeKey);
                    throw new ActivityException(ActivityResponseCode.TASK_RETRY);
                }
                log.info("Inserted new task item for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());
            }
            return taskItem;
        } catch (Exception e) {
            log.error("Error in getTaskItem for distributeKey: {}", distributeKey, e);
            throw new ActivityException(ActivityResponseCode.TASK_RETRY);
        } finally {
            redisDistributedLock.unlock(distributeKey);
            log.info("Lock released for distributeKey: {}", distributeKey);
        }
    }

    private void completeTask(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage) {
        if (!ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus())) {
            taskItem.setStatus(ActivityConstant.TaskStatusEnum.DONE.name());
            taskItem.setCompleteTime(TimeUtil.getUtcTime(TimeUtil.parseUnittime(activityEventMessage.getEventTime()), TimeUtil.YYYYMMDDHHMMSS));
            activityTaskItemBuilder.update(taskItem);
            log.info("Task marked as complete for taskItem: {}", taskItem);
        }
    }

    private List<Award> executeTask(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig) throws Exception {
        try {
            log.info("Executing task in TCC service for taskItem: {}", taskItem);
            return activityTaskTccService.doTask(taskItem, activityEventMessage, taskConfig, codeConfig);
        } catch (TableStoreException ex) {
            log.error("Error executing task in TCC service for taskItem: {}", taskItem, ex);
            throw new ActivityException(ActivityResponseCode.TASK_RETRY);
        }
    }

    @Override
    public TaskCompletedResult getTaskResult(String customerId, String cycle, String taskId) {
        TaskCompletedResult result = new TaskCompletedResult();
        ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, cycle, taskId);
        Page<ActivityTaskItem> list = activityTaskItemBuilder.findByIdList(customerId, taskId, TimeUtil.getDataStr(TimeUtil.addDay(TimeUtil.parse(cycle, new SimpleDateFormat("yyyyMMdd")), -50), new SimpleDateFormat("yyyyMMdd")), cycle, 100);
        int consecutiveDays = taskItem != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus()) ? 1 : 0;
        int complateDays = taskItem != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(taskItem.getStatus()) ? 1 : 0;
        int start = taskItem != null ? 1 : 0;
        if(CollectionUtils.isNotEmpty(list.getRows())){
            List<ActivityTaskItem> sortedItems = list.getRows();
            String currentCycle = TimeUtil.getDataStr(TimeUtil.addDay(TimeUtil.parse(cycle, new SimpleDateFormat("yyyyMMdd")), -1), "yyyyMMdd");
            for(int i = start; i < list.getRows().size(); i++){
                ActivityTaskItem item = sortedItems.get(i);
                if(ActivityConstant.TaskStatusEnum.DONE.name().equals(item.getStatus())) {
                    complateDays++;
                    if(TimeUtil.parse(currentCycle, new SimpleDateFormat("yyyyMMdd")).compareTo(TimeUtil.parse(item.getCycle(), new SimpleDateFormat("yyyyMMdd"))) == 0) {
                        consecutiveDays++;
                        currentCycle = TimeUtil.getDataStr(TimeUtil.addDay(TimeUtil.parse(currentCycle, new SimpleDateFormat("yyyyMMdd")), -1), "yyyyMMdd");
                    }
                }
            }
        }
        if(taskItem == null){
            result.setConsecutiveDays(consecutiveDays);
            result.setCompleteDays(complateDays);
            result.setCustomerId(customerId);
            result.setTaskId(taskId);
            result.setCycle(cycle);
            result.setStatus(ActivityConstant.TaskStatusEnum.NOT_STARTED.name());
            return result;
        }
        TaskCompletedResult taskCompletedResult = BeanUtil.copyProperties(taskItem, TaskCompletedResult.class);
        taskCompletedResult.setConsecutiveDays(consecutiveDays);
        taskCompletedResult.setCompleteDays(complateDays);
        return taskCompletedResult;
    }

    @Override
    public TaskCompletedResult getTaskDetailResult(String customerId, String cycle ,String taskId, String targetId) {
        ActivityTaskItem taskItems = activityTaskItemBuilder.findDetailByCustomer(customerId, cycle, taskId, targetId);
        return taskItems == null ? null : BeanUtil.copyProperties(taskItems, TaskCompletedResult.class);
    }

    @Override
    public TaskCompletedResult getTaskResultBySocial(String platform, String socialCustomerId, String cycle, String taskId) {
        TaskCompletedResult result = new TaskCompletedResult();
        ActivityTaskItem taskItem = activityTaskItemBuilder.findByTarget(platform, socialCustomerId, cycle, taskId);
        if(taskItem == null){
            result.setStatus(ActivityConstant.TaskStatusEnum.NOT_STARTED.name());
            return result;
        }
        return BeanUtil.copyProperties(taskItem, TaskCompletedResult.class);
    }

    @Override
    public List<Award> findByTaskCode(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum) {
        List<TaskConfigDTO> configDTOS = taskConfigService.findByTaskCode(saasId, taskCode);
        List<Award> awards = new ArrayList<>();
        for(TaskConfigDTO configDTO : configDTOS){
            List<Award> award = configDTO.getReward().get("0");
            awards.add(award.stream().filter(a -> vipLevelEnum.name().equals(a.getVipLevel())).findFirst().get());
        }
        return awards;
    }

    @Override
    public List<Award> listTaskRewards(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum) {
        List<TaskConfigDTO> configDTOS = taskConfigService.findByTaskCode(saasId, taskCode);
        List<Award> awards = new ArrayList<>();
        for(TaskConfigDTO configDTO : configDTOS){
            List<Award> award = configDTO.getReward().get("0");
            awards.addAll(award.stream().filter(a -> vipLevelEnum.name().equals(a.getVipLevel())).collect(Collectors.toList()));
        }
        return awards;
    }

    /**
     * 查询任务是否完成
     * @param saasId
     * @param taskId
     * @param customerId
     * @param type
     * @return
     */
    @Override
    public TaskProgressResponse findProgressByTaskId(String saasId, String taskId, String customerId, String type) {
        TaskProgressResponse response = new TaskProgressResponse();
        response.setStatus(1);//1：已完成，不弹窗
        boolean taskWhiteFlag = StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
        TaskConfigDTO configDTO = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        if (Objects.isNull(configDTO)) {
            log.warn("findProgressByTaskId configDTO is null");
            return null;
        }
        List<TaskConfigDTO> subTasks = configDTO.getSubTask().stream().sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).collect(Collectors.toList());
        if ("reward".equals(type)) {
            if (taskDateLimit(configDTO, taskWhiteFlag)) {
                log.info("findProgressByTaskId, time out");
                return response;
            }
            if(configDTO.getAttr() != null && configDTO.getAttr().get("preTaskId") != null){
                List<ActivityTaskItem> taskItems = activityTaskItemBuilder.findByCustomer(customerId, configDTO.getAttr().get("preTaskId"));
                if(CollectionUtils.isEmpty(taskItems)){
                    log.info("findProgressByTaskId, pre task not completed");
                    return response;
                }
            }
            for (TaskConfigDTO dto : subTasks) {
                if (taskDateLimit(dto, taskWhiteFlag)) {
                    log.info("findProgressByTaskId, task time time out");
                    continue;
                }
                String rt = dto.getAttr().get("register-time");
                CustomerBindDTO cid = remoteCustomerBindService.findByUid(saasId, customerId);
                if(cid != null && rt != null && cid.getCid().substring(0, rt.length()).compareTo(rt) >= 0){
                    log.info("findProgressByTaskId, register time time out");
                    continue;
                }
                ActivityTaskItem taskItem = activityTaskItemBuilder.findByCustomer(customerId, TaskCycleDomain.getCurrencyCycle(dto, null), dto.getTaskId());
                if(taskItem == null || !"DONE".equals(taskItem.getStatus())){
                    response.setStatus(0);
                    return response;
                }
                String businessId = taskItem.getBusinessId() + taskItem.getProgress() / dto.getRewardFrequency();
                ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(businessId);
                if (reward == null || !ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())) {
                    response.setStatus(0);
                    return response;
                }
            }
        }
        return response;
    }

    @Override
    public TaskPopResponse getTaskEarlyBirdPop(String saasId, String customerId) {
        TaskPopResponse response = new TaskPopResponse();
        response.setPop(false);//1：已完成，不弹窗
        List<TaskConfigDTO> taskConfigDTOS = taskConfigService.findByTaskCodeAndWhiteFlag(saasId, "early_bird", customerId, ActivityTaskConstant.TaskConfigScope.SUB_TASK);
        if (CollectionUtils.isEmpty(taskConfigDTOS)) {
            log.info("findProgressByTaskId, time out");
            return response;
        }
        taskConfigDTOS.sort((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime()));
        TaskConfigDTO configDTO = taskConfigDTOS.get(0);
        List<TaskConfigDTO> subTasks =
            configDTO.getSubTask().stream().sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime()))
                .collect(Collectors.toList());
        if (configDTO.getAttr() != null && configDTO.getAttr().get("preTaskId") != null) {
            List<ActivityTaskItem> taskItems =
                activityTaskItemBuilder.findByCustomer(customerId, configDTO.getAttr().get("preTaskId"));
            if (CollectionUtils.isEmpty(taskItems)) {
                log.info("findProgressByTaskId, pre task not completed");
                return response;
            }
        }
        CustomerBindDTO cid = remoteCustomerBindService.findByUid(saasId, customerId);
        if (CollectionUtils.isNotEmpty(subTasks)) {
            Map<String, ActivityTaskItem> taskItemsMap = new HashMap<>();
            List<String> taskIds = subTasks.stream().map(e -> e.getTaskId()).collect(Collectors.toList());
            List<ActivityTaskItem> taskItems = activityTaskItemBuilder.findByCustomer(customerId,TaskCycleDomain.getCurrencyCycle(subTasks.get(0), null),taskIds);
            log.info("TaskEarlyBirdPop customerId:{}, taskIds:{}, activityItems:{}", customerId, JSON.toJSONString(taskIds), JSON.toJSONString(taskItems));
            if (CollectionUtils.isNotEmpty(taskItems)) {
                taskItemsMap = taskItems.stream().collect(Collectors.toMap(ActivityTaskItem::getTaskId, Function.identity(), (v1, v2) -> v2));
            }
            boolean taskWhiteFlag = StringUtils.isNotBlank(customerId) && redisService.hGet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), customerId) != null;
            for (TaskConfigDTO dto : subTasks) {
                if (taskDateLimit(dto, taskWhiteFlag)) {
                    log.info("findProgressByTaskId, task time time out");
                    continue;
                }
                String rt = dto.getAttr().get("register-time");
                if (cid != null && rt != null && cid.getCid().substring(0, rt.length()).compareTo(rt) >= 0) {
                    log.info("findProgressByTaskId, register time time out");
                    continue;
                }
                ActivityTaskItem taskItem = taskItemsMap.get(dto.getTaskId());
                if (taskItem == null || !"DONE".equals(taskItem.getStatus())) {
                    response.setPop(true);
                    response.setTaskId(configDTO.getTaskId());
                    return response;
                }
                String businessId = taskItem.getBusinessId() + taskItem.getProgress()/dto.getRewardFrequency();
                ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByBusinessId(businessId);
                if(reward == null || !ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(reward.getStatus())){
                    response.setPop(true);
                    response.setTaskId(configDTO.getTaskId());
                    return response;
                }
            }
        }
        return response;
    }

    @Override
    public Result<List<String>> batchCreateRedeemByTask(List<String> codeList, String saasId, String taskId, String businessType) {
        if(CollectionUtils.isEmpty(codeList) || StringUtils.isEmpty(saasId) || StringUtils.isEmpty(taskId)){
            return Result.of(PARAM_INVALID, "入参不允许为空");
        }
        List<List<String>> parts = Lists.partition(codeList, 100);
        for(List<String> codePart : parts){
            List<CouponConfig> couponConfigs = couponConfigBuilder.batchGetByCode(codePart, saasId, businessType);
            if(CollectionUtils.isNotEmpty(couponConfigs)){
                List<String> codeListRepeated = couponConfigs.stream().map(CouponConfig::getCouponCode).collect(Collectors.toList());
                return Result.of(CHECK_FAILED, "data 中验证码重复，操作未执行", codeListRepeated);
            }
        }

        Result<List<String>> result = new Result<>();

        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskId(taskId);
        if(taskConfigDTO == null){
            return Result.of(PARAM_INVALID,"taskId 无效");
        }
        Award award = taskConfigDTO.getReward().get("0").get(0);

        for(List<String> codePart : parts){
            log.info("batchCreateRedeemByTask part begin, codeList:{}, taskConfigDTO:{}", JSON.toJSONString(codePart), JSON.toJSONString(taskConfigDTO));
            List<CouponConfig> couponConfigs = codePart.stream().filter(StringUtils::isNotBlank).map(code -> {
                CouponConfig config = new CouponConfig();
                config.setCouponCode(code);
                config.setTaskId(taskId);
                config.setBusinessType(businessType);
                config.setSaasId(saasId);
                config.setStatus(ActivityConstant.CommonStatus.ACTIVE.getCode());
                config.setAwardAmount(award.getAmount());
                config.setAwardCurrency(award.getCurrency());
                return config;
            }).collect(Collectors.toList());
            Boolean partResult = couponConfigBuilder.batchInsert(couponConfigs);
            if(!partResult){
                log.error("ActivityTaskServiceImpl.batchCreateRedeemByTask error, code request:{}", codePart);
                result.setSuccess(false);
                result.setMessage("创建验证码失败，请人工检查");
            }
        }
        return result;
    }

    private void preCheck(TaskConfigDTO configDTO, String customerId) throws ActivityException {
        if (MapUtils.isEmpty(configDTO.getAttr()) || configDTO.getAttr().get("preTaskId") == null) {
            return;
        }
        String[] list = configDTO.getAttr().get("preTaskId").split(",");
        for (String taskId : list) {
            TaskConfigDTO task = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId);
            if (Objects.isNull(task)) {
                throw new ActivityException(ActivityResponseCode.INVALID_PARAMETER, taskId);
            }
            TaskCompletedResult taskResult = getTaskResult(customerId, TaskCycleDomain.getCurrencyCycle(task, null), taskId);
            if (!taskResult.isDone()) {
                throw new ActivityException(ActivityResponseCode.TASK_PRE_CHECK_NOT_PASS, taskId);
            }
        }
    }

    @Override
    public VerifyResponse verify(Token accessToken, String taskId, String saasId, String ext) throws ActivityException {
        log.info("[task] verify start:{}, {}, {}, {}", accessToken, taskId, saasId, ext);
        TaskConfigDTO configDTO = taskConfigService.findByTaskIdAndWhiteFlag(taskId, accessToken.getLoginCustomerId());
        if (Objects.isNull(configDTO)) {
            log.warn("verify taskConfigDTO is null");
            return null;
        }
        log.info("user_track [task verify] [{},{}] start", configDTO.getCode(), configDTO.getTaskId());
        String domain = configDTO.getDomain();

        if (Objects.nonNull(configDTO.getAttr()) && configDTO.getAttr().containsKey("ospCallBack")) {
            if (Boolean.parseBoolean(configDTO.getAttr().get("ospCallBack"))) {
                configDTO.setCode(osp_callback.name());
            }
        }

        if (Objects.nonNull(configDTO.getAttr()) && configDTO.getAttr().containsKey("createDapp")) {
            if (Boolean.parseBoolean(configDTO.getAttr().get("createDapp"))) {
                configDTO.setCode(create_dapp.name());
            }
        }

        TaskCompletedResult taskResult = getTaskResult(accessToken.getLoginCustomerId(), TaskCycleDomain.getCurrencyCycle(configDTO, null), taskId);
        //否，实时校验
        if(taskResult.isDone()){
            log.info("[task] verify 社区帐号已经做过该任务:{}, {}, {}", accessToken, domain, taskResult);
            throw new ActivityException(ActivityResponseCode.SOCIAL_VERIFY_ALREADY);
        }

        VerifyResult verify;

        if (BooleanUtils.isTrue(configDTO.getSkipVerification())) {
            verify = VerifyResult.builder().result(true).contentIds(List.of(accessToken.getLoginCustomerId())).build();
            log.info("[task] verify skipped, customerID:{},taskId:{}", accessToken.getLoginCustomerId(), taskId);
        } else {
            preCheck(configDTO, accessToken.getLoginCustomerId());
            if("opensocial".equals(saasId) && "twitter".equals(domain)){
                verify = threePlatformFilter.verify(configDTO,
                        AccessToken.builder()
                                .twitterId(accessToken.getSocialCustomerId())
                                .twitterHandleName(accessToken.getSocialCustomerName())
                                .build(),
                        accessToken.getLoginCustomerId(), saasId, StringUtils.isNotBlank(ext) ? JSON.parseObject(ext, Map.class) : null);
                log.info("[task] verify threePlatformFilter end. customerID:{},taskId:{}", accessToken.getLoginCustomerId(), taskId);
            }else if (EnumUtils.isValidEnum(OpenSocialPlatformEnum.class, domain)) {
                preCheck(configDTO, accessToken.getLoginCustomerId());
                Token authToken = authService.getAuthToken(saasId, accessToken.getLoginCustomerId(), domain);
                if (authToken == null) {
                    log.info("[task] verify accessToken 已过期:{}, {}", accessToken, domain);
                    throw new ActivityException(ActivityResponseCode.ACCESS_TOKEN_EXPIRE);
                }
                accessToken.setAccessToken(authToken.getAccessToken());
                accessToken.setRefreshToken(authToken.getRefreshToken());
                accessToken.setSocialCustomerId(authToken.getSocialCustomerId());
                verify = threePlatformFilter.verify(configDTO,
                        AccessToken.builder()
                                .vipLevel(accessToken.getVipLevel())
                                .accessToken(accessToken.getAccessToken())
                                .refreshToken(accessToken.getRefreshToken())
                                .userId(accessToken.getSocialCustomerId())
                                .build(),
                        accessToken.getLoginCustomerId(), saasId, StringUtils.isNotBlank(ext) ? JSON.parseObject(ext, Map.class) : null);
                log.info("[task] verify OpenSocialPlatform Filter end. authToken:{},taskId:{}", authToken, taskId);
            } else {
                preCheck(configDTO, accessToken.getLoginCustomerId());
                //other third platform , only "osp" now
                verify = threePlatformFilter.verify(configDTO,
                        AccessToken.builder()
                                .userId(accessToken.getLoginCustomerId())
                                .address(accessToken.getAddress())
                                .build(),
                        accessToken.getLoginCustomerId(), saasId, StringUtils.isNotBlank(ext) ? JSON.parseObject(ext, Map.class) : null);
                log.info("[task] verify threePlatformFilter end. customerID:{},taskId:{}", accessToken.getLoginCustomerId(), taskId);
            }
        }

        if(verify.isResult()){
            // 等待 OSP 回调处理
            if (configDTO.getCode().equalsIgnoreCase(ActivityTaskConstant.TaskCodeEnum.osp_callback.name())) {
                log.info("[task] wait osp call back:{}, {}, {}", accessToken, domain, verify);
                throw new ActivityException(ActivityResponseCode.SOCIAL_CALL_BACK_WAIT);
            }
            // 异步执行
            for(int i = 0; i < Math.min(verify.getContentIds().size(), configDTO.getLimit(accessToken.getVipLevel())); i++){
                if(BooleanUtils.isTrue(configDTO.getOnlyVerifySocial())){
                    ActivityTaskItem taskItem = activityTaskItemBuilder.findByTarget(null, verify.getContentIds().get(i), TaskCycleDomain.getCurrencyCycle(configDTO, null), taskId);
                    if(taskItem != null){
                        throw new ActivityException(ActivityResponseCode.SOCIAL_VERIFY_ALREADY);
                    }
                }
                EventDTO eventDTO = new EventDTO();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("taskId", taskId);
                jsonObject.put("vipLevel", accessToken.getVipLevel());
                jsonObject.put("saasId", configDTO.getSaasId());
                if(StringUtils.isNotBlank(ext)){
                    JSONObject extMap = JSON.parseObject(ext);
                    if(extMap.containsKey("encryptMaxPointMultiplier")){
                        jsonObject.put("pointMultiplier", extMap.getString("encryptMaxPointMultiplier"));
                    }
                }
                if(verify.getSocialId() != null && verify.getSocialName() != null){
                    jsonObject.put("socialId", verify.getSocialId());
                    jsonObject.put("socialName", verify.getSocialName());
                }
                eventDTO.setBody(jsonObject);
                eventDTO.setCustomerId(accessToken.getLoginCustomerId());
                eventDTO.setTime(new Date().getTime());
                if(BooleanUtils.isTrue(configDTO.getOnlyVerifySocial())){
                    eventDTO.setGlobalUid(verify.getContentIds().get(i));
                }else{
                    eventDTO.setGlobalUid(TaskCycleDomain.getCurrencyCycle(configDTO, null) + "_" + verify.getContentIds().get(i) + "_" + accessToken.getLoginCustomerId() + "_" +  taskId);
                }
                eventDTO.setName(configDTO.getCode());
                eventClient.push(eventDTO);
            }
        }
        VerifyResponse verifyResponse = new VerifyResponse();
        verifyResponse.setSuccess(verify.isResult());
        log.info("[task] verify end:{},taskId:{}", verify, taskId);
        log.info("user_track [task verify] [{},{}] end", configDTO.getCode(), configDTO.getTaskId());
        return verifyResponse;
    }

    @Override
    public Result<List<String>> batchCreateRedeem(List<String> codeList, String saasId, String taskId, String businessType, String awardValue) {
        List<List<String>> parts = Lists.partition(codeList, 100);
        for(List<String> codePart : parts){
            List<CouponConfig> couponConfigs = couponConfigBuilder.batchGetByCode(codePart, saasId, businessType);
            if(CollectionUtils.isNotEmpty(couponConfigs)){
                List<String> codeListRepeated = couponConfigs.stream().map(CouponConfig::getCouponCode).collect(Collectors.toList());
                return Result.of(CHECK_FAILED, "data 中验证码重复，操作未执行", codeListRepeated);
            }
        }

        Result<List<String>> result = new Result<>();

        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskId(taskId);
        if(taskConfigDTO == null){
            return Result.of(PARAM_INVALID,"taskId 无效");
        }

        for(List<String> codePart : parts){
            log.info("batchCreateRedeemByTask part begin, codeList:{}, taskConfigDTO:{}", JSON.toJSONString(codePart), JSON.toJSONString(taskConfigDTO));
            List<CouponConfig> couponConfigs = codePart.stream().filter(StringUtils::isNotBlank).map(code -> {
                CouponConfig config = new CouponConfig();
                config.setCouponCode(code);
                config.setTaskId(taskId);
                config.setBusinessType(businessType);
                config.setSaasId(saasId);
                config.setStatus(ActivityConstant.CommonStatus.ACTIVE.getCode());
                config.setAwardAmount(awardValue);
                config.setAwardCurrency("POINT");
                return config;
            }).collect(Collectors.toList());
            Boolean partResult = couponConfigBuilder.batchInsert(couponConfigs);
            if(!partResult){
                log.error("ActivityTaskServiceImpl.batchCreateRedeemByTask error, code request:{}", codePart);
                result.setSuccess(false);
                result.setMessage("创建验证码失败，请人工检查");
            }
        }
        return result;
    }

    private ActivityTaskItem buildActivityTaskItem(ActivityEventMessage activityEventMessage, TaskConfigDTO config){
        ActivityTaskItem taskItem = ActivityTaskItem
                .buildPrimary(activityEventMessage.getCustomerId(),
                        config.getTaskId(), TaskCycleDomain.getCurrencyCycle(config, activityEventMessage.getEventTime()));
        taskItem.setBusinessId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(activityTaskItemBuilder.getTableName())));
        taskItem.setEvent(config.getCode());
        taskItem.setStatus(ActivityConstant.TaskStatusEnum.APPENDING.name());
        String vipLevel = activityEventMessage.getBody().get("vipLevel") == null ? null
                : String.valueOf(activityEventMessage.getBody().get("vipLevel"));
        taskItem.setCompleteThreshold(config.getLimit(vipLevel));
        taskItem.setProgress(0);
        taskItem.setExpiredTime(TaskCycleDomain.getCurrencyCycleEnd(config, activityEventMessage.getEventTime()));
        return taskItem;
    }

    private void mergeActivityTaskConfig(ActivityTaskConfig newConfig, ActivityTaskConfig oldConfig) {
        if (StringUtils.isNotBlank(newConfig.getEvent())) {
            oldConfig.setEvent(newConfig.getEvent());
        }
        if (StringUtils.isNotBlank(newConfig.getLevel())) {
            oldConfig.setLevel(newConfig.getLevel());
        }
        if (StringUtils.isNotBlank(newConfig.getType())) {
            oldConfig.setType(newConfig.getType());
        }
        if (StringUtils.isNotBlank(newConfig.getNameCn())) {
            oldConfig.setNameCn(newConfig.getNameCn());
        }
        if (StringUtils.isNotBlank(newConfig.getNameEn())) {
            oldConfig.setNameEn(newConfig.getNameEn());
        }
        if (StringUtils.isNotBlank(newConfig.getNameHk())) {
            oldConfig.setNameHk(newConfig.getNameHk());
        }
        if (StringUtils.isNotBlank(newConfig.getActivityId())) {
            oldConfig.setActivityId(newConfig.getActivityId());
        }
        if (StringUtils.isNotBlank(newConfig.getCallback())) {
            oldConfig.setCallback(newConfig.getCallback());
        }
        if (StringUtils.isNotBlank(newConfig.getCycleType())) {
            oldConfig.setCycleType(newConfig.getCycleType());
        }
        if (StringUtils.isNotBlank(newConfig.getCycle())) {
            oldConfig.setCycle(newConfig.getCycle());
        }
        if (newConfig.getCompleteThreshold() != null) {
            oldConfig.setCompleteThreshold(newConfig.getCompleteThreshold());
        }
        if (StringUtils.isNotBlank(newConfig.getDescCn())) {
            oldConfig.setDescCn(newConfig.getDescCn());
        }
        if (StringUtils.isNotBlank(newConfig.getDescEn())) {
            oldConfig.setDescEn(newConfig.getDescEn());
        }
        if (StringUtils.isNotBlank(newConfig.getDescHk())) {
            oldConfig.setDescHk(newConfig.getDescHk());
        }
        if (StringUtils.isNotBlank(newConfig.getIcon())) {
            oldConfig.setIcon(newConfig.getIcon());
        }
        if (newConfig.getStatus() != null) {
            oldConfig.setStatus(newConfig.getStatus());
        }
        if (newConfig.getAmount() != null) {
            oldConfig.setAmount(newConfig.getAmount());
        }
        if (StringUtils.isNotBlank(newConfig.getAward())) {
            oldConfig.setAward(newConfig.getAward());
        }
        if (newConfig.getStartTime() != null) {
            oldConfig.setStartTime(newConfig.getStartTime());
        }
        if (newConfig.getAwardTimeType() != null) {
            oldConfig.setAwardTimeType(newConfig.getAwardTimeType());
        }
        if (newConfig.getCompleteTimes() != null) {
            oldConfig.setCompleteTimes(newConfig.getCompleteTimes());
        }
        if (StringUtils.isNotBlank(newConfig.getNameI18n())) {
            oldConfig.setNameI18n(newConfig.getNameI18n());
        }
        if (StringUtils.isNotBlank(newConfig.getDescI18n())) {
            oldConfig.setDescI18n(newConfig.getDescI18n());
        }
        oldConfig.setEndTime(newConfig.getEndTime());
        oldConfig.setUrl(newConfig.getUrl());
        oldConfig.setModified(new Date());
    }

    @Override
    public ActivityTaskConfig findTaskById(String id){
        return activityTaskDao.selectByPrimaryKey(id);
    }

    private String parseDesc(String desc, ActivityConstant.VipLevelEnum vipLevelEnum, String channel){
        if(desc != null && desc.contains("\n")){
            return desc.split("\\n")[vipLevelEnum.getLevel()];
        }else if(desc != null && desc.contains("{")){
            try{
                return String.valueOf(JSON.parseObject(desc).get(String.format("%s_%s", channel, vipLevelEnum.name()).toUpperCase()));
            }catch (Exception ex){
                return desc;
            }
        }else{
            return desc;
        }
    }

    private String parseTitle(String title, String channel){
        if(title.contains("{")){
            return String.valueOf(JSON.parseObject(title).get(channel.toUpperCase()));
        }else{
            return title;
        }
    }

    @Override
    public VerifyResponse serverVerify(String saasId, String uid, String scene, String ext) throws ActivityException {
        try {
            log.info("user_track【task】serverVerify start, saasId:{}, uid:{}, scene:{}, ext:{}", saasId, uid, scene, ext);
            ActivityTaskConstant.ServerVerifyScene verifyScene = ActivityTaskConstant.ServerVerifyScene.of(scene);
            if (verifyScene == null) {
                log.error("serverVerify fail, scene not support, scene:{}", scene);
                return VerifyResponse.response(ActivityResponseCode.NOT_SUPPORT_SCENE);
            }
            VerifyResponse response = switch (verifyScene) {
                case follow_x -> serverVerifyFollowX(saasId, uid, ext);
            };
            log.info("user_track【task】serverVerify finish, saasId:{}, uid:{}, scene:{}, ext:{}, response:{}", saasId, uid, scene, ext, response);
            return response;
        } catch (Exception e) {
            log.error("serverVerify exception, saasId:{}, uid:{}, scene:{}, ext:{}", saasId, uid, scene, ext, e);
            throw new ActivityException(ActivityResponseCode.SYSTEM_ERROR);
        }
    }

    private VerifyResponse serverVerifyFollowX(String saasId, String uid, String ext) throws ActivityException {
        // 根据业务需要，校验ext参数
        if (!checkExt(ext, TARGET_HANDLE)) {
            log.error("serverVerifyFollowX fail, ext is invalid, ext:{}", ext);
            return VerifyResponse.response(ActivityResponseCode.INVALID_PARAMETER);
        }
        // 校验twitter token
        Token authToken = authService.getAuthToken(saasId, uid, OpenSocialPlatformEnum.twitter.name());
        if (authToken == null) {
            log.warn("serverVerifyFollowX fail, accessToken已过期: saasId:{}, uid:{}", saasId, uid);
            return VerifyResponse.response(ActivityResponseCode.AUTH_CODE_INVALID);
        }
        // 校验是否关注
        JSONObject json = JSON.parseObject(ext);
        String targetHandle = json.getString(TARGET_HANDLE);
        VerifyResult result = threePlatformFilter.verifyFollowX(
                AccessToken.builder()
                    .vipLevel("NORMAL")
                    .accessToken(authToken.getAccessToken())
                    .refreshToken(authToken.getRefreshToken())
                    .userId(authToken.getSocialCustomerId())
                    .saasId(saasId)
                    .build(),
                targetHandle);
        log.info("serverVerifyFollowX end. authToken:{}, result:{}, saasId:{}, uid:{}, ext={}", authToken, result, saasId, uid, ext);
        return result.isResult() ? VerifyResponse.response(ActivityResponseCode.SUCCESS)
            : VerifyResponse.response(ActivityResponseCode.TASK_VERIFY_NO_PASS);
    }

    private boolean checkExt(String ext, String... fields) {
        if (fields.length == 0) {
            return true;
        }
        if (StringUtils.isBlank(ext) || !JSONUtil.isTypeJSON(ext)) {
            log.error("checkExt fail, ext is null or not invalid , ext:{}", ext);
            return false;
        }
        JSONObject json = JSON.parseObject(ext);
        List<String> missingFields = Arrays.stream(fields).filter(field -> !json.containsKey(field)).collect(Collectors.toList());
        if (missingFields != null && !missingFields.isEmpty()) {
            log.error("checkExt fail, ext not contain fields:{}, ext:{}", missingFields, ext);
            return false;
        }
        return true;
    }

    private boolean taskDateLimit(TaskConfigDTO taskConfig, boolean taskWhiteFlag) {
        log.info("taskDateLimit, taskConfig:{}, taskWhiteFlag:{}", JSON.toJSONString(taskConfig), taskWhiteFlag);
        if (taskWhiteFlag) {
            //配置任务白名单时，可提前查看到任务
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, kactivityProperties.getTaskWhiteDate());
            if (calendar.getTime().getTime() < taskConfig.getStartTime() || new Date().getTime() >= taskConfig.getEndTime()) {
                return true;
            }
        } else {
            if (new Date().getTime() < taskConfig.getStartTime() || new Date().getTime() >= taskConfig.getEndTime()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean submitTask(TaskSubmitDTO taskSubmitDTO) {
        SaharaTaskItem saharaTaskItem = BeanUtil.copyProperties(taskSubmitDTO, SaharaTaskItem.class, "submittedContent");
        saharaTaskItem.setSubmittedContent(JSON.toJSONString(taskSubmitDTO.getSubmittedContent()));
        saharaTaskItem.setUserWalletAddress(saharaTaskItem.getUserWalletAddress().toLowerCase());
        return saharaTaskItemBuilder.insert(saharaTaskItem);
    }
}
