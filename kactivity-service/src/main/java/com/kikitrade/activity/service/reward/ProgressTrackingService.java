package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.dal.tablestore.model.ProgressChest;
import com.kikitrade.activity.dal.tablestore.model.UserChestClaim;
import com.kikitrade.activity.dal.tablestore.model.UserProgress;

import java.util.List;

/**
 * 进度追踪服务接口
 * 提供用户进度更新、宝箱解锁条件判断、宝箱领取等核心功能
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ProgressTrackingService {
    
    /**
     * 更新用户进度
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param progressType 进度类型
     * @param increment 增量值
     * @return 更新后的进度值
     */
    Integer updateUserProgress(String userId, String saasId, String progressType, Integer increment);
    
    /**
     * 设置用户进度（绝对值）
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param progressType 进度类型
     * @param progressValue 进度值
     * @return 是否成功
     */
    boolean setUserProgress(String userId, String saasId, String progressType, Integer progressValue);
    
    /**
     * 获取用户进度
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param progressType 进度类型
     * @return 用户进度
     */
    UserProgress getUserProgress(String userId, String saasId, String progressType);
    
    /**
     * 获取用户所有进度
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 用户进度列表
     */
    List<UserProgress> getAllUserProgress(String userId, String saasId);
    
    /**
     * 检查宝箱解锁条件
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param chestId 宝箱ID
     * @return 是否满足解锁条件
     */
    boolean checkChestUnlockCondition(String userId, String saasId, String chestId);
    
    /**
     * 领取宝箱
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param chestId 宝箱ID
     * @param claimChannel 领取渠道
     * @param claimIp 领取IP
     * @param claimDevice 领取设备
     * @return 领取结果
     */
    ChestClaimResult claimChest(String userId, String saasId, String chestId, 
                               String claimChannel, String claimIp, String claimDevice);
    
    /**
     * 获取用户可领取的宝箱列表
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @return 可领取的宝箱列表
     */
    List<ProgressChest> getClaimableChests(String userId, String saasId);
    
    /**
     * 获取用户宝箱领取状态
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param chestId 宝箱ID
     * @return 领取状态
     */
    ChestClaimStatus getChestClaimStatus(String userId, String saasId, String chestId);
    
    /**
     * 获取用户宝箱领取历史
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param chestType 宝箱类型（可选）
     * @return 领取历史列表
     */
    List<UserChestClaim> getUserChestClaimHistory(String userId, String saasId, String chestType);
    
    /**
     * 获取进度宝箱配置
     * 
     * @param saasId SaaS ID
     * @param chestId 宝箱ID
     * @return 宝箱配置
     */
    ProgressChest getProgressChest(String saasId, String chestId);
    
    /**
     * 获取活跃的进度宝箱配置列表
     * 
     * @param saasId SaaS ID
     * @param chestType 宝箱类型（可选）
     * @return 宝箱配置列表
     */
    List<ProgressChest> getActiveProgressChests(String saasId, String chestType);
    
    /**
     * 重置用户进度（按重置周期）
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param progressType 进度类型
     * @return 是否成功
     */
    boolean resetUserProgress(String userId, String saasId, String progressType);
    
    /**
     * 批量重置用户进度
     * 
     * @param saasId SaaS ID
     * @param progressType 进度类型
     * @return 重置的用户数量
     */
    int batchResetUserProgress(String saasId, String progressType);
    
    /**
     * 宝箱领取结果
     */
    class ChestClaimResult {
        private boolean success;
        private String errorCode;
        private String message;
        private List<String> receivedPrizes;
        private String claimId;
        private Long claimTime;
        
        public ChestClaimResult(boolean success, String errorCode, String message) {
            this.success = success;
            this.errorCode = errorCode;
            this.message = message;
        }
        
        public ChestClaimResult(boolean success, String message, List<String> receivedPrizes, String claimId) {
            this.success = success;
            this.message = message;
            this.receivedPrizes = receivedPrizes;
            this.claimId = claimId;
            this.claimTime = System.currentTimeMillis();
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public List<String> getReceivedPrizes() { return receivedPrizes; }
        public void setReceivedPrizes(List<String> receivedPrizes) { this.receivedPrizes = receivedPrizes; }
        
        public String getClaimId() { return claimId; }
        public void setClaimId(String claimId) { this.claimId = claimId; }
        
        public Long getClaimTime() { return claimTime; }
        public void setClaimTime(Long claimTime) { this.claimTime = claimTime; }
    }
    
    /**
     * 宝箱领取状态
     */
    class ChestClaimStatus {
        private String chestId;
        private String chestName;
        private String status; // LOCKED, UNLOCKED, CLAIMED, EXPIRED
        private Integer currentProgress;
        private Integer requiredProgress;
        private boolean canClaim;
        private Long lastClaimTime;
        private Integer claimCount;
        private Integer maxClaimCount;
        
        // Getters and Setters
        public String getChestId() { return chestId; }
        public void setChestId(String chestId) { this.chestId = chestId; }
        
        public String getChestName() { return chestName; }
        public void setChestName(String chestName) { this.chestName = chestName; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public Integer getCurrentProgress() { return currentProgress; }
        public void setCurrentProgress(Integer currentProgress) { this.currentProgress = currentProgress; }
        
        public Integer getRequiredProgress() { return requiredProgress; }
        public void setRequiredProgress(Integer requiredProgress) { this.requiredProgress = requiredProgress; }
        
        public boolean isCanClaim() { return canClaim; }
        public void setCanClaim(boolean canClaim) { this.canClaim = canClaim; }
        
        public Long getLastClaimTime() { return lastClaimTime; }
        public void setLastClaimTime(Long lastClaimTime) { this.lastClaimTime = lastClaimTime; }
        
        public Integer getClaimCount() { return claimCount; }
        public void setClaimCount(Integer claimCount) { this.claimCount = claimCount; }
        
        public Integer getMaxClaimCount() { return maxClaimCount; }
        public void setMaxClaimCount(Integer maxClaimCount) { this.maxClaimCount = maxClaimCount; }
    }
}
