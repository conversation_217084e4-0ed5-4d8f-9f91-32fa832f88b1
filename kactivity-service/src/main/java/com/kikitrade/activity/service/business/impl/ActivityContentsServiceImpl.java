package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.dal.mysql.dao.ActivityContentsDao;
import com.kikitrade.activity.service.meta.ActivityContentsService;
import com.kikitrade.activity.dal.mysql.model.ActivityContents;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityContentsServiceImpl implements ActivityContentsService {


    @Resource
    private ActivityContentsDao activityContentsDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ActivityContents activityContents) throws Exception {
        log.info("ActivityContentsServiceImpl save begin :{}", JSONObject.toJSONString(activityContents));
        return activityContentsDao.insert(activityContents) > 0;
    }

    @Override
    public boolean update(ActivityContents activityContents) throws Exception {
        log.info("ActivityContentsServiceImpl update begin :{}", JSONObject.toJSONString(activityContents));
        return activityContentsDao.update(activityContents) > 0;
    }

    @Override
    public boolean delete(Integer activityId, String locale) throws Exception {
        log.info("ActivityContentsServiceImpl delete begin :activityId {} locale {}", activityId, locale);
        return activityContentsDao.deleteByPara(activityId, locale) > 0;
    }

    @Override
    public List<ActivityContents> findById(Integer activityId, String saasId) {
        log.info("ActivityContentsServiceImpl findById begin :{}", activityId);
        return activityContentsDao.findById(activityId,saasId);
    }

    @Override
    public ActivityContents findByPara(Integer activityId, String locale) {
        log.info("ActivityContentsServiceImpl findByPara begin :activityId {} locale:{} ", activityId, locale);
        return activityContentsDao.findByPara(activityId, locale);
    }
}
