package com.kikitrade.activity.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.mq.TopicConfig;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import com.kikitrade.framework.ons.OnsProducer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@Slf4j
public class ActivityRealTimeRewardTccServiceImpl implements ActivityRealTimeRewardTccService {

    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private RewardService rewardService;
    @Autowired
    private KiKiMonitor kiKiMonitor;
    @Resource
    @Lazy
    private OnsProducer onsProducer;
    @Resource
    private TopicConfig topicConfig;

    @Compensation(confirmMethod = "confirmReward", cancelMethod = "cancelReward")
    public void reward(LauncherParameter launcherParameter) throws Exception {
        log.info("[reward] request:{}", JSON.toJSONString(launcherParameter));
        ActivityCustomReward activityCustomReward = launcherParameter.getActivityCustomReward();
        activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.AWARDING.name());

        if (!insertOrUpdateReward(activityCustomReward)) {
            log.warn("[reward] Failed to insert or update reward: {}", activityCustomReward);
            return;
        }

        if (shouldSendRewardRequest(launcherParameter)) {
            log.info("[reward] Sending reward request for: {}", activityCustomReward);
            sendRewardRequest(activityCustomReward);
        }
    }

    private boolean insertOrUpdateReward(ActivityCustomReward activityCustomReward) {
        log.info("[insertOrUpdateReward] Inserting reward: {}", activityCustomReward);
        boolean success = activityCustomRewardStoreBuilder.insert(activityCustomReward);
        if (!success) {
            log.warn("[insertOrUpdateReward] Insert failed, attempting to update reward status");
            ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByPrimaryId(
                    activityCustomReward.getBatchId(),
                    activityCustomReward.getCustomerId(),
                    activityCustomReward.getSeq()
            );
            if (reward != null && ActivityConstant.RewardStatusEnum.AWARD_FAILED.name().equals(reward.getStatus())) {
                reward.setStatus(ActivityConstant.RewardStatusEnum.AWARDING.name());
                success = activityCustomRewardStoreBuilder.updateStatus(reward);
                log.info("[insertOrUpdateReward] Updated reward status to AWARDING: {}", reward);
            }
        }
        return success;
    }

    private boolean shouldSendRewardRequest(LauncherParameter launcherParameter) {
        boolean shouldSend = launcherParameter.getProvideType() == null
                || launcherParameter.getProvideType() == ActivityTaskConstant.ProvideType.auto
                || launcherParameter.getProvideType() == ActivityTaskConstant.ProvideType.claim_reward_confirm
                || launcherParameter.getProvideType() == ActivityTaskConstant.ProvideType.claim_task;
        log.info("[shouldSendRewardRequest] Should send reward request: {}", shouldSend);
        return shouldSend;
    }

    private void sendRewardRequest(ActivityCustomReward activityCustomReward) throws Exception {
        RewardRequest request = RewardRequest.builder()
                .customerId(activityCustomReward.getCustomerId())
                .address(activityCustomReward.getAddress())
                .rewardId(activityCustomReward.getBusinessId())
                .amount(new BigDecimal(activityCustomReward.getAmount()))
                .currency(activityCustomReward.getCurrency())
                .type(activityCustomReward.getRewardType())
                .businessType(activityCustomReward.getBusinessType())
                .desc(activityCustomReward.getExtendParamMap().getOrDefault("desc", ""))
                .receiveEndTime(Long.parseLong(activityCustomReward.getExtendParamMap().getOrDefault("receiveEndTime", "0")))
                .saasId(activityCustomReward.getSaasId())
                .build();
        log.info("[sendRewardRequest] Sending reward request: {}", request);
        rewardService.reward(request);
    }

    public void confirmReward(LauncherParameter launcherParameter) throws Exception {
        log.info("Confirming reward with launcherParameter: {}", launcherParameter);
        if (shouldProcessReward(launcherParameter)) {
            ActivityCustomReward reward = findReward(launcherParameter);
            if (isRewardInAwardingStatus(reward)) {
                updateRewardToSuccess(reward);
                sendDivideMqIfNeeded(launcherParameter, reward);
            }
        }
    }

    private boolean shouldProcessReward(LauncherParameter launcherParameter) {
        boolean shouldProcess = launcherParameter.getProvideType() == null
                || launcherParameter.getProvideType() == ActivityTaskConstant.ProvideType.auto
                || launcherParameter.getProvideType() == ActivityTaskConstant.ProvideType.claim_task;
        log.info("Should process reward: {}", shouldProcess);
        return shouldProcess;
    }

    private ActivityCustomReward findReward(LauncherParameter launcherParameter) {
        log.info("Finding reward with batchId: {}, customerId: {}, seq: {}",
                launcherParameter.getActivityCustomReward().getBatchId(),
                launcherParameter.getActivityCustomReward().getCustomerId(),
                launcherParameter.getActivityCustomReward().getSeq());
        return activityCustomRewardStoreBuilder.findByPrimaryId(
                launcherParameter.getActivityCustomReward().getBatchId(),
                launcherParameter.getActivityCustomReward().getCustomerId(),
                launcherParameter.getActivityCustomReward().getSeq()
        );
    }

    private boolean isRewardInAwardingStatus(ActivityCustomReward reward) {
        boolean isInAwardingStatus = reward != null && ActivityConstant.RewardStatusEnum.AWARDING.name().equals(reward.getStatus());
        log.info("Is reward in awarding status: {}", isInAwardingStatus);
        return isInAwardingStatus;
    }

    private void updateRewardToSuccess(ActivityCustomReward reward) {
        log.info("Updating reward to success: {}", reward);
        reward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name());
        reward.setRewardTime(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        activityCustomRewardStoreBuilder.updateStatus(reward);
    }

    private void sendDivideMqIfNeeded(LauncherParameter launcherParameter, ActivityCustomReward reward) {
        SaasConfig saasConfig = SaasConfigLoader.getConfig(launcherParameter.getActivityCustomReward().getSaasId());
        if (shouldSendDivideMq(saasConfig, reward, launcherParameter)) {
            log.info("Sending divide MQ for launcherParameter: {}", launcherParameter);
            onsProducer.send(topicConfig.getTopicRewardDivide(), JSON.toJSONString(launcherParameter));
        }
    }

    private boolean shouldSendDivideMq(SaasConfig saasConfig, ActivityCustomReward reward, LauncherParameter launcherParameter) {
        boolean shouldSend = BooleanUtils.isTrue(saasConfig.getDividePoint())
                && BigDecimal.valueOf(saasConfig.getDividePointPercent()).multiply(new BigDecimal(reward.getAmount())).compareTo(BigDecimal.ZERO) > 0
                && !AssetBusinessType.INVITE_DIVIDE.name().equalsIgnoreCase(launcherParameter.getActivityCustomReward().getBusinessType())
                && !AssetBusinessType.REGISTRATION.name().equalsIgnoreCase(launcherParameter.getActivityCustomReward().getBusinessType())
                && !AssetBusinessType.INVITED_REGISTER.name().equalsIgnoreCase(launcherParameter.getActivityCustomReward().getBusinessType())
                && (ActivityConstant.AwardTypeEnum.POINT.name().equalsIgnoreCase(launcherParameter.getActivityCustomReward().getRewardType())
                    || ActivityConstant.AwardTypeEnum.AURA.name().equalsIgnoreCase(launcherParameter.getActivityCustomReward().getRewardType()));
        log.info("Should send divide MQ: {}", shouldSend);
        return shouldSend;
    }

    public void cancelReward(LauncherParameter launcherParameter) throws Exception {
        log.info("Cancel reward called with launcherParameter: {}", launcherParameter);
        if (isAutoProvideType(launcherParameter)) {
            log.warn("realTimeReward failed: {}", launcherParameter);
            monitorRewardFail(launcherParameter);
            updateRewardStatusToFailed(launcherParameter);
        }
    }

    private boolean isAutoProvideType(LauncherParameter launcherParameter) {
        boolean isAuto = launcherParameter.getProvideType() == null || launcherParameter.getProvideType() == ActivityTaskConstant.ProvideType.auto;
        log.info("isAutoProvideType: {}", isAuto);
        return isAuto;
    }

    private void monitorRewardFail(LauncherParameter launcherParameter) {
        if (launcherParameter.getActivityCustomReward().getSaasId() != null && launcherParameter.getActivityCustomReward().getBusinessType() != null) {
            log.info("Monitoring reward fail for saasId: {}, businessType: {}", launcherParameter.getActivityCustomReward().getSaasId(), launcherParameter.getActivityCustomReward().getBusinessType());
            kiKiMonitor.monitor(BusinessMonitorConstant.EVENT, new String[]{
                    "saasId", launcherParameter.getActivityCustomReward().getSaasId(),
                    "code", launcherParameter.getActivityCustomReward().getBusinessType(),
                    "stage", "reward-fail"
            });
        }
    }

    private void updateRewardStatusToFailed(LauncherParameter launcherParameter) {
        log.info("Updating reward status to failed for launcherParameter: {}", launcherParameter);
        ActivityCustomReward reward = activityCustomRewardStoreBuilder.findByPrimaryId(
                launcherParameter.getActivityCustomReward().getBatchId(),
                launcherParameter.getActivityCustomReward().getCustomerId(),
                launcherParameter.getActivityCustomReward().getSeq()
        );
        if (reward != null && ActivityConstant.RewardStatusEnum.AWARDING.name().equals(reward.getStatus())) {
            log.info("Reward found and status is AWARDING, updating status to AWARD_FAILED");
            reward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name());
            activityCustomRewardStoreBuilder.updateStatus(reward);
        } else {
            log.warn("Reward not found or status is not AWARDING");
        }
    }
}
