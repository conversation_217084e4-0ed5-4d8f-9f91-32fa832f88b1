package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.dal.tablestore.model.PackageConfig;
import com.kikitrade.activity.dal.tablestore.model.UserPackageEntitlement;

import java.util.List;

/**
 * 礼包管理服务接口
 * 提供礼包的配置管理、权益授予、发放等核心功能
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface PackageService {
    
    /**
     * 授予用户礼包权益
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @param grantReason 授予原因
     * @return 是否成功
     */
    boolean grantPackEntitlement(String userId, String saasId, String packageId, String grantReason);
    
    /**
     * 批量授予用户礼包权益
     * 
     * @param userIds 用户ID列表
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @param grantReason 授予原因
     * @return 成功授予的用户数量
     */
    int batchGrantPackEntitlement(List<String> userIds, String saasId, String packageId, String grantReason);
    
    /**
     * 发放礼包（用户领取）
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @param claimChannel 领取渠道
     * @param claimIp 领取IP
     * @param claimDevice 领取设备
     * @return 发放结果
     */
    PackageIssueResult issuePack(String userId, String saasId, String packageId, 
                                String claimChannel, String claimIp, String claimDevice);
    
    /**
     * 查询用户礼包权益列表
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param status 状态过滤（可选）
     * @return 权益列表
     */
    List<UserPackageEntitlement> getUserPackageEntitlements(String userId, String saasId, String status);
    
    /**
     * 查询用户特定礼包权益
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @return 权益信息
     */
    UserPackageEntitlement getUserPackageEntitlement(String userId, String saasId, String packageId);
    
    /**
     * 检查用户是否有礼包权益
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @return 是否有权益
     */
    boolean hasPackageEntitlement(String userId, String saasId, String packageId);
    
    /**
     * 检查礼包是否可以领取
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @return 是否可以领取
     */
    boolean canClaimPackage(String userId, String saasId, String packageId);
    
    /**
     * 获取礼包配置
     * 
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @return 礼包配置
     */
    PackageConfig getPackageConfig(String saasId, String packageId);
    
    /**
     * 获取活跃的礼包配置列表
     * 
     * @param saasId SaaS ID
     * @param packageType 礼包类型（可选）
     * @return 礼包配置列表
     */
    List<PackageConfig> getActivePackageConfigs(String saasId, String packageType);
    
    /**
     * 自动检查并授予符合条件的礼包
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param triggerEvent 触发事件
     * @return 授予的礼包数量
     */
    int autoGrantPackages(String userId, String saasId, String triggerEvent);
    
    /**
     * 撤销用户礼包权益
     * 
     * @param userId 用户ID
     * @param saasId SaaS ID
     * @param packageId 礼包ID
     * @param reason 撤销原因
     * @return 是否成功
     */
    boolean revokePackageEntitlement(String userId, String saasId, String packageId, String reason);
    
    /**
     * 清理过期的礼包权益
     * 
     * @param saasId SaaS ID
     * @return 清理的权益数量
     */
    int cleanupExpiredEntitlements(String saasId);
    
    /**
     * 礼包发放结果
     */
    class PackageIssueResult {
        private boolean success;
        private String errorCode;
        private String message;
        private List<String> issuedPrizes;
        private String entitlementId;
        
        // 构造方法和getter/setter
        public PackageIssueResult(boolean success, String errorCode, String message) {
            this.success = success;
            this.errorCode = errorCode;
            this.message = message;
        }
        
        public PackageIssueResult(boolean success, String message, List<String> issuedPrizes, String entitlementId) {
            this.success = success;
            this.message = message;
            this.issuedPrizes = issuedPrizes;
            this.entitlementId = entitlementId;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public List<String> getIssuedPrizes() { return issuedPrizes; }
        public void setIssuedPrizes(List<String> issuedPrizes) { this.issuedPrizes = issuedPrizes; }
        
        public String getEntitlementId() { return entitlementId; }
        public void setEntitlementId(String entitlementId) { this.entitlementId = entitlementId; }
    }
}
