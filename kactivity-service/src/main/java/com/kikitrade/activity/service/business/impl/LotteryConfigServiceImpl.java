package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.api.model.LotteryConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.LotteryConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.service.business.LotteryConfigService;
import com.kikitrade.framework.common.util.BeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/6 15:17
 */
@Service
public class LotteryConfigServiceImpl implements LotteryConfigService {

    @Resource
    private LotteryConfigBuilder lotteryConfigBuilder;

    @Override
    public LotteryConfigDTO findByCode(String code) {
        LotteryConfig config = lotteryConfigBuilder.findByCode(code);
        if(config == null){
            return null;
        }
        return BeanUtil.copyProperties(config, LotteryConfigDTO::new);
    }
}
