package com.kikitrade.activity.service.business.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.CouponConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.CouponConfig;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.business.ClaimItemService;
import com.kikitrade.activity.service.model.CouponClaimInfo;
import com.kikitrade.order.api.RemoteOrderService;
import com.kikitrade.order.model.constant.OrderEventEnum;
import com.kikitrade.order.model.exception.OrderException;
import com.kikitrade.order.model.request.PlaceOrderRequest;
import com.kikitrade.order.model.response.PlaceOrderResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem.DEFAULT_CYCLE;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/8 11:32
 */
@Service
@Slf4j
public class ClaimRedeemItemServiceImpl implements ClaimItemService {

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @Resource
    private CouponConfigBuilder couponConfigBuilder;

    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;


    @Override
    public Boolean isContinue(String businessType) {
        return "redeem".equals(businessType);
    }

    @Override
    public Boolean allowClaim(String businessType, String customerId, String address, List<String> addresses) {
        return true;
    }

    @Override
    public ClaimResponse claim(String businessType, String address, String customerId, String code, String saasId, List<String> addresses) throws ActivityException {
        if(StringUtils.isEmpty(code)){
            throw new ActivityException(ActivityResponseCode.CLAIM_CODE_INVALID);
        }
        code = code.toLowerCase();
        CouponConfig config = couponConfigBuilder.getConfigByPK(code, saasId, businessType);
        if(config == null) {
            throw new ActivityException(ActivityResponseCode.CLAIM_CODE_INVALID);
        }

        boolean success = activityTaskItemBuilder.exist(customerId, config.getTaskId(), DEFAULT_CYCLE, code);
        if(success){
            throw new ActivityException(ActivityResponseCode.CLAIM_REPEAT);
        }

        PlaceOrderRequest orderRequest = new PlaceOrderRequest();
        orderRequest.setEvent(OrderEventEnum.redeem);
        orderRequest.setSaasId(saasId);
        orderRequest.setCustomerId(customerId);

        CouponClaimInfo couponClaimInfo = new CouponClaimInfo();
        BeanUtils.copyProperties(config, couponClaimInfo);
        couponClaimInfo.setCustomerId(customerId);
        orderRequest.setParam(JSON.toJSONString(couponClaimInfo));
        //生成订单
        try {
            PlaceOrderResponse orderResponse = remoteOrderService.placeOrder(orderRequest);
            if(orderResponse == null || orderResponse.getOrderId() == null){
                throw new RuntimeException("生成订单失败");
            }
            ClaimResponse claimResponse = new ClaimResponse();
            claimResponse.setSuccess(true);
            claimResponse.setOrderId(orderResponse.getOrderId());
            claimResponse.setCurrency(config.getAwardCurrency());
            claimResponse.setReward(new BigDecimal(config.getAwardAmount()));
            return claimResponse;
        } catch (OrderException e) {
            throw new RuntimeException(e);
        }
    }
}
