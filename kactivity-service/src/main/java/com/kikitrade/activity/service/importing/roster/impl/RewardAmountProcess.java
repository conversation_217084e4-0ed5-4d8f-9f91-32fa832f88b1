package com.kikitrade.activity.service.importing.roster.impl;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.kcustomer.common.constants.CustomerConstants;
import com.kikitrade.market.client.TickerClient;
import com.kikitrade.market.common.model.TickerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@Order(5)
public class RewardAmountProcess extends AbstractRewardProcess {

    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private TickerClient tickerClient;

    @Override
    boolean support(ActivityEntity activityEntity, ActivityBatch activityBatch, CustomerCacheDTO customerDO) {
        return activityEntity != null && activityBatch != null && customerDO != null;
    }

    @Override
    void doProcess(ActivityEntity activityEntity, ActivityBatch activityBatch, ActivityCustomReward activityCustomReward, CustomerCacheDTO customerDO) {
        if(activityCustomReward.getAmount()!= null && new BigDecimal(activityCustomReward.getAmount()).compareTo(BigDecimal.ZERO) > 0){
            if(StringUtils.isNoneBlank(activityCustomReward.getAmount()) && "Token".equalsIgnoreCase(activityCustomReward.getRewardType())){
                if(kactivityProperties.getRewardStatisticsCurrency().equals(activityCustomReward.getCurrency())){
                    activityCustomReward.setCost(new BigDecimal(activityCustomReward.getAmount()));
                }else{
                    TickerDTO tickerDTO = tickerClient.get(String.format("%s_%s", activityCustomReward.getCurrency(), kactivityProperties.getRewardStatisticsCurrency()));
                    activityCustomReward.setCost(new BigDecimal(activityCustomReward.getAmount()).multiply(tickerDTO.getPrice()));
                }
            }else{
                //如果是积分，cost == amount
                activityCustomReward.setCost(new BigDecimal(activityCustomReward.getAmount()));
            }
            return;
        }
        ActivityConstant.ActivityTypeEnum value = ActivityConstant.ActivityTypeEnum.value(activityEntity.getType());
        Optional<List<ActivityBatch.Rule>> rules;
        switch (value){
            case INVITE:
                rules = filterRules(activityBatch, activityCustomReward);
                if(rules.isPresent()){
                    List<ActivityBatch.Rule> ruleOptional = rules.get().stream().filter(r
                            -> activityCustomReward.getSide().equalsIgnoreCase(r.getSide()))
                            .collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(ruleOptional)){
                        activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                        activityCustomReward.setMessage("side of rules not match");
                    }else{
                        Optional<ActivityBatch.Rule> rule = ruleOptional.stream().sorted((o1, o2) -> o2.getVipLevel().compareTo(o1.getVipLevel())).filter(r
                                -> CustomerConstants.Type.fromCode(customerDO.getType()).name().equalsIgnoreCase(r.getUserType()) && (StringUtils.isBlank(r.getVipLevel()) || r.getVipLevel().equals(activityCustomReward.getVipLevel()))).findFirst();
                        if(rule.isPresent()){
                            activityCustomReward.setAmount(rule.get().getAwardAmount());
                            activityCustomReward.setCurrency(rule.get().getAward());
                            activityCustomReward.setRewardType(rule.get().getAwardType().toUpperCase());
                        }else{
                            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                            activityCustomReward.setMessage("user_type of rules not match");
                        }
                    }
                }
                break;
            case CUSTOMIZE:
                if(StringUtils.isBlank(activityCustomReward.getAmount()) || !NumberUtils.isNumber(activityCustomReward.getAmount())){
                    activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                    activityCustomReward.setMessage("amount format error，rules not match");
                }
                break;
            default:
                rules = filterRules(activityBatch, activityCustomReward);
                if(rules.isPresent()){
                    Optional<ActivityBatch.Rule> ruleOptional = rules.get().stream().sorted((o1, o2) -> o2.getVipLevel().compareTo(o1.getVipLevel())).filter(r
                            -> CustomerConstants.Type.fromCode(customerDO.getType()).name().equalsIgnoreCase(r.getUserType()) && (StringUtils.isBlank(r.getVipLevel()) || r.getVipLevel().equals(activityCustomReward.getVipLevel()))).findFirst();
                    if(ruleOptional.isPresent()){
                        activityCustomReward.setAmount(ruleOptional.get().getAwardAmount());
                        activityCustomReward.setCurrency(ruleOptional.get().getAward());
                        activityCustomReward.setRewardType(ruleOptional.get().getAwardType().toUpperCase());
                        activityCustomReward.setLevel(ruleOptional.get().getLevel());
                    }else{
                        activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                        activityCustomReward.setMessage("user_type of rules not match");
                    }
                }
                break;
        }
        if(StringUtils.isNoneBlank(activityCustomReward.getAmount())){
            activityCustomReward.setCost(new BigDecimal(activityCustomReward.getAmount()));
            if("Token".equalsIgnoreCase(activityCustomReward.getRewardType())){
                if(kactivityProperties.getRewardStatisticsCurrency().equals(activityCustomReward.getCurrency())){
                    activityCustomReward.setCost(new BigDecimal(activityCustomReward.getAmount()));
                }else{
                    TickerDTO tickerDTO = tickerClient.get(String.format("%s_%s", activityCustomReward.getCurrency(), kactivityProperties.getRewardStatisticsCurrency()));
                    activityCustomReward.setCost(new BigDecimal(activityCustomReward.getAmount()).multiply(tickerDTO.getPrice()));
                }
            }
        }

    }

    private Optional<List<ActivityBatch.Rule>> filterRules(ActivityBatch activityBatch, ActivityCustomReward activityCustomReward){
        try{
            if(CollectionUtils.isEmpty(activityBatch.getRules())){
                activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                activityCustomReward.setMessage("amount format error，rules not match");
                return Optional.empty();
            }
            List<ActivityBatch.Rule> rules = activityBatch.getRules().stream().sorted((o1, o2) -> o2.getMin().compareTo(o1.getMin())).filter(r
                    -> ((r.getMax() != null && Double.parseDouble(activityCustomReward.getScope()) >= r.getMin() && Double.parseDouble(activityCustomReward.getScope()) < r.getMax())
                    || (r.getMax() == null && Double.parseDouble(activityCustomReward.getScope()) >= r.getMin()))).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(rules)){
                activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
                activityCustomReward.setMessage("amount format error，rules not match");
                return Optional.empty();
            }
            return Optional.of(rules);
        }catch (Exception ex){
            log.error("filterRules error:{},{}", activityBatch, activityCustomReward, ex);
            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.VALID_FAIL.name());
            activityCustomReward.setMessage("amount format exception，rules not match");
            return Optional.empty();
        }
    }
}
