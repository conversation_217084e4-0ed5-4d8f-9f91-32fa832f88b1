package com.kikitrade.activity.service.auth;

import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.auth.impl.AuthServiceImpl;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.knotify.api.NotifyResult;
import com.kikitrade.knotify.api.NotifyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 验证码发送频率限制测试类
 * 测试60秒内只能发送一次验证码的功能
 * 
 * <AUTHOR>
 * @date 2025/8/18
 */
@ExtendWith(MockitoExtension.class)
class EmailVerifyCodeFrequencyTest {

    @Mock
    private RedisService redisService;

    @Mock
    private NotifyService notifyService;

    @Mock
    private KactivityProperties kactivityProperties;

    @InjectMocks
    private AuthServiceImpl authService;

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_SAAS_ID = "test-saas";
    private static final String TEST_CUSTOMER_ID = "test-customer";
    private static final String TEST_CUSTOMER_NAME = "test-user";
    private static final String TEST_CHANNEL = "email";
    private static final String TEST_SCENE_CODE = "social_bind_email";

    @BeforeEach
    void setUp() {
        // 模拟配置属性
//        when(kactivityProperties.getVerifyCodeExpireMinutes()).thenReturn(5);
        
        // 模拟邮件发送成功
        NotifyResult notifyResult = new NotifyResult();
//        notifyResult.setSuccess(true);
//        when(notifyService.sendEmail(any())).thenReturn(notifyResult);
    }

    @Test
    void testSendVerifyCodeFirstTime() throws ActivityException {
        // 第一次发送验证码，Redis中没有发送记录
        when(redisService.get(anyString())).thenReturn(null);

        Boolean result = authService.sendVerifyCode(TEST_SAAS_ID, TEST_CUSTOMER_ID, 
                TEST_CUSTOMER_NAME, TEST_CHANNEL, TEST_EMAIL, TEST_SCENE_CODE);

        assertTrue(result);
        
        // 验证Redis操作
        verify(redisService, times(1)).get(anyString()); // 检查发送频率
        verify(redisService, times(1)).hSetAll(anyString(), any(Map.class)); // 存储验证码
//        verify(redisService, times(1)).set(anyString(), anyString()); // 记录发送时间
        verify(redisService, times(2)).expire(anyString(), anyInt()); // 设置过期时间
        
        // 验证邮件发送
//        verify(notifyService, times(1)).sendEmail(any());
    }

    @Test
    void testSendVerifyCodeTooFrequent() {
        // 模拟60秒内已经发送过验证码
        long currentTime = System.currentTimeMillis();
        String lastSendTime = String.valueOf(currentTime - 30 * 1000); // 30秒前发送过
        when(redisService.get(anyString())).thenReturn(lastSendTime);

        ActivityException exception = assertThrows(ActivityException.class, () -> {
            authService.sendVerifyCode(TEST_SAAS_ID, TEST_CUSTOMER_ID, 
                    TEST_CUSTOMER_NAME, TEST_CHANNEL, TEST_EMAIL, TEST_SCENE_CODE);
        });

        assertEquals(ActivityResponseCode.EMAIL_VERIFY_CODE_SEND_TOO_FREQUENT, exception.getCode());
        
        // 验证没有发送邮件
//        verify(notifyService, never()).sendEmail(any());
        // 验证没有存储验证码
//        verify(redisService, never()).hSetAll(anyString(), any(Map.class));
        // 验证没有记录发送时间
//        verify(redisService, never()).set(anyString(), anyString());
    }

    @Test
    void testSendVerifyCodeAfter60Seconds() throws ActivityException {
        // 模拟60秒后再次发送验证码
        long currentTime = System.currentTimeMillis();
        String lastSendTime = String.valueOf(currentTime - 61 * 1000); // 61秒前发送过
        when(redisService.get(anyString())).thenReturn(lastSendTime);

        Boolean result = authService.sendVerifyCode(TEST_SAAS_ID, TEST_CUSTOMER_ID,
                TEST_CUSTOMER_NAME, TEST_CHANNEL, TEST_EMAIL, TEST_SCENE_CODE);

        assertTrue(result);
        
        // 验证Redis操作
        verify(redisService, times(1)).get(anyString()); // 检查发送频率
        verify(redisService, times(1)).hSetAll(anyString(), any(Map.class)); // 存储验证码
//        verify(redisService, times(1)).set(anyString(), anyString()); // 记录发送时间
        
        // 验证邮件发送
//        verify(notifyService, times(1)).sendEmail(any());
    }

    @Test
    void testSendVerifyCodeWithEmptyEmail() throws ActivityException {
        // 测试空邮箱地址的情况
        Boolean result = authService.sendVerifyCode(TEST_SAAS_ID, TEST_CUSTOMER_ID, 
                TEST_CUSTOMER_NAME, TEST_CHANNEL, "", TEST_SCENE_CODE);

        assertTrue(result);
        
        // 空邮箱不会检查发送频率
        verify(redisService, never()).get(anyString());
    }

    @Test
    void testSendVerifyCodeWithRedisException() throws ActivityException {
        // 模拟Redis异常，应该允许发送（异常时跳过验证）
        when(redisService.get(anyString())).thenThrow(new RuntimeException("Redis error"));

        Boolean result = authService.sendVerifyCode(TEST_SAAS_ID, TEST_CUSTOMER_ID, 
                TEST_CUSTOMER_NAME, TEST_CHANNEL, TEST_EMAIL, TEST_SCENE_CODE);

        assertTrue(result);
        
        // 验证邮件发送
//        verify(notifyService, times(1)).sendEmail(any());
    }

    @Test
    void testSendVerifyCodeEmailSendFailed() throws ActivityException {
        // 模拟邮件发送失败
        when(redisService.get(anyString())).thenReturn(null);
        
        NotifyResult failedResult = new NotifyResult();
//        failedResult.setSuccess(false);
//        when(notifyService.sendEmail(any())).thenReturn(failedResult);

        Boolean result = authService.sendVerifyCode(TEST_SAAS_ID, TEST_CUSTOMER_ID, 
                TEST_CUSTOMER_NAME, TEST_CHANNEL, TEST_EMAIL, TEST_SCENE_CODE);

        assertFalse(result);
        
        // 验证邮件发送失败时不记录发送时间
//        verify(redisService, never()).set(anyString(), anyString());
    }

    @Test
    void testSendVerifyCodeExactly60Seconds() throws ActivityException {
        // 测试恰好60秒的边界情况
        long currentTime = System.currentTimeMillis();
        String lastSendTime = String.valueOf(currentTime - 60 * 1000); // 恰好60秒前
        when(redisService.get(anyString())).thenReturn(lastSendTime);

        Boolean result = authService.sendVerifyCode(TEST_SAAS_ID, TEST_CUSTOMER_ID, 
                TEST_CUSTOMER_NAME, TEST_CHANNEL, TEST_EMAIL, TEST_SCENE_CODE);

        assertTrue(result);
        
        // 验证邮件发送
//        verify(notifyService, times(1)).sendEmail(any());
    }
}
