package com.kikitrade.activity.service.remote;

import com.kikitrade.activity.api.model.response.SocialAuthUrlResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23 11:00
 * @description: 社媒授权URL逻辑测试，不依赖任何框架
 */
public class SocialAuthUrlLogicTest {

    public static void main(String[] args) {
        SocialAuthUrlLogicTest test = new SocialAuthUrlLogicTest();
        
        System.out.println("=== 社媒授权URL逻辑测试开始 ===");
        
        try {
            test.testSocialAuthUrlCreation();
            test.testAuthUrlFormatting();
            test.testPlatformOrdering();
            test.testIconUrlGeneration();
            System.out.println("=== 所有测试通过！ ===");
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void testSocialAuthUrlCreation() {
        System.out.println("\n1. 测试社媒授权URL对象创建");
        
        // 测试创建Twitter授权URL
        SocialAuthUrlResponse twitterAuth = SocialAuthUrlResponse.builder()
            .platform("twitter")
            .platformName("Twitter")
            .authUrl("https://twitter.com/i/oauth2/authorize?client_id=test123")
            .iconUrl("https://example.com/icons/twitter.webp")
            .enabled(true)
            .order(1)
            .build();
        
        assert "twitter".equals(twitterAuth.getPlatform()) : "Twitter平台名称错误";
        assert twitterAuth.getAuthUrl().contains("client_id=test123") : "Twitter授权URL格式错误";
        assert twitterAuth.getEnabled() : "Twitter应该是启用状态";
        assert twitterAuth.getOrder() == 1 : "Twitter排序应该是1";
        
        System.out.println("   ✓ Twitter授权URL创建测试通过");
        System.out.println("   - 平台: " + twitterAuth.getPlatform());
        System.out.println("   - 授权URL: " + twitterAuth.getAuthUrl());
        System.out.println("   - 启用状态: " + twitterAuth.getEnabled());
        System.out.println("   - 排序: " + twitterAuth.getOrder());
    }
    
    public void testAuthUrlFormatting() {
        System.out.println("\n2. 测试授权URL格式化");
        
        String clientId = "test_client_123";
        String authScore = "https://twitter.com/i/oauth2/authorize?response_type=code&scope=tweet.read%20users.read";
        
        // 模拟Twitter授权URL构建
        String twitterAuthUrl = String.format("%s&client_id=%s", authScore, clientId);
        
        assert twitterAuthUrl.contains("response_type=code") : "授权URL应包含response_type";
        assert twitterAuthUrl.contains("client_id=" + clientId) : "授权URL应包含client_id";
        assert twitterAuthUrl.contains("scope=") : "授权URL应包含scope";
        
        System.out.println("   ✓ 授权URL格式化测试通过");
        System.out.println("   - 客户端ID: " + clientId);
        System.out.println("   - 基础授权URL: " + authScore);
        System.out.println("   - 完整授权URL: " + twitterAuthUrl);
        
        // 测试Discord授权URL
        String discordClientId = "discord_client_456";
        String discordAuthUrl = String.format("https://discord.com/api/oauth2/authorize?client_id=%s&response_type=code&scope=identify", discordClientId);
        
        assert discordAuthUrl.contains("discord.com") : "Discord URL应包含discord.com";
        assert discordAuthUrl.contains("scope=identify") : "Discord URL应包含identify scope";
        
        System.out.println("   ✓ Discord授权URL格式化测试通过");
        System.out.println("   - Discord授权URL: " + discordAuthUrl);
    }
    
    public void testPlatformOrdering() {
        System.out.println("\n3. 测试平台排序逻辑");
        
        List<SocialAuthUrlResponse> authUrls = new ArrayList<>();
        
        // 添加各个平台（故意打乱顺序）
        authUrls.add(createAuthUrl("wallet", "Wallet", "", 8));
        authUrls.add(createAuthUrl("twitter", "Twitter", "https://twitter.com/oauth", 1));
        authUrls.add(createAuthUrl("email", "Email", "", 6));
        authUrls.add(createAuthUrl("discord", "Discord", "https://discord.com/oauth", 2));
        authUrls.add(createAuthUrl("telegram", "Telegram", "", 7));
        authUrls.add(createAuthUrl("google", "Google", "https://google.com/oauth", 3));
        
        // 按order排序
        authUrls.sort((a, b) -> Integer.compare(a.getOrder(), b.getOrder()));
        
        // 验证排序结果
        assert "twitter".equals(authUrls.get(0).getPlatform()) : "第一个应该是Twitter";
        assert "discord".equals(authUrls.get(1).getPlatform()) : "第二个应该是Discord";
        assert "google".equals(authUrls.get(2).getPlatform()) : "第三个应该是Google";
        
        System.out.println("   ✓ 平台排序测试通过");
        System.out.println("   - 排序后的平台顺序:");
        for (int i = 0; i < authUrls.size(); i++) {
            SocialAuthUrlResponse auth = authUrls.get(i);
            System.out.println("     " + (i + 1) + ". " + " (order: " + auth.getOrder() + ")");
        }
    }
    
    public void testIconUrlGeneration() {
        System.out.println("\n4. 测试图标URL生成逻辑");
        
        String saasId = "test-saas";
        String platform = "twitter";
        String iconSuffix = "webp";
        
        // 模拟图标URL生成
        String iconUrl = generateIconUrl(saasId, platform, iconSuffix);
        
        assert iconUrl.contains(saasId) : "图标URL应包含saasId";
        assert iconUrl.contains(platform) : "图标URL应包含平台名称";
        assert iconUrl.endsWith("." + iconSuffix) : "图标URL应以正确的后缀结尾";
        
        System.out.println("   ✓ 图标URL生成测试通过");
        System.out.println("   - SaaS ID: " + saasId);
        System.out.println("   - 平台: " + platform);
        System.out.println("   - 图标后缀: " + iconSuffix);
        System.out.println("   - 生成的图标URL: " + iconUrl);
        
        // 测试不同平台的图标URL
        String[] platforms = {"twitter", "discord", "google", "facebook", "line", "email", "telegram", "wallet"};
        System.out.println("   - 所有平台的图标URL:");
        for (String p : platforms) {
            String url = generateIconUrl(saasId, p, iconSuffix);
            System.out.println("     " + p + ": " + url);
        }
    }
    
    private SocialAuthUrlResponse createAuthUrl(String platform, String platformName, String authUrl, int order) {
        return SocialAuthUrlResponse.builder()
            .platform(platform)
            .platformName(platformName)
            .authUrl(authUrl)
            .iconUrl(generateIconUrl("test-saas", platform, "webp"))
            .enabled(true)
            .order(order)
            .build();
    }
    
    private String generateIconUrl(String saasId, String platform, String iconSuffix) {
        // 模拟OSS URL生成逻辑
        return String.format("https://example-oss.com/social/%s/%s.%s", saasId, platform, iconSuffix);
    }
}
