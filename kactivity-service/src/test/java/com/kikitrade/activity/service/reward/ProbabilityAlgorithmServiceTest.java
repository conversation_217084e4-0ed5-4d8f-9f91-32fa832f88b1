package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 概率算法服务测试
 * 验证OVERALL和SINGLE两种概率策略的正确性
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
class ProbabilityAlgorithmServiceTest {

    @InjectMocks
    private ProbabilityAlgorithmService probabilityAlgorithmService;

    private List<PrizeConfig> testPrizes;
    private PrizeConfig fallbackPrize;

    @BeforeEach
    void setUp() {
        // 创建测试奖品配置
        testPrizes = new ArrayList<>();

        // 奖品1：金币，概率30%
        PrizeConfig prize1 = new PrizeConfig();
        prize1.setId("prize_1");
        prize1.setPrizeName("金币");
        prize1.setPrizeType("CURRENCY");
        prize1.setQuantityPerWin(100);
        prize1.setWinningProbability(new BigDecimal("0.3"));
        testPrizes.add(prize1);

        // 奖品2：钻石，概率10%
        PrizeConfig prize2 = new PrizeConfig();
        prize2.setId("prize_2");
        prize2.setPrizeName("钻石");
        prize2.setPrizeType("CURRENCY");
        prize2.setQuantityPerWin(10);
        prize2.setWinningProbability(new BigDecimal("0.1"));
        testPrizes.add(prize2);

        // 奖品3：装备，概率5%
        PrizeConfig prize3 = new PrizeConfig();
        prize3.setId("prize_3");
        prize3.setPrizeName("传说装备");
        prize3.setPrizeType("ITEM");
        prize3.setQuantityPerWin(1);
        prize3.setWinningProbability(new BigDecimal("0.05"));
        testPrizes.add(prize3);

        // 兜底奖品：经验药水
        fallbackPrize = new PrizeConfig();
        fallbackPrize.setId("fallback_prize");
        fallbackPrize.setPrizeName("经验药水");
        fallbackPrize.setPrizeType("ITEM");
        fallbackPrize.setQuantityPerWin(1);
        fallbackPrize.setWinningProbability(new BigDecimal("1.0"));
    }

    @Test
    void testOverallStrategyDraw() {
        // 为OVERALL策略调整概率，使总和为1.0
        testPrizes.get(0).setWinningProbability(new BigDecimal("0.6")); // 金币60%
        testPrizes.get(1).setWinningProbability(new BigDecimal("0.3")); // 钻石30%
        testPrizes.get(2).setWinningProbability(new BigDecimal("0.1")); // 装备10%

        // 执行多次抽奖，验证概率分布
        int totalDraws = 1000;
        int[] winCounts = new int[3];

        for (int i = 0; i < totalDraws; i++) {
            PrizeConfig result = probabilityAlgorithmService.drawWithOverallStrategy(testPrizes);
            assertNotNull(result, "抽奖结果不应为空");

            if ("prize_1".equals(result.getId())) {
                winCounts[0]++;
            } else if ("prize_2".equals(result.getId())) {
                winCounts[1]++;
            } else if ("prize_3".equals(result.getId())) {
                winCounts[2]++;
            }
        }

        // 验证概率分布（允许一定误差）
        double goldRate = (double) winCounts[0] / totalDraws;
        double diamondRate = (double) winCounts[1] / totalDraws;
        double equipmentRate = (double) winCounts[2] / totalDraws;

        System.out.println("OVERALL策略测试结果:");
        System.out.println("金币中奖率: " + goldRate + " (期望: 0.6)");
        System.out.println("钻石中奖率: " + diamondRate + " (期望: 0.3)");
        System.out.println("装备中奖率: " + equipmentRate + " (期望: 0.1)");

        // 允许10%的误差
        assertTrue(Math.abs(goldRate - 0.6) < 0.1, "金币中奖率应接近60%");
        assertTrue(Math.abs(diamondRate - 0.3) < 0.1, "钻石中奖率应接近30%");
        assertTrue(Math.abs(equipmentRate - 0.1) < 0.1, "装备中奖率应接近10%");
    }

    @Test
    void testSingleStrategyDraw() {
        // SINGLE策略：每个奖品独立判断
        testPrizes.get(0).setWinningProbability(new BigDecimal("0.5")); // 金币50%
        testPrizes.get(1).setWinningProbability(new BigDecimal("0.2")); // 钻石20%
        testPrizes.get(2).setWinningProbability(new BigDecimal("0.1")); // 装备10%

        int totalDraws = 1000;
        int fallbackCount = 0;
        int prizeCount = 0;

        for (int i = 0; i < totalDraws; i++) {
            PrizeConfig result = probabilityAlgorithmService.drawWithSingleStrategy(testPrizes, fallbackPrize);
            assertNotNull(result, "抽奖结果不应为空");

            if ("fallback_prize".equals(result.getId())) {
                fallbackCount++;
            } else {
                prizeCount++;
            }
        }

        double fallbackRate = (double) fallbackCount / totalDraws;
        double prizeRate = (double) prizeCount / totalDraws;

        System.out.println("SINGLE策略测试结果:");
        System.out.println("中奖率: " + prizeRate);
        System.out.println("兜底奖品率: " + fallbackRate);

        // SINGLE策略应该有一定比例的兜底奖品
        assertTrue(fallbackRate > 0, "应该有兜底奖品");
        assertTrue(prizeRate > 0, "应该有正常奖品");
    }

    @Test
    void testBatchDraw() {
        // 测试批量抽奖
        testPrizes.get(0).setWinningProbability(new BigDecimal("0.6"));
        testPrizes.get(1).setWinningProbability(new BigDecimal("0.3"));
        testPrizes.get(2).setWinningProbability(new BigDecimal("0.1"));

        int drawCount = 10;
        List<PrizeConfig> results = probabilityAlgorithmService.batchDraw(
                testPrizes, "OVERALL", null, drawCount);

        assertNotNull(results, "批量抽奖结果不应为空");
        assertEquals(drawCount, results.size(), "抽奖次数应该正确");

        // 验证每个结果都不为空
        for (PrizeConfig result : results) {
            assertNotNull(result, "每个抽奖结果都不应为空");
            assertNotNull(result.getPrizeName(), "奖品名称不应为空");
        }

        System.out.println("批量抽奖测试结果:");
        results.forEach(prize -> System.out.println("- " + prize.getPrizeName()));
    }
}