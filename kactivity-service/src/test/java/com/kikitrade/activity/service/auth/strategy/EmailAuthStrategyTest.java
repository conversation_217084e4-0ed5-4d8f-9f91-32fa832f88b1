package com.kikitrade.activity.service.auth.strategy;

import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.auth.strategy.impl.EmailAuthStrategy;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * EmailAuthStrategy测试类
 * 验证邮箱认证策略的功能
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@ExtendWith(MockitoExtension.class)
class EmailAuthStrategyTest {

    @Mock
    private RedisService redisService;

    @InjectMocks
    private EmailAuthStrategy emailAuthStrategy;

    @Test
    void testAuthenticateSuccess() throws ActivityException {
        // 准备测试数据
        String customerId = "test-customer";
        String verifyCode = "123456";
        String email = "<EMAIL>";

        // 模拟Redis中的验证码数据
        Map<String, Object> verifyData = new HashMap<>();
        verifyData.put("code", verifyCode);
        verifyData.put("email", email);
        verifyData.put("timestamp", String.valueOf(System.currentTimeMillis()));

        when(redisService.hGetAllMap(anyString())).thenReturn(verifyData);

        // 构建认证上下文
        AuthContext context = AuthContext.builder()
                .customerId(customerId)
                .code(verifyCode)
                .platform("email")
                .build();

        // 执行认证
        AuthResult result = emailAuthStrategy.authenticate(context);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getToken());
        assertNotNull(result.getSocialUserInfo());
        assertEquals(email, result.getSocialUserInfo().getUserId());
        assertEquals(email, result.getSocialUserInfo().getUserName());
        assertEquals(email, result.getSocialUserInfo().getEmail());

        // 验证Redis删除操作被调用
        verify(redisService).del(RedisKeyConst.ACTIVITY_EMAIL_VERIFY_CODE.getKey(customerId));
    }

    @Test
    void testAuthenticateWithInvalidCode() {
        // 准备测试数据
        String customerId = "test-customer";
        String correctCode = "123456";
        String wrongCode = "654321";
        String email = "<EMAIL>";

        // 模拟Redis中的验证码数据
        Map<String, Object> verifyData = new HashMap<>();
        verifyData.put("code", correctCode);
        verifyData.put("email", email);
        verifyData.put("timestamp", String.valueOf(System.currentTimeMillis()));

        when(redisService.hGetAllMap(anyString())).thenReturn(verifyData);

        // 构建认证上下文（使用错误的验证码）
        AuthContext context = AuthContext.builder()
                .customerId(customerId)
                .code(wrongCode)
                .platform("email")
                .build();

        // 执行认证并验证异常
        assertThrows(ActivityException.class, () -> {
            emailAuthStrategy.authenticate(context);
        });

        // 验证Redis删除操作没有被调用
        verify(redisService, never()).del(anyString());
    }

    @Test
    void testAuthenticateWithExpiredCode() {
        // 准备测试数据 - 空的验证码数据（模拟过期）
        when(redisService.hGetAllMap(anyString())).thenReturn(new HashMap<>());

        // 构建认证上下文
        AuthContext context = AuthContext.builder()
                .customerId("test-customer")
                .code("123456")
                .platform("email")
                .build();

        // 执行认证并验证异常
        assertThrows(ActivityException.class, () -> {
            emailAuthStrategy.authenticate(context);
        });
    }

    @Test
    void testAuthenticateWithIncompleteData() {
        // 准备测试数据 - 不完整的验证码数据
        Map<String, Object> verifyData = new HashMap<>();
        verifyData.put("code", "123456");
        // 缺少email字段

        when(redisService.hGetAllMap(anyString())).thenReturn(verifyData);

        // 构建认证上下文
        AuthContext context = AuthContext.builder()
                .customerId("test-customer")
                .code("123456")
                .platform("email")
                .build();

        // 执行认证并验证异常
        assertThrows(ActivityException.class, () -> {
            emailAuthStrategy.authenticate(context);
        });
    }

    @Test
    void testGetPlatformName() {
        assertEquals("email", emailAuthStrategy.getPlatformName());
    }

    @Test
    void testSupportsRefreshToken() {
        assertFalse(emailAuthStrategy.supportsRefreshToken());
    }
}
