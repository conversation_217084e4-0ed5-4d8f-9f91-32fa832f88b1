package com.kikitrade.activity.controller;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.OSSObject;
import com.kikitrade.activity.api.RemoteActivityService;
import com.kikitrade.activity.api.model.ActivityRequest;
import com.kikitrade.activity.api.model.activity.ActivityInfoVO;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.param.ActivityBatchParam;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.facade.award.*;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.*;
import com.kikitrade.activity.service.common.CronUtil;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.service.importing.ActivityLauncher;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.job.ActivityRewardJob;
import com.kikitrade.activity.service.job.ElasticJobService;
import com.kikitrade.activity.service.job.MemberRankingJob;
import com.kikitrade.activity.service.model.ActivityEntityVO;
import com.kikitrade.activity.service.model.ActivityMaterialVO;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.framework.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

import static com.kikitrade.activity.model.Result.ResultCode.PARAM_INVALID;

@Controller
@Slf4j
@RequestMapping("/activity/batch")
public class ActivityBatchController {

    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityEntityService activityEntityService;
    @Resource
    private ElasticJobService elasticJobService;
    @Resource
    private CsvService rewardCsvService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private ActivityRewardJob activityRewardJob;
    @Resource
    private UploadOssUtil uploadOssUtil;
    @Resource
    private ActivityLauncher activityOfflineRewardLauncher;
    @Resource
    private ActivityMaterialService activityMaterialService;

    @Value("${batch.csv.path}")
    private String path;

    @Resource
    private RedisService redisService;
    @Resource
    private RemoteActivityService remoteActivityService;
    @Resource
    private CustomerService customerService;
    @Resource
    private ActivityTaskService activityTaskService;

    @RequestMapping(value = "activity/create", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> createActivity(@RequestBody ActivityEntityVO activityEntity) {
        log.info("create...{}", JSON.toJSONString(activityEntity));
        try {
            List<RewardRule> ruleList = new ArrayList<>();
            for(com.kikitrade.activity.service.model.RewardRule rule : JSON.parseArray(activityEntity.getRewardConfig(), com.kikitrade.activity.service.model.RewardRule.class)){
                RewardRule.Builder builder = RewardRule.newBuilder();
                if(rule.getLevel() != null){
                    builder.setLevel(rule.getLevel());
                }
                if(rule.getSide() != null){
                    builder.setSide(rule.getSide());
                }
                if(rule.getMin() != null){
                    builder.setMin(String.valueOf(rule.getMin()));
                }
                if(rule.getMax() != null){
                    builder.setMax(String.valueOf(rule.getMax()));
                }
                if(rule.getAward() != null){
                    builder.setAward(rule.getAward());
                }
                if(rule.getAwardAmount() != null){
                    builder.setAwardAmount(rule.getAwardAmount());
                }
                if(rule.getAwardType() != null){
                    builder.setAwardType(rule.getAwardType());
                }
                if(rule.getUserType() != null){
                    builder.setUserType(rule.getUserType());
                }
                ruleList.add(builder.build());
            }
            ActivityDTO activityDTO = ActivityDTO.newBuilder()
                    .setId(activityEntity.getId())
                    .setActivityName(activityEntity.getName())
                    .setType(ActivityTypeEnum.valueOf(activityEntity.getType()))
                    .setStartTime(activityEntity.getStartTime())
                    .setEndTime(activityEntity.getEndTime())
                    .setRemark(activityEntity.getRemark())
                    .setAutoCreateBatch(activityEntity.getAutoCreateBatch())
                    .setBatchFrequency(BatchFrequency.valueOf(activityEntity.getCycle()))
                    .setStatus(ActivityStatusEnum.forNumber(activityEntity.getStatus()))
                    .addAllRewardRule(ruleList)
                    .build();
            if (StringUtils.isBlank(activityEntity.getId())) {
                return activityEntityService.save(activityDTO, null, null, null);
            } else {
                return activityEntityService.updateCheckType(activityDTO);
            }
        } catch (Exception e) {
            log.error("create exception...{}", JSON.toJSONString(activityEntity), e);
            return new Result<>(false, "exception", e.getMessage());
        }
    }

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    public Result<ActivityBatch> create(@RequestBody ActivityBatch activityBatch) {
        log.info("create...{}", JSON.toJSONString(activityBatch));
        try {
            ActivityBatchDTO.Builder build = ActivityBatchDTO.newBuilder();
            if(activityBatch.getBatchId() != null){
                build.setId(activityBatch.getBatchId());
            }
            build.setActivityId(activityBatch.getActivityId());
            build.setActivityName(activityBatch.getActivityName());
            build.setBatchName(activityBatch.getName());
            if(activityBatch.getAmount() != null){
                build.setAmount(activityBatch.getAmount());
            }
            if(activityBatch.getCurrency() != null){
                build.setCurrency(activityBatch.getCurrency());
            }
            build.setRemark(activityBatch.getRemark());
            build.setScheduled(activityBatch.getScheduled());
            if(activityBatch.getScheduledTime() != null){
                build.setScheduledTime(activityBatch.getScheduledTime());
            }
            if(activityBatch.getSourceOssUrl() != null){
                build.setSourceOssUrl(activityBatch.getSourceOssUrl());
            }
            if(activityBatch.getRewardConfig() != null){

                List<RewardRule> ruleList = new ArrayList<>();
                for(com.kikitrade.activity.service.model.RewardRule rule : JSON.parseArray(activityBatch.getRewardConfig(), com.kikitrade.activity.service.model.RewardRule.class)){
                    RewardRule.Builder builder = RewardRule.newBuilder();
                    if(rule.getLevel() != null){
                        builder.setLevel(rule.getLevel());
                    }
                    if(rule.getSide() != null){
                        builder.setSide(rule.getSide());
                    }
                    if(rule.getMin() != null){
                        builder.setMin(String.valueOf(rule.getMin()));
                    }
                    if(rule.getMax() != null){
                        builder.setMax(String.valueOf(rule.getMax()));
                    }
                    if(rule.getAward() != null){
                        builder.setAward(rule.getAward());
                    }
                    if(rule.getAwardAmount() != null){
                        builder.setAwardAmount(rule.getAwardAmount());
                    }
                    if(rule.getAwardType() != null){
                        builder.setAwardType(rule.getAwardType());
                    }
                    if(rule.getUserType() != null){
                        builder.setUserType(rule.getUserType());
                    }
                    ruleList.add(builder.build());
                }
                build.addAllRewardRule(ruleList);
            }
            return activityBatchNewService.saveOrUpdate(build.build());
        } catch (Exception e) {
            log.error("create exception...{}", JSON.toJSONString(activityBatch), e);
            return new Result<>(false, String.format("%s,%s", "创建失败", e.getMessage()));
        }
    }

    @RequestMapping(value = "/get", method = RequestMethod.GET)
    @ResponseBody
    public ActivityBatch get(String batchId) {
        try {
            return activityBatchNewService.findByBatchId(batchId);
        } catch (Exception e) {
            log.error("create exception...{}", JSON.toJSONString(batchId), e);
            return null;
        }
    }

    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @ResponseBody
    public List<ActivityBatch> getList(String batchId, int pageNo, int pageSize) {
        try {
            return activityBatchNewService.findForPage(ActivityBatchParam.builder().batchId(batchId).build(), pageNo, pageSize).getRows();
        } catch (Exception e) {
            log.error("create exception...{}", JSON.toJSONString(batchId), e);
            return null;
        }
    }

    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @ResponseBody
    public String audit(String batchId, String type) {
        log.info("audit...{}", batchId);
        try {
            activityBatchNewService.audit(batchId, "1".equals(type) ? ActivityConstant.AuditTypeEnum.APPROVE : ActivityConstant.AuditTypeEnum.REJECT);
        } catch (Exception e) {
            log.error("reward exception...{}", batchId, e);
            return "发奖异常";
        }
        return "发奖就绪";
    }

    @RequestMapping(value = "/uploadOss", method = RequestMethod.POST)
    @ResponseBody
    public void uploadOss(String batchId, String status) {
        try {
            ActivityRewardPageParam pageParam = new ActivityRewardPageParam();
            pageParam.setBatchId(batchId);
            ActivityBatch batch = activityBatchNewService.findByBatchId(batchId);
            pageParam.setPageNo(0);
            pageParam.setActivityType(batch.getActivityType());
            if(status != null){
                pageParam.setStatusList(Collections.singletonList(status));
            }
            rewardCsvService.write(String.format("%s-%s.%s", batch.getName(), batch.getBatchId(), "csv"), pageParam);
        } catch (Exception e) {
            log.error("create exception...", e);
        }
    }


    @RequestMapping(value = "/trigger", method = RequestMethod.GET)
    @ResponseBody
    public void triggerJob(String jobName) {
        try {
            elasticJobService.removeJob(jobName);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @RequestMapping(value = "/deleteReward", method = RequestMethod.GET)
    @ResponseBody
    public void deleteReward(String id) {
        try {
            activityCustomRewardStoreBuilder.delete(Arrays.asList(id));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @RequestMapping(value = "/retryReward", method = RequestMethod.GET)
    @ResponseBody
    public void retryReward(String batchId) {
        try {
            activityBatchNewService.updateBatchStatus(batchId, ActivityConstant.BatchRewardStatusEnum.AWARDING.name());
            ActivityBatch batch = activityBatchNewService.findByBatchId(batchId);
            Map<String, String> param = new HashMap<String, String>() {{
                put("activityId", batch.getActivityId());
                put("batchId", batch.getBatchId());
            }};
            elasticJobService.createJob(activityRewardJob, elasticJobService.getJobNameForReward(batch.getBatchId()), CronUtil.getCronForMinute(1), batch.getShardCount(), param);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @RequestMapping(value = "/uploadCsv", method = RequestMethod.POST)
    @ResponseBody
    public String upload(MultipartFile file) {
        File tempFile = new File(path + "/" + file.getOriginalFilename());
        try {
            file.transferTo(tempFile);
            uploadOssUtil.putObject(String.format("%s/%s", getOssPath(),tempFile.getName()), tempFile);
            return tempFile.getName();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @RequestMapping(value = "/getOss", method = RequestMethod.POST)
    @ResponseBody
    public void getOss(String bucketName, String path, String url) {
        try{
            OSSObject object = uploadOssUtil.getObject(path);
            System.out.println(object);
        }catch (Exception ex){
            ex.printStackTrace();
        }
        try {
            OSSObject object = uploadOssUtil.getObject(bucketName, path);
            System.out.println(object);
        }catch (Exception ex){
            ex.printStackTrace();
        }
        try {
            OSSObject object = uploadOssUtil.getObject(new URL(url));
            System.out.println(object);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/reward", method = RequestMethod.POST)
    @ResponseBody
    public void reward(String batchId) throws Exception{
        LauncherParameter domain = new LauncherParameter();
        domain.setBatch(activityBatchNewService.findByBatchId(batchId));
        activityOfflineRewardLauncher.run(domain);
    }

    @RequestMapping(value = "/lock", method = RequestMethod.POST)
    @ResponseBody
    public void lock(String key){
        try {
            String lock = redisService.lock(key, 5);
            System.out.println(lock);
            Thread.sleep(10 * 1000);
            String lock1 = redisService.lock(key, 5);
            System.out.println(lock1);
            redisService.unLock(lock);
            lock = redisService.lock(key, 5);
            System.out.println(lock);
            redisService.unLock(lock);
        }catch (Exception ex){
            log.error("ex",ex);
        }
    }

    private String getOssPath(){
        return String.format("reward/cvs/%s", TimeUtil.getDataStr(new Date(), TimeUtil.YYYYMM));
    }

    @RequestMapping(value = "saveMaterial", method = RequestMethod.POST)
    @ResponseBody
    public Result saveMaterial(@RequestBody ActivityMaterialVO activityMaterialVO){
        return activityMaterialService.save(activityMaterialVO);
    }

    @RequestMapping(value = "getActivityInfo", method = RequestMethod.GET)
    @ResponseBody
    public Result<ActivityInfoVO> activityInfoVO(ActivityRequest activityRequest){
        return remoteActivityService.getActivityInfo(activityRequest);
    }

    @RequestMapping(value = "getCustomer", method = RequestMethod.GET)
    @ResponseBody
    public CustomerCacheDTO getCustomer(String customerId){
        return customerService.getById(customerId);
    }

    @RequestMapping(value = "getReferral", method = RequestMethod.GET)
    @ResponseBody
    public String getReferral(String customerId){
        return customerService.getInviteInfo(customerId);
    }


    @RequestMapping(value = "rewards", method = RequestMethod.GET)
    @ResponseBody
    public PageResult rewards(String customerId){
        return remoteActivityService.inviteRewards("", customerId, 0, 10);
    }

    @RequestMapping(value = "rewardSum", method = RequestMethod.GET)
    @ResponseBody
    public BigDecimal rewardSum(String customerId){
        return remoteActivityService.inviteRewardSum("", customerId);
    }


    /**
     * curl  http://127.0.0.1:8080/activity/batch/batchCreateRedeem -X POST -d "taskId=403010&codeListStr=ayle,ayle_test"
     *
     * @return
     */
    @RequestMapping(value = "/batchCreateRedeem", method = RequestMethod.POST)
    @ResponseBody
    public String batchCreateRedeem(@RequestParam String taskId, @RequestParam String codeListStr) {
        log.info("batchAddRedeem begin, list:{}", codeListStr);
        if(StringUtils.isEmpty(codeListStr)){
            return "codeListStr 不允许为空";
        }
        try {
            codeListStr = codeListStr.toLowerCase();
            List<String> codeList = Arrays.asList(codeListStr.trim().split(","));

            final String reg = "^[0-9a-zA-Z_]+$";
            for (String code : codeList) {
                if(!code.matches(reg)){
                    return "code:" + code + " 不合法, 操作未执行";
                }
            }

            Result<List<String>> result = activityTaskService.batchCreateRedeemByTask(codeList, "mugen", taskId, "redeem");
            log.info("batchAddRedeem end, result:{}", result);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            log.error("batchAddRedeem failed:", e);
            return "Exception!";
        }
    }


    /**
     * curl  http://127.0.0.1:8080/activity/batch/batchCreateRedeemDetail -X POST -d "taskId=407017&codeListStr=loa_test4&saasId=loa_op&businessType=redeem-simple&awardValue=1000"
     *
     * @return
     */
    @RequestMapping(value = "/batchCreateRedeemDetail", method = RequestMethod.POST)
    @ResponseBody
    public String batchCreateRedeem(@RequestParam String taskId, @RequestParam String codeListStr,
                                    @RequestParam String saasId, @RequestParam String businessType, @RequestParam String awardValue) {
        log.info("batchAddRedeem begin, list:{}", codeListStr);
        if(StringUtils.isEmpty(codeListStr) || StringUtils.isEmpty(saasId) || StringUtils.isEmpty(taskId) || StringUtils.isEmpty(awardValue)){
            return "入参不允许为空";
        }
        if( !NumberUtil.isNumber(awardValue)){
            return "awardValue必须为数字";
        }
        try {
            codeListStr = codeListStr.toLowerCase();
            List<String> codeList = Arrays.asList(codeListStr.trim().split(","));

            final String reg = "^[0-9a-zA-Z_]+$";
            for (String code : codeList) {
                if(!code.matches(reg)){
                    return "code:" + code + " 不合法, 操作未执行";
                }
            }

            Result<List<String>> result = activityTaskService.batchCreateRedeem(codeList, saasId, taskId, businessType, awardValue);
            log.info("batchAddRedeem end, result:{}", result);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            log.error("batchAddRedeem failed:", e);
            return "Exception!";
        }
    }
}
