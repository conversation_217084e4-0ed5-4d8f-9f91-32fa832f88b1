package com.kikitrade.activity.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendCallback;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.mysql.model.SchedLog;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityUserTotalDataBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityUserTotalData;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.model.ActivityEventMassage;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.model.FiatDepositActivityConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.auth.AuthService;
import com.kikitrade.activity.service.business.ActivityService;
import com.kikitrade.activity.service.business.KolExtraRewardService;
import com.kikitrade.activity.service.business.SchedLogService;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.job.BreadMemberRankingJob;
import com.kikitrade.activity.service.job.MemberRankingJob;
import com.kikitrade.activity.service.job.ZeekMemberRankingJob;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.mq.TopicConfig;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.model.EventDTO;
import cn.hutool.core.util.RandomUtil;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.remoting.TimeoutException;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Controller
@Slf4j
@RequestMapping("/activity")
public class SchedulerController {

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityUserTotalDataBuilder activityUserTotalDataBuilder;

    @Resource
    private OnsProducer onsProducer;

    @Resource
    private TopicConfig topicConfig;

    @Resource
    private ActivityRecordsBuilder activityRecordsBuilder;

    @Resource
    private SchedLogService schedLogService;

    @Resource
    private KolExtraRewardService kolExtraRewardService;

    @Resource
    private ReleaseService releaseService;

    @Resource
    private AuthService authService;

    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;

    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;

    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;

    @Resource
    private RedisService redisService;

    @Resource
    private ThreePlatformProperties threePlatformProperties;

    @Resource
    private EventClient eventClient;


    /**
     * curl  http://127.0.0.1:8050/activity/create  -H "Content-type: application/json" -X  POST -d "{\"actionConfig\":[{\"action_id\":22,\"params\":\"\"}],\"apply_times\":1,\"content\":\"法币累计入金活动（新））\",\"created\":1611037608591,\"end_time\":1611901608591,\"execute_type\":1,\"is_need_audit\":1,\"is_push\":1,\"modified\":1611037608591,\"name\":\"法币累计入金活动（新）\",\"publish_user_id\":\"1\",\"ruleConfig\":[{\"params\":\"{\\\"rewardType\\\":1,\\\"rewardTimes\\\":1,\\\"configList\\\":[{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":5000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":50},{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":10000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":100}],\\\"frequency\\\":1}\",\"rule_id\":22}],\"saas_id\":\"kiki\",\"start_time\":1611037608591,\"status\":0,\"top_order\":1,\"type\":22}"
     *
     * @param activity
     * @return
     */
    @RequestMapping("/create")
    @ResponseBody
    public Boolean create(@RequestBody Activity activity) {
        log.info("create...{}", JSON.toJSONString(activity));
        try {
            activity.setSaas_id(activity.getSaas_id());
            activityService.save(activity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/edit  -H "Content-type: application/json" -X  POST -d "{\"actionConfig\":[{\"action_id\":22,\"params\":\"\"}],\"apply_times\":1,\"content\":\"法币累计入金活动（新））\",\"created\":1611037608591,\"end_time\":1611901608591,\"execute_type\":1,\"is_need_audit\":1,\"is_push\":1,\"modified\":1611037608591,\"name\":\"法币累计入金活动（新）\",\"publish_user_id\":\"1\",\"ruleConfig\":[{\"params\":\"{\\\"rewardType\\\":1,\\\"rewardTimes\\\":1,\\\"configList\\\":[{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":5000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":50},{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":10000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":100}],\\\"frequency\\\":1}\",\"rule_id\":22}],\"saas_id\":\"kiki\",\"start_time\":1611037608591,\"status\":2,\"top_order\":1,\"type\":22,\"id\":366}"
     * curl  http://127.0.0.1:8050/activity/edit  -H "Content-type: application/json" -X  POST -d "{\"actionConfig\":[{\"action_id\":21,\"params\":\"\"}],\"apply_times\":1,\"content\":\"法币单笔入金活动（新））\",\"created\":1611037608591,\"end_time\":1611901608591,\"execute_type\":1,\"is_need_audit\":1,\"is_push\":1,\"modified\":1611037608591,\"name\":\"法币单笔入金活动（新）\",\"publish_user_id\":\"1\",\"ruleConfig\":[{\"params\":\"{\\\"rewardType\\\":1,\\\"rewardTimes\\\":1,\\\"configList\\\":[{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":1000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":10},{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":2000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":20}],\\\"frequency\\\":1}\",\"rule_id\":21}],\"saas_id\":\"kiki\",\"start_time\":1611037608591,\"status\":2,\"top_order\":1,\"type\":21,\"id\":365}"
     *
     * @param activity
     * @return
     */
    @RequestMapping("/edit")
    @ResponseBody
    public Boolean edit(@RequestBody Activity activity) {
        log.info("edit...{}", JSON.toJSONString(activity));
        try {
            activity.setSaas_id(activity.getSaas_id());
            activityService.update(activity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/update/status -X POST -d "id=365&status=1"
     *
     * @param id
     * @param status
     * @return
     */
    @RequestMapping("/update/status")
    @ResponseBody
    public Boolean update(@RequestParam Integer id,
                          @RequestParam Integer status) {
        log.info("updateStatus...id[{}] status[{}]", id, status);
        try {
            activityService.updateStatus(id, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/detail?id=365
     *
     * @param id
     * @return
     */
    @RequestMapping("/detail")
    @ResponseBody
    public JsonResult detail(@RequestParam Integer id) {
        log.info("updateStatus...id[{}]", id);
        try {
            JsonResult result = activityService.findDetailById(id);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/delete -X POST -d "id=365"
     *
     * @param id
     * @return
     */
    @RequestMapping("/delete")
    @ResponseBody
    public JsonResult delete(@RequestParam Integer id) {
        log.info("updateStatus...id[{}] ", id);
        try {
            JsonResult result = activityService.delete(id, false);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/data/prepare -X POST -d "id=366&jobName=fiat_deposit_total_22"
     *
     * @param id
     * @return
     */
    @RequestMapping("/data/prepare")
    @ResponseBody
    public String dataPrepare(@RequestParam Integer id, String jobName) {
        log.info("dataPrepare...id[{}] ", id);
        try {

            JsonResult result = activityService.findDetailById(id);
            if (!result.getSuccess() || result.getObj() == null) {
                return "invalid activity!";
            }

            SchedLog schedLog = schedLogService.findByLatestBatch(jobName);
            if (schedLog == null) {
                return "max compute data process unready!";
            }

            Activity activity = (Activity) result.getObj();
            JSONObject object = JSONObject.parseObject(activity.getRuleConfig().get(0).getParams());
            List<FiatDepositActivityConfig> configList = object.getObject("configList", new TypeReference<List<FiatDepositActivityConfig>>() {
            });
            List<String> currencyList = configList.stream().map(f -> f.getFiatCurrency()).collect(Collectors.toList());

            boolean dealsuccess = currencyList.stream().map(currency -> {
                RangeResult<ActivityUserTotalData> records = activityUserTotalDataBuilder.list(schedLog.getBatch_pt(), id, currency, ActivityConstant.RecordStatus.RECORDED, null, 1);
                return CollectionUtils.isEmpty(records.list);
            }).anyMatch(f -> f == true);

            boolean rewardsuccess = currencyList.stream().map(currency -> {
                RangeResult<ActivityUserTotalData> records = activityUserTotalDataBuilder.list(schedLog.getBatch_pt(), id, currency, ActivityConstant.RecordStatus.COMPLETED, null, 1);
                return !CollectionUtils.isEmpty(records.list);
            }).anyMatch(f -> f == true);

            if (dealsuccess && rewardsuccess) {
                return "data prepare success!";
            }

            ActivityEventMassage massage = new ActivityEventMassage();
            massage.setId(id);
            massage.setAutoDataPrepare(false);
            massage.setType(ActivityConstant.RewardType.DATA_PROCESS.getCode());
            massage.setBatchNo(schedLog.getBatch_pt());
            massage.setJobName(jobName);
            ActivityResponse activityResponse = activityService.activityDataProcess(massage);
            if (activityResponse.isSuccess()) {
                return "data prepare processing!";
            }

        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
        return null;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/manual/reward -X POST -d "id=366"
     *
     * @param id
     * @return
     */
    @RequestMapping("/manual/reward")
    @ResponseBody
    public String manualReward(@RequestParam Integer id) {
        log.info("manualReward...id[{}] ", id);
        try {

            JsonResult result = activityService.findDetailById(id);
            if (!result.getSuccess() || result.getObj() == null) {
                return "invalid activity";
            }

            Activity activity = (Activity) result.getObj();
            long count = activityRecordsBuilder.countByActivityIdAndType(id, activity.getExecute_type(), new Date(), ActivityConstant.RecordStatus.RECORDED.getCode());
            if (count == 0) {
                return "manual reward success";
            }

            ActivityEventMassage massage = new ActivityEventMassage();
            massage.setId(id);
            massage.setType(ActivityConstant.RewardType.MANUAL_REWARD.getCode());
            ActivityResponse activityResponse = activityService.activityManualReward(massage);
            if (activityResponse.isSuccess()) {
                return "manual reward processing";
            }

        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
        return null;
    }

    /**
     * curl  http://127.0.0.1:8080/activity/reward/retry -X POST -d "statusRequest=NOT_AWARD%2CAWARD_FAILED&size=3"
     *
     * @return
     */
    @RequestMapping("/reward/retry")
    @ResponseBody
    public String rewardRetry(@RequestParam String statusRequest, @RequestParam Integer size) {
        log.info("[reward retry] begin...");
        try {
            List<String> supportStatusList = Arrays.asList("AWARD_FAILED", "NOT_AWARD");
            List<String> statusList = Arrays.asList(statusRequest.trim().split(","));
            for( String status : statusList ) {
                if(!supportStatusList.contains(status)) {
                    return "[reward retry] status {} not support" + status;
                }
            }
            ActivityRewardPageParam activityRewardPageParam = new ActivityRewardPageParam();
            activityRewardPageParam.setStatusList(statusList);
            for( int offset = 0; ; offset += size){
                Page<ActivityCustomReward> result = activityCustomRewardStoreBuilder.findAllByStatus(activityRewardPageParam, offset, size);
                if (result == null || CollectionUtils.isEmpty(result.getRows())) {
                    break;
                }
                log.info("[reward retry] for {} fail items begin...", result.getRows().size());

                for (ActivityCustomReward reward : result.getRows()) {
                    log.info("[reward retry] for rewardId:{} begin", reward.getBatchId() + "-" + reward.getCustomerId() + "-" + reward.getSeq());
                    LauncherParameter launcherParameter = new LauncherParameter();
                    launcherParameter.setActivityCustomReward(reward);
                    try {
                        activityRealTimeRewardTccService.reward(launcherParameter);
                        log.info("[reward retry] for rewardId:{} end", reward.getBatchId() + "-" + reward.getCustomerId() + "-" + reward.getSeq());
                    } catch (Exception e) {
                        log.error("reward error rewardId:{}",  reward.getBatchId() + "-" + reward.getCustomerId() + "-" + reward.getSeq(), e);
                    }
                }
            }

            return "[reward retry] all end.";
        } catch (Exception exception) {
            log.info("[reward retry] error", exception);
            return exception.getMessage();
        }
    }

    /**
     * curl  http://127.0.0.1:8050/activity/activityTest -X POST -d "{ActivityMessage 类型的 json 格式的测试数据}"
     *
     * @param activityMessage
     * @return
     */
    @RequestMapping("/activityRewardTest")
    @ResponseBody
    public boolean activityRewardTest(@RequestBody ActivityMessage activityMessage) {
        log.info("activityRewardTest... activityMessage={}", JSONObject.toJSONString(activityMessage));
        try {
            // 校验必须的参数
            if (activityMessage == null || !StringUtils.isNoneBlank(activityMessage.getCustomerId(), activityMessage.getBusinessId(), activityMessage.getSaasId())
                    || activityMessage.getEvent() == null) {
                log.error("activityRewardTest, necessary parameter is missing, return false");
                return false;
            }
            // 发送充值完成的消息，触发奖励活动
            onsProducer.asyncSend(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage), new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("activityRewardTest, testMqMsg send success!");
                }

                @Override
                public void onException(OnExceptionContext onExceptionContext) {
                    onsProducer.send(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage));
                    log.info("activityRewardTest, testMqMsg send success!");
                }
            });
            return true;
        } catch (Exception e) {
            log.error("activityRewardTest, execute exception!", e);
            return false;
        }
    }

    /**
     * 刷新 KOL 邀请额外奖励的配置
     *
     * @return
     */
    @RequestMapping(value = "/inviteFirstDeposit/refresh", method = RequestMethod.POST)
    @ResponseBody
    public String inviteFirstDepositRefresh(HttpServletRequest request) {
        try {
            String result = null;
            if (kolExtraRewardService.reload()) {
                result = ActivityExceptionType.EXECUTION_SUCCEED.getCode();
            } else {
                result = ActivityExceptionType.KOL_EXTRA_REWARD_CONFIG_RELOAD_FAILED.getCode();
            }
            log.info("inviteFirstDepositRefresh, result:{}", result);
            return result;
        } catch (Exception e) {
            log.error("inviteFirstDepositRefresh process fail", e);
            return "error";
        }
    }

    @RequestMapping(value = "airdrop/refund", method = RequestMethod.GET)
    @ResponseBody
    public String refund(String id) {
        return releaseService.refundById(id) ? "success" : "fail";
    }

    @RequestMapping(value = "rt", method = RequestMethod.GET)
    @ResponseBody
    public Token refreshToken(@RequestParam("cid") String customerId, @RequestParam("platform") String platform){
        return authService.getAuthToken("monster", customerId, platform);
    }

    /**
     * 根据平台获取authVersion配置
     */
    private String getAuthVersionForPlatform(String saasId, String platform) {
        try {
            OpenSocialPlatformEnum platformEnum = OpenSocialPlatformEnum.valueOf(platform);
            return switch (platformEnum) {
                case twitter -> threePlatformProperties.getTwitter().getAuthVersion() != null ?
                    threePlatformProperties.getTwitter().getAuthVersion().get(saasId) : null;
                case facebook -> threePlatformProperties.getFacebook().getAuthVersion() != null ?
                    threePlatformProperties.getFacebook().getAuthVersion().get(saasId) : null;
                case line -> threePlatformProperties.getLine().getAuthVersion() != null ?
                    threePlatformProperties.getLine().getAuthVersion().get(saasId) : null;
                default -> null;
            };
        } catch (IllegalArgumentException e) {
            log.warn("不支持的平台类型: {}", platform);
            return null;
        }
    }

    /**
     * 根据平台获取clientId配置
     */
    private String getClientIdForPlatform(String saasId, String platform) {
        try {
            OpenSocialPlatformEnum platformEnum = OpenSocialPlatformEnum.valueOf(platform);
            return switch (platformEnum) {
                case discord -> threePlatformProperties.getDiscord().getClientId() != null ?
                    threePlatformProperties.getDiscord().getClientId().get(saasId) : null;
                case twitter -> threePlatformProperties.getTwitter().getClientId() != null ?
                    threePlatformProperties.getTwitter().getClientId().get(saasId) : null;
                case google -> threePlatformProperties.getGoogle().getClientId() != null ?
                    threePlatformProperties.getGoogle().getClientId().get(saasId) : null;
                case facebook -> threePlatformProperties.getFacebook().getClientId() != null ?
                    threePlatformProperties.getFacebook().getClientId().get(saasId) : null;
                case line -> threePlatformProperties.getLine().getClientId() != null ?
                    threePlatformProperties.getLine().getClientId().get(saasId) : null;
                default -> null;
            };
        } catch (IllegalArgumentException e) {
            log.warn("不支持的平台类型: {}", platform);
            return null;
        }
    }

    /**
     * 发送解绑社交账号事件消息
     */
    private void sendUnbindSocialInfoEvent(String saasId, String customerId, String platform) {
        try {
            if (eventClient != null) {
                EventDTO eventDTO = new EventDTO();
                JSONObject eventBody = new JSONObject();
                eventBody.put("saasId", saasId);
                eventBody.put("platform", platform);

                eventDTO.setBody(eventBody);
                eventDTO.setCustomerId(customerId);
                eventDTO.setTime(new Date().getTime());
                eventDTO.setGlobalUid(RandomUtil.randomString(12) + "_" + customerId + "_" + platform + "_unbind");
                eventDTO.setName("unbindSocialInfo");
                eventClient.push(eventDTO);
                log.info("发送解绑社交账号事件消息成功: platform={}, customerId={}", platform, customerId);
            }
        } catch (Exception e) {
            log.error("发送解绑社交账号事件消息失败", e);
        }
    }

    /**
     * 解绑社交账号 (支持多平台)
     * curl --location 'localhost:8080/activity/unbind?saasId=xxx&cid=xxx&socialPlatform=xxx'
     * @param saasId
     * @param cid
     * @param socialPlatform
     * @return
     */
    @RequestMapping(value = "unbind", method = RequestMethod.GET)
    @ResponseBody
    public String unbind(@RequestParam("saasId") String saasId,
                         @RequestParam("cid") String cid,
                         @RequestParam(value = "socialPlatform", required = false) String socialPlatform){
        log.info("开始解绑社交账号: platform={}, saasId={}, cid={}", socialPlatform, saasId, cid);

        // 默认平台为twitter
        if (StringUtils.isBlank(socialPlatform)) {
            socialPlatform = OpenSocialPlatformEnum.twitter.name();
        }

        try {
            // 查找绑定的用户信息
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, cid);
            if (Objects.nonNull(customerBindDTO)) {
                String customerId = customerBindDTO.getUid();

                // 1. 解绑社交账号
                Boolean unbindResult = remoteCustomerBindService.unbindSocial(saasId, customerId, socialPlatform);
                log.info("解绑用户社交账号信息, platform={}, result={}", socialPlatform, unbindResult);

                // 2. 删除Redis缓存的认证信息
                String authVersion = threePlatformProperties.getTwitter().getAuthVersion().get(saasId);
                String clientId = getClientIdForPlatform(saasId, socialPlatform);

                Boolean cacheDelResult = false;
                if (authVersion != null && clientId != null) {
                    String cacheKey = String.format("%s:%s:%s", authVersion, customerId, clientId);
                    cacheDelResult = redisService.del(RedisKeyConst.ACTIVITY_AUTH_TOKEN.getKey(cacheKey));
                    log.info("删除用户缓存认证信息, platform={}, cacheKey={}, result={}", socialPlatform, cacheKey, cacheDelResult);
                } else {
                    log.warn("无法获取平台配置信息进行缓存清理: platform={}, authVersion={}, clientId={}", socialPlatform, authVersion, clientId);
                }

                // 3. 发送解绑事件MQ消息，通知其他项目删除绑定信息
                sendUnbindSocialInfoEvent(saasId, cid, socialPlatform);

                log.info("解绑社交账号完成: platform={}, customerId={}, unbindResult={}, cacheDelResult={}",
                    socialPlatform, customerId, unbindResult, cacheDelResult);
                return String.format("解绑%s账号成功", socialPlatform);

            } else {
                log.info("未找到绑定的用户信息: saasId={}, platform={}, cid={}", saasId, socialPlatform, cid);
                return String.format("未找到绑定的%s账号: %s", socialPlatform, cid);
            }
        } catch (Exception e) {
            log.error("解绑社交账号异常: platform={}, saasId={}, cid={}", socialPlatform, saasId, cid, e);
            return String.format("解绑%s账号失败: %s", socialPlatform, e.getMessage());
        }
    }

    @Resource
    @Lazy
    private MemberRankingJob memberRankingJob;
    @Resource
    @Lazy
    private BreadMemberRankingJob breadMemberRankingJob;
    @Resource
    @Lazy
    private ZeekMemberRankingJob zeekMemberRankingJob;


    /**
     * curl  http://127.0.0.1:8080/activity/rank "
     *
     * @return
     */
    @RequestMapping(value = "rank", method = RequestMethod.GET)
    @ResponseBody
    public String rank(){
        memberRankingJob.ex();
        return "success";
    }

    @RequestMapping(value = "bread/rank", method = RequestMethod.GET)
    @ResponseBody
    public String breadRank(){
        breadMemberRankingJob.ex();
        return "success";
    }

    @RequestMapping(value = "zeek/rank", method = RequestMethod.GET)
    @ResponseBody
    public String zeekRank(){
        zeekMemberRankingJob.ex();
        return "success";
    }

    @RequestMapping(value = "reset/auth", method = RequestMethod.GET)
    @ResponseBody
    public String resetAuth(){

        return "success";
    }
}


