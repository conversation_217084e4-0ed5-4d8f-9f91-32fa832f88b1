package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 资产兑换抽奖券请求
 * 支持两阶段抽奖系统，用户使用积分、金币等资产兑换抽奖券
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ExchangeTicketsRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 奖池编码（兼容字段）
     */
    private String prizePoolCode;

    /**
     * 兑换的资产类型
     * POINTS: 积分
     * COINS: 金币
     * DIAMONDS: 钻石
     * SHELL: 贝壳币（兼容）
     */
    private String assetType;

    /**
     * 兑换的资产数量
     */
    private Long assetAmount;

    /**
     * 兑换数量（兼容字段）
     */
    private Integer amount;

    /**
     * 目标抽奖券类型
     * NORMAL: 普通抽奖券
     * PREMIUM: 高级抽奖券
     * SPECIAL: 特殊抽奖券
     */
    private String ticketType;

    /**
     * 兑换的抽奖券数量
     */
    private Integer ticketCount;

    /**
     * 兑换场景
     * MANUAL: 手动兑换
     * AUTO: 自动兑换
     * ACTIVITY: 活动兑换
     */
    private String exchangeScene;

    /**
     * 客户端请求ID（用于幂等性控制）
     */
    private String requestId;
}