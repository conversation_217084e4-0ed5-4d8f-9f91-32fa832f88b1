package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询用户礼包请求
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class GetUserPackagesRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 状态过滤
     * GRANTED: 已授予（未领取）
     * CLAIMED: 已领取
     * EXPIRED: 已过期
     * REVOKED: 已撤销
     */
    private String status;

    /**
     * 礼包类型过滤
     * WELCOME: 新手礼包
     * DAILY: 每日礼包
     * WEEKLY: 每周礼包
     * MONTHLY: 每月礼包
     * ACTIVITY: 活动礼包
     * VIP: VIP礼包
     * ACHIEVEMENT: 成就礼包
     */
    private String packageType;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;
}
