package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 领取礼包响应
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class ClaimPackageResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 礼包ID
     */
    private String packageId;

    /**
     * 礼包名称
     */
    private String packageName;

    /**
     * 获得的奖品列表
     */
    private List<String> receivedPrizes;

    /**
     * 权益ID
     */
    private String entitlementId;

    /**
     * 领取时间
     */
    private Long claimTime;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;
}
