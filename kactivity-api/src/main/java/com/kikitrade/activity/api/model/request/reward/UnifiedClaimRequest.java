package com.kikitrade.activity.api.model.request.reward;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一领奖请求
 * 按照技术规格书要求设计的统一领奖接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class UnifiedClaimRequest implements Serializable {

    /**
     * 用户ID (通过Token获取，可选)
     */
    private String userId;

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 领奖凭证ID
     */
    private String claimId;

    /**
     * 客户端请求ID（用于幂等性控制）
     */
    private String requestId;

    /**
     * 领取渠道
     * APP: 移动应用
     * WEB: 网页
     * API: API调用
     */
    private String claimChannel;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 设备信息
     */
    private String deviceInfo;
}
