package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询用户礼包响应
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class GetUserPackagesResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 用户礼包列表
     */
    private List<UserPackageInfo> packages;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 用户礼包信息
     */
    @Data
    @Builder
    public static class UserPackageInfo implements Serializable {
        
        /**
         * 礼包ID
         */
        private String packageId;
        
        /**
         * 礼包名称
         */
        private String packageName;
        
        /**
         * 礼包类型
         */
        private String packageType;
        
        /**
         * 权益状态
         */
        private String status;
        
        /**
         * 授予时间
         */
        private Long grantTime;
        
        /**
         * 领取时间
         */
        private Long claimTime;
        
        /**
         * 过期时间
         */
        private Long expireTime;
        
        /**
         * 是否可以领取
         */
        private Boolean canClaim;
        
        /**
         * 奖品预览
         */
        private List<String> prizePreview;
        
        /**
         * 权益ID
         */
        private String entitlementId;
    }
}
