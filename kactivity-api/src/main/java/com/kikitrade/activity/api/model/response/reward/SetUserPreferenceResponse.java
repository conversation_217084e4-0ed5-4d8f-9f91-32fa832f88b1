package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 设置用户偏好响应
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class SetUserPreferenceResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 设置前的旧值
     */
    private String previousValue;

    /**
     * 偏好类型
     */
    private String preferenceType;

    /**
     * 设置的新值
     */
    private String preferenceValue;
}