package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.request.reward.*;
import com.kikitrade.activity.api.model.response.reward.*;

/**
 * 奖励中台服务接口
 * 提供新的奖励中台功能，与现有RemoteLotteryService并行运行
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface RewardPlatformService {

    // ==================== 用户端API ====================

    /**
     * 用户兑换抽奖券
     * POST /api/lottery/exchange-tickets
     */
    ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request);

    /**
     * 用户消耗抽奖券进行批量抽奖
     * POST /api/lottery/draw-batch
     */
    DrawBatchResponse drawBatch(DrawBatchRequest request);

    // ==================== 用户偏好管理API ====================

    /**
     * 设置用户偏好（通用接口，支持英雄选择、VIP等级等多种偏好类型）
     * POST /api/user/preference/set
     */
    SetUserPreferenceResponse setUserPreference(SetUserPreferenceRequest request);

    /**
     * 获取用户偏好
     * GET /api/user/preference/get
     */
    GetUserPreferenceResponse getUserPreference(GetUserPreferenceRequest request);

    // ==================== 礼包/宝箱系统API ====================

    /**
     * 领取礼包
     * POST /api/package/claim
     */
    ClaimPackageResponse claimPackage(ClaimPackageRequest request);

    /**
     * 查询用户礼包列表
     * GET /api/package/user-packages
     */
    GetUserPackagesResponse getUserPackages(GetUserPackagesRequest request);

    /**
     * 用户领取已解锁的宝箱
     * POST /api/lottery/chest/claim
     */
    ClaimChestResponse claimChest(ClaimChestRequest request);

    /**
     * 查询用户是否已领取过特定场景的一次性奖励
     * GET /api/rewards/claim-status
     */
    ClaimStatusResponse getClaimStatus(ClaimStatusRequest request);

    // ==================== 统一领奖系统API ====================

    /**
     * 统一领奖接口
     * POST /api/rewards/claim
     */
    UnifiedClaimResponse claimReward(UnifiedClaimRequest request);

    /**
     * 获取可领取奖励列表
     * GET /api/rewards/claimable
     */
    GetClaimableRewardsResponse getClaimableRewards(GetClaimableRewardsRequest request);

    /**
     * 获取用户抽奖状态
     * GET /api/lottery/state
     */
    LotteryStateResponse getLotteryState(LotteryStateRequest request);

    // ==================== 管理后台API ====================

    /**
     * 创建或更新奖池配置
     * POST /api/admin/pools
     */
    AdminPoolResponse createOrUpdatePool(AdminPoolRequest request);

    /**
     * 创建或更新奖池内的奖品
     * POST /api/admin/prizes
     */
    AdminPrizeResponse createOrUpdatePrize(AdminPrizeRequest request);

    /**
     * 实时查看奖品库存（直接从Redis读取）
     * GET /api/admin/stock/realtime
     */
    AdminStockResponse getRealtimeStock(AdminStockRequest request);

    /**
     * 手动触发系统重载所有配置到缓存中
     * POST /api/admin/cache/refresh
     */
    AdminCacheResponse refreshCache(AdminCacheRequest request);

    // ==================== 内部服务API (Dubbo) ====================

    /**
     * 由上游服务（如任务系统）为用户创建领奖资格
     */
    GrantPackResponse grantPackEntitlement(GrantPackRequest request);

    /**
     * 由外部业务系统调用，按ID为用户发放一个宝箱/礼包（直接发放，无需用户领取）
     */
    IssuePackResponse issuePack(IssuePackRequest request);

    /**
     * 由外部业务系统调用，直接为用户发放一组指定的奖励
     */
    IssueDirectResponse issueDirect(IssueDirectRequest request);

    /**
     * 由排行榜等上游服务调用，批量为用户发放排行榜奖励
     */
    IssueLeaderboardResponse issueLeaderboard(IssueLeaderboardRequest request);
}