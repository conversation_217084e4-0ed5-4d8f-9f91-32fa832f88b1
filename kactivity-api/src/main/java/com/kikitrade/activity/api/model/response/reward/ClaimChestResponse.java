package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 领取进度宝箱响应
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class ClaimChestResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 宝箱ID
     */
    private String chestId;

    /**
     * 宝箱名称
     */
    private String chestName;

    /**
     * 获得的奖品列表
     */
    private List<String> receivedPrizes;

    /**
     * 领取记录ID
     */
    private String claimId;

    /**
     * 领取时间
     */
    private Long claimTime;

    /**
     * 当前进度值
     */
    private Integer currentProgress;

    /**
     * 所需进度值
     */
    private Integer requiredProgress;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;
}
