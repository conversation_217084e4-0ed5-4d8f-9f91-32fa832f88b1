package com.kikitrade.activity.api.model.response.reward;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取用户抽奖状态响应
 * 返回用户在抽奖活动中的完整状态信息
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Builder
public class LotteryStateResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 用户基本信息
     */
    private UserBasicInfo userInfo;

    /**
     * 用户偏好设置
     */
    private UserPreferences preferences;

    /**
     * 抽奖券信息
     */
    private TicketInfo ticketInfo;

    /**
     * 奖池状态列表
     */
    private List<PoolStateInfo> poolStates;

    /**
     * 用户进度信息
     */
    private List<UserProgressInfo> progressInfo;

    /**
     * 可领取奖励信息
     */
    private List<ClaimableRewardInfo> claimableRewards;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 用户基本信息
     */
    @Data
    @Builder
    public static class UserBasicInfo implements Serializable {
        
        /**
         * 用户ID
         */
        private String userId;
        
        /**
         * 用户等级
         */
        private Integer userLevel;
        
        /**
         * VIP等级
         */
        private Integer vipLevel;
        
        /**
         * 总抽奖次数
         */
        private Long totalDrawCount;
        
        /**
         * 总中奖次数
         */
        private Integer totalWinCount;
        
        /**
         * 今日抽奖次数
         */
        private Integer todayDrawCount;
        
        /**
         * 连续抽奖天数
         */
        private Integer consecutiveDays;
    }

    /**
     * 用户偏好设置
     */
    @Data
    @Builder
    public static class UserPreferences implements Serializable {
        
        /**
         * 选择的英雄
         */
        private String selectedHero;
        
        /**
         * 偏好的奖池类型
         */
        private String preferredPoolType;
        
        /**
         * 其他偏好设置
         */
        private String otherPreferences;
    }

    /**
     * 抽奖券信息
     */
    @Data
    @Builder
    public static class TicketInfo implements Serializable {
        
        /**
         * 普通抽奖券数量
         */
        private Integer normalTickets;
        
        /**
         * 高级抽奖券数量
         */
        private Integer premiumTickets;
        
        /**
         * 特殊抽奖券数量
         */
        private Integer specialTickets;
        
        /**
         * 总抽奖券数量
         */
        private Integer totalTickets;
    }

    /**
     * 奖池状态信息
     */
    @Data
    @Builder
    public static class PoolStateInfo implements Serializable {
        
        /**
         * 奖池编码
         */
        private String poolCode;
        
        /**
         * 奖池名称
         */
        private String poolName;
        
        /**
         * 奖池类型
         */
        private String poolType;
        
        /**
         * 是否激活
         */
        private Boolean isActive;
        
        /**
         * 用户在此奖池的抽奖次数
         */
        private Integer userDrawCount;
        
        /**
         * 用户在此奖池的中奖次数
         */
        private Integer userWinCount;
        
        /**
         * 下次抽奖所需抽奖券数量
         */
        private Integer requiredTickets;
        
        /**
         * 奖池详细信息（可选）
         */
        private PoolDetails poolDetails;
    }

    /**
     * 奖池详细信息
     */
    @Data
    @Builder
    public static class PoolDetails implements Serializable {
        
        /**
         * 奖池描述
         */
        private String description;
        
        /**
         * 奖品预览
         */
        private List<String> prizePreview;
        
        /**
         * 开始时间
         */
        private Long startTime;
        
        /**
         * 结束时间
         */
        private Long endTime;
        
        /**
         * 最大抽奖次数限制
         */
        private Integer maxDrawLimit;
    }

    /**
     * 用户进度信息
     */
    @Data
    @Builder
    public static class UserProgressInfo implements Serializable {
        
        /**
         * 进度类型
         */
        private String progressType;
        
        /**
         * 当前进度值
         */
        private Integer currentProgress;
        
        /**
         * 目标进度值
         */
        private Integer targetProgress;
        
        /**
         * 进度百分比
         */
        private Double progressPercentage;
        
        /**
         * 关联的宝箱ID
         */
        private String associatedChestId;
        
        /**
         * 是否可以领取宝箱
         */
        private Boolean canClaimChest;
    }

    /**
     * 可领取奖励信息
     */
    @Data
    @Builder
    public static class ClaimableRewardInfo implements Serializable {
        
        /**
         * 凭证ID
         */
        private String claimId;
        
        /**
         * 奖励类型
         */
        private String rewardType;
        
        /**
         * 奖励名称
         */
        private String rewardName;
        
        /**
         * 奖励描述
         */
        private String rewardDescription;
        
        /**
         * 过期时间
         */
        private Long expireTime;
        
        /**
         * 是否即将过期
         */
        private Boolean expiringSoon;
    }
}
