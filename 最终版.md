# 奖励中台后端技术规格书 (最终版)

## 1. 系统概述

本文档为一份完整的后端技术规格说明书，旨在为开发一个功能全面、高内聚、低耦合的**奖励中台**提供清晰、详尽的实现指南。该系统作为平台能力，支持由多个外部业务渠道驱动，并集成了英雄动态奖池、多维度风控、多种激励机制和灵活的概率策略。

### 核心功能：

- **概率性抽奖**：
  - **英雄动态奖池**：支持根据用户存储的英雄选择，动态替换奖池中的专属奖品。
  - **双重概率策略**：支持“整体概率 (OVERALL)”和“单品概率 (SINGLE)”两种模式。
  - **两阶段抽奖**：提供独立的“资产兑换抽奖券”和“消耗抽奖券批量抽奖”接口，由前端编排调用。
- **组合奖励 (礼包/宝箱)**：支持配置“礼包”型奖品，内含多个固定奖励和随机奖励。可通过抽奖获得，也可由外部服务直接按ID触发发放。
- **统一领奖能力**：提供一个统一、抽象的领奖接口，用于核销由不同业务系统（如抽奖进度、任务系统）为用户生成的“领奖凭证”。
- **定向奖励发放**：提供安全的内部API，允许其他微服务直接指定奖励内容并调用本中台进行发放。
- **排行榜奖励发放**：支持由上游系统（如排行榜服务）定时调用，根据排名配置，通过消息队列异步为上榜用户发放奖励。
- **多维度抽奖风控**：可按日、周、月三个维度精确控制用户的抽奖次数上限。
- **后台管理支持**：提供独立的API接口，供管理后台配置活动和监控库存。

## 2. 系统架构与流程图

### 2.1 业务流程图

#### **A. 抽奖与进度奖励流程**

```
[游戏客户端]
    |
    | 1. 调用批量抽奖接口
    v
[奖励中台 API (/lottery/draw-batch)]
    |
    | 2. (内部) 执行抽奖 & 更新用户进度
    |
    | 3. (判断) 用户进度是否达到解锁宝箱条件？
    |    |
    |    +-----> 是 ----> [奖励中台 核心逻辑]
    |                       |
    |                       | 4. (内部调用) 创建一条“领奖凭证”
    |                       v
    |                    [user_claim_entitlement 表]
    v
[游戏客户端] <--- (轮询/推送) --- [奖励中台]
    |
    | 5. 发现有新的可领取奖励，显示“领取”按钮
    v
[奖励中台 API (/rewards/claim)]
    |
    | 6. 接收领奖请求 (含 claimId)
    v
[奖励中台 核心逻辑]
    |
    | 7. 核销凭证，发放宝箱奖励，更新凭证状态
    v
[数据库]
```

#### **B. 外部服务授予奖励流程**

```
[上游服务 (如任务系统)]
    |
    | 1. 业务完成，决定授予用户奖励
    v
[奖励中台 内部API (grantClaimEntitlement)]
    |
    | 2. 创建一条“领奖凭证”
    v
[user_claim_entitlement 表]
    |
    | 3. (后续流程同上)
    v
[游戏客户端] -> [奖励中台 API (/rewards/claim)] -> ...
```

## 3. 数据模型 (Data Model)

### 3.1 `prize_pool` - 奖池配置表

| **字段**               | **类型**     | **含义**                                          |
| ---------------------- | ------------ | ------------------------------------------------- |
| `id`                   | BIGINT       | 主键ID                                            |
| `code`                 | VARCHAR(50)  | 奖池唯一编码                                      |
| `name`                 | VARCHAR(100) | 奖池名称                                          |
| `exchange_rules`       | JSON         | 兑换规则 `[{"assetType": "POINTS", "cost": 100}]` |
| `probability_strategy` | VARCHAR(20)  | 概率策略 (`OVERALL` 或 `SINGLE`)                  |
| `fallback_prize_id`    | BIGINT       | 兜底奖品ID (`SINGLE`策略专用)                     |
| `daily_limit`          | INT          | 每日抽奖上限 (-1表示无限制)                       |
| `weekly_limit`         | INT          | 每周抽奖上限 (-1表示无限制)                       |
| `monthly_limit`        | INT          | 每月抽奖上限 (-1表示无限制)                       |
| `chest_cycle_days`     | INT          | 进度宝箱的刷新周期（天）                          |
| `status`               | VARCHAR(20)  | 状态 (`ACTIVE`, `INACTIVE`)                       |
| `start_time`           | DATETIME     | 活动开始时间                                      |
| `end_time`             | DATETIME     | 活动结束时间                                      |
| `create_time`          | DATETIME     | 创建时间                                          |
| `update_time`          | DATETIME     | 更新时间                                          |

### 3.2 `prize_config` - 奖品配置表

| **字段**              | **类型**       | **含义**                                      |
| --------------------- | -------------- | --------------------------------------------- |
| `id`                  | BIGINT         | 主键ID                                        |
| `prize_pool_code`     | VARCHAR(50)    | 所属奖池编码                                  |
| `hero_id`             | VARCHAR(100)   | 关联的英雄ID (`NULL`表示通用)                 |
| `prize_name`          | VARCHAR(100)   | 奖品名称                                      |
| `prize_type`          | VARCHAR(50)    | 奖品类型 (如 `ITEM`, `CURRENCY`, `GIFT_PACK`) |
| `prize_item_id`       | VARCHAR(100)   | 奖品/礼包ID                                   |
| `prize_icon`          | VARCHAR(255)   | 奖品图标URL                                   |
| `quantity_per_win`    | INT            | 每次中奖发放数量                              |
| `winning_probability` | DECIMAL(10, 8) | 中奖概率                                      |
| `stock_quantity`      | INT            | 库存数量 (-1 表示无限)                        |
| `is_active`           | TINYINT(1)     | 是否生效                                      |
| `create_time`         | DATETIME       | 创建时间                                      |
| `update_time`         | DATETIME       | 更新时间                                      |

### 3.3 `user_lottery_profile` - 用户抽奖档案表

| **字段**             | **类型** | **含义**       |
| -------------------- | -------- | -------------- |
| `id`                 | BIGINT   | 主键ID         |
| `user_id`            | BIGINT   | 用户ID (唯一)  |
| `total_draw_count`   | BIGINT   | 历史总抽奖次数 |
| `daily_draw_count`   | INT      | 今日已抽奖次数 |
| `weekly_draw_count`  | INT      | 本周已抽奖次数 |
| `monthly_draw_count` | INT      | 本月已抽奖次数 |
| `last_draw_time`     | DATETIME | 上次抽奖时间   |
| `create_time`        | DATETIME | 创建时间       |
| `update_time`        | DATETIME | 更新时间       |

### 3.4 `progress_chest_config` - 进度宝箱配置表

| **字段**            | **类型**     | **含义**                          |
| ------------------- | ------------ | --------------------------------- |
| `id`                | BIGINT       | 主键ID                            |
| `prize_pool_code`   | VARCHAR(50)  | 关联的奖池编码                    |
| `hero_id`           | VARCHAR(100) | 关联的英雄ID (`NULL`表示通用宝箱) |
| `chest_name`        | VARCHAR(100) | 宝箱名称                          |
| `unlock_progress`   | INT          | 解锁所需的进度值                  |
| `pack_id_on_unlock` | VARCHAR(100) | 解锁时对应的礼包ID                |
| `display_order`     | INT          | 显示顺序                          |
| `is_active`         | TINYINT(1)   | 是否启用                          |
| `create_time`       | DATETIME     | 创建时间                          |
| `update_time`       | DATETIME     | 更新时间                          |

### 3.5 `user_claim_entitlement` - 用户领奖凭证表 (核心)

| **字段**                | **类型**     | **含义**                                    |
| ----------------------- | ------------ | ------------------------------------------- |
| `id`                    | BIGINT       | 主键ID                                      |
| `claim_id`              | VARCHAR(255) | 领奖凭证唯一ID (UUID)                       |
| `user_id`               | BIGINT       | 用户ID                                      |
| `reward_type`           | VARCHAR(50)  | 奖励类型 (`PROGRESS_CHEST`, `GRANTED_PACK`) |
| `reward_source_id`      | VARCHAR(100) | 奖励来源ID (如 `chestId` 或 `packId`)       |
| `status`                | VARCHAR(20)  | 状态 (`UNCLAIMED`, `CLAIMED`)               |
| `source_channel`        | VARCHAR(50)  | 凭证来源渠道                                |
| `source_transaction_id` | VARCHAR(255) | 来源渠道的唯一交易ID (幂等键)               |
| `create_time`           | DATETIME     | 创建时间                                    |
| `update_time`           | DATETIME     | 更新时间                                    |

### 3.6 `user_preference` - 用户偏好表 (新)

| **字段**           | **类型**                     | **含义**                      |
| ------------------ | ---------------------------- | ----------------------------- |
| `id`               | BIGINT                       | 主键ID                        |
| `user_id`          | BIGINT                       | 用户ID                        |
| `preference_type`  | VARCHAR(50)                  | 偏好类型 (如 `SELECTED_HERO`) |
| `preference_value` | VARCHAR(255)                 | 偏好值 (如 `HERO_A_001`)      |
| `update_time`      | DATETIME                     | 更新时间                      |
| **PK**             | `(user_id, preference_type)` | 复合主键                      |

### 3.7 `draw_history` - 抽奖历史记录表

| **字段**               | **类型**     | **含义**             |
| ---------------------- | ------------ | -------------------- |
| `id`                   | BIGINT       | 主键ID               |
| `user_id`              | BIGINT       | 用户ID               |
| `batch_transaction_id` | VARCHAR(255) | 批量抽奖的唯一交易ID |
| `prize_pool_code`      | VARCHAR(50)  | 抽奖时所在的奖池     |
| `prize_id`             | BIGINT       | 中奖的奖品ID         |
| `prize_name`           | VARCHAR(100) | 中奖的奖品名称       |
| `draw_time`            | DATETIME     | 抽奖时间             |

### 3.8 `gift_pack_config` - 礼包内容规则表

| **字段**         | **类型**     | **含义**                                                   |
| ---------------- | ------------ | ---------------------------------------------------------- |
| `id`             | BIGINT       | 主键ID                                                     |
| `pack_id`        | VARCHAR(100) | 礼包ID (关联 `prize_config.prize_item_id`)                 |
| `rule_type`      | VARCHAR(20)  | 规则类型 (`FIXED_ITEM` 或 `RANDOM_POOL_PICK`)              |
| `item_id`        | VARCHAR(100) | 物品ID (仅当 `rule_type`='FIXED_ITEM')                     |
| `quantity_min`   | INT          | 最小数量                                                   |
| `quantity_max`   | INT          | 最大数量                                                   |
| `random_pool_id` | VARCHAR(100) | 随机池ID (仅当 `rule_type`='RANDOM_POOL_PICK')             |
| `pick_count`     | INT          | 从随机池中抽取的数量 (仅当 `rule_type`='RANDOM_POOL_PICK') |

### 3.9 `random_reward_pool` - 随机奖励池定义表

| **字段**       | **类型**     | **含义**                   |
| -------------- | ------------ | -------------------------- |
| `id`           | BIGINT       | 主键ID                     |
| `pool_id`      | VARCHAR(100) | 随机池ID                   |
| `item_id`      | VARCHAR(100) | 池内包含的物品ID           |
| `quantity_min` | INT          | 抽中该物品时获得的最小数量 |
| `quantity_max` | INT          | 抽中该物品时获得的最大数量 |
| `weight`       | INT          | 抽中该物品的权重           |

### 3.10 `direct_reward_issuance_log` - 定向奖励发放流水表

| **字段**          | **类型**     | **含义**                      |
| ----------------- | ------------ | ----------------------------- |
| `id`              | BIGINT       | 主键ID                        |
| `user_id`         | BIGINT       | 目标用户ID                    |
| `rewards_content` | JSON         | 发放的奖励内容                |
| `channel`         | VARCHAR(50)  | 来源渠道/场景Code             |
| `transaction_id`  | VARCHAR(255) | 外部渠道的唯一交易ID (幂等键) |
| `create_time`     | DATETIME     | 创建时间                      |

### 3.11 `leaderboard_reward_config` - 排行榜奖励配置表

| **字段**           | **类型**     | **含义**                          |
| ------------------ | ------------ | --------------------------------- |
| `id`               | BIGINT       | 主键ID                            |
| `leaderboard_type` | VARCHAR(50)  | 排行榜类型 (如 `DAILY`, `WEEKLY`) |
| `rank_start`       | INT          | 排名起始（包含）                  |
| `rank_end`         | INT          | 排名结束（包含）                  |
| `rewards`          | JSON         | 奖励内容                          |
| `description`      | VARCHAR(255) | 描述                              |

## 4. 核心业务逻辑

### 4.1 批量抽奖与进度处理

1. **抽奖主流程**：
   - **获取用户偏好**：在构建奖池前，通过内部接口查询 `user_preference` 表，获取用户 `SELECTED_HERO` 类型的偏好值。
2. **更新用户进度**：在批量抽奖成功后，根据抽奖次数更新用户的进度值。
3. **检查并生成凭证**：检查更新后的进度值是否跨越了 `progress_chest_config` 中配置的阈值。对于新解锁的宝箱，调用内部服务 `grantClaimEntitlement`，在 `user_claim_entitlement` 表中**创建一条新的凭证记录**。

### 4.2 开启礼包逻辑

1. 根据礼包的 `pack_id` 查询 `gift_pack_config` 表中的所有规则。
2. 遍历规则：
   - 如果 `rule_type` 是 `FIXED_ITEM`，则在 `quantity_min` 和 `quantity_max` 之间随机生成一个数量，然后将该物品和随机数量加入待发放列表。
   - 如果 `rule_type` 是 `RANDOM_POOL_PICK`，则根据 `random_pool_id` 查询 `random_reward_pool` 表，按权重随机抽取 `pick_count` 个不重复的物品。对于抽中的每一个物品，再在其各自的 `quantity_min` 和 `quantity_max` 之间随机一个数量。然后将物品和其随机数量加入待发放列表。
3. 返回最终的奖励列表。

### 4.3 缓存策略

- **配置缓存**: `prize_pool`, `prize_config` 等不常变动的配置数据，在服务启动时或管理员更新配置后加载到Redis中。
- **库存缓存**: 有限库存奖品的 `stock_quantity` 必须在Redis中进行实时管理，使用 `DECRBY` 等原子操作扣减库存，并异步同步回数据库。
- **用户数据缓存**: `user_lottery_profile` 等用户频繁读写的数据可被缓存，以提升性能。

## 5. API 接口设计

### 5.1 用户端 API

#### `POST /api/lottery/exchange-tickets`

用户发起一次资产兑换抽奖券的操作。

- **Request Body**:

  ```
  {
    "prizePoolCode": "NEWBIE_POOL",
    "assetType": "POINTS",
    "amount": 1000
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "ticketsObtained": 10
    }
  }
  ```

- **Error Response (4xx)**:

  ```
  {
    "success": false,
    "errorCode": "INSUFFICIENT_ASSETS",
    "message": "资产不足"
  }
  ```

#### `POST /api/lottery/draw-batch`

用户消耗抽奖券进行批量抽奖。后端服务会自动获取用户已设置的英雄偏好来构建奖池。

- **Request Body**:

  ```
  {
    "prizePoolCode": "NEWBIE_POOL",
    "drawCount": 10
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "rewardsObtained": [
        {
          "itemId": "gold",
          "itemName": "金币",
          "quantity": 1500,
          "itemIcon": "url_to_gold_icon"
        },
        {
          "itemId": "exp_potion",
          "itemName": "经验药水",
          "quantity": 8,
          "itemIcon": "url_to_potion_icon"
        }
      ]
    }
  }
  ```

- **Error Response (4xx)**:

  ```
  {
    "success": false,
    "errorCode": "INSUFFICIENT_TICKETS",
    "message": "抽奖券不足"
  }
  ```

#### `POST /api/lottery/select-preference`

用户设置通用偏好，如选择英雄。

- **Request Body**:

  ```
  {
    "preferenceType": "SELECTED_HERO",
    "preferenceValue": "HERO_A_001"
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "message": "偏好设置成功"
  }
  ```

#### `GET /api/lottery/state`

获取用户在抽奖活动中的所有状态。

- **Request Parameters**:

  - `prizePoolCode` (string, required): 需要查询状态的奖池编码。

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "prizePool": {
        "code": "NEWBIE_POOL",
        "name": "新手奖池",
        "exchangeRules": [
          { "assetType": "POINTS", "cost": 100 }
        ],
        "prizes": [
          {
            "prizeName": "金币",
            "prizeIcon": "url_to_gold_icon",
            "prizeType": "CURRENCY",
            "heroId": null
          },
          {
            "prizeName": "英雄A皮肤",
            "prizeIcon": "url_to_skin_icon",
            "prizeType": "ITEM",
            "heroId": "HERO_A_001"
          }
        ]
      },
      "userProfile": {
        "preferences": {
          "SELECTED_HERO": "HERO_A_001"
        },
        "daily": { "used": 5, "limit": 10 },
        "weekly": { "used": 20, "limit": 50 },
        "monthly": { "used": 20, "limit": -1 }
      },
      "claimableRewards": [
        {
          "claimId": "a7b1c3d4-e5f6-7890-1234-567890abcdef",
          "rewardType": "PROGRESS_CHEST",
          "rewardName": "进度宝箱1 (10/20)",
          "rewardIcon": "url_to_chest_icon"
        },
        {
          "claimId": "b8c2d4e5-f6a7-8901-2345-67890abcdef1",
          "rewardType": "GRANTED_PACK",
          "rewardName": "任务奖励礼包",
          "rewardIcon": "url_to_pack_icon"
        }
      ]
    }
  }
  ```

- **核心逻辑**: `userProfile` 部分返回一个通用的 `preferences` 对象。`claimableRewards` 数组的数据来源于 `user_claim_entitlement` 表中该用户所有 `status` 为 `UNCLAIMED` 的记录。

#### `POST /api/rewards/claim`

用户领取一个已解锁的奖励。

- **Request Body**:

  ```
  {
    "claimId": "a7b1c3d4-e5f6-7890-1234-567890abcdef"
  }
  ```

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "rewards": [
        { "itemId": "gold", "itemName": "金币", "quantity": 2000, "itemIcon": "url_to_gold_icon" }
      ]
    }
  }
  ```

- **Error Response (4xx)**:

  ```
  {
    "success": false,
    "errorCode": "INVALID_CLAIM_ID",
    "message": "无效的领取凭证或已被领取"
  }
  ```

#### `GET /api/leaderboards/rewards`

查询指定排行榜的奖励配置，用于前端展示。

- **Request Parameters**:

  - `leaderboardType` (string, required): 需要查询的排行榜类型，例如 "WEEKLY"。

- **Success Response (200 OK)**:

  ```
  {
    "success": true,
    "data": {
      "leaderboardType": "WEEKLY",
      "rewardTiers": [
        {
          "rankStart": 1,
          "rankEnd": 1,
          "rankDisplay": "第 1 名",
          "rewards": [
            { "itemId": "diamond_1000", "itemName": "钻石", "quantity": 1000, "itemIcon": "url_to_diamond" }
          ]
        },
        {
          "rankStart": 2,
          "rankEnd": 3,
          "rankDisplay": "第 2-3 名",
          "rewards": [
            { "itemId": "gold_50000", "itemName": "金币", "quantity": 50000, "itemIcon": "url_to_gold" }
          ]
        }
      ]
    }
  }
  ```

### 5.2 管理后台 API

#### `POST /api/admin/pools`

创建或更新一个奖池配置。

#### `POST /api/admin/prizes`

创建或更新奖池内的奖品。

#### `GET /api/admin/stock/realtime`

实时查看奖品库存（直接从Redis读取）。

#### `POST /api/admin/cache/refresh`

手动触发系统重载所有配置到缓存中。

### 5.3 内部服务 API (Dubbo)

#### `setUserPreference(SetUserPreferenceRequest request)`

由本服务的用户端API调用，或由其他内部服务调用，用于设置用户偏好。

- **Request DTO**:

  ```
  public class SetUserPreferenceRequest implements Serializable {
      private String userId;
      private String preferenceType;
      private String preferenceValue;
  }
  ```

#### `getUserPreference(GetUserPreferenceRequest request)`

获取用户指定的偏好设置。

- **Request DTO**:

  ```
  public class GetUserPreferenceRequest implements Serializable {
      private String userId;
      private String preferenceType;
  }
  ```

#### `grantClaimEntitlement(GrantEntitlementRequest request)`

由上游服务（如任务系统）为用户创建领奖凭证。

- **Request DTO**:

  ```
  public class GrantEntitlementRequest implements Serializable {
      private String userId;
      private String rewardType;
      private String rewardSourceId;
      private String sourceChannel;
      private String sourceTransactionId; // 用于幂等性
  }
  ```

#### `issuePack(IssuePackRequest request)`

由外部业务系统调用，按ID为用户发放一个宝箱/礼包（直接发放，无需用户领取）。

- **Request DTO**:

  ```
  public class IssuePackRequest implements Serializable {
      private String userId;
      private String packId;
      private String channel;
      private String transactionId;
      private String description;
  }
  ```

#### `issueDirect(IssueDirectRequest request)`

由外部业务系统调用，直接为用户发放一组指定的奖励。

- **Request DTO**:

  ```
  public class IssueDirectRequest implements Serializable {
      private String userId;
      private List<RewardItem> rewards;
      private String channel;
      private String transactionId;
      private String description;
  }
  ```

#### `issueLeaderboard(IssueLeaderboardRequest request)`

由排行榜等上游服务调用，批量为用户发放排行榜奖励。

- **Request DTO**:

  ```
  public class IssueLeaderboardRequest implements Serializable {
      private String leaderboardType;
      private String cycleId;
      private List<UserRank> rankings;
  }
  ```